#调度器名称，必须配置
org.quartz.scheduler.instanceName = AlertVerifyScheduler
org.quartz.scheduler.instanceId = AUTO

#调度器使用的线程管理类，一般使用SimpleThreadPool即可
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
#线程数，一般不超过10为佳，如果是负载较重的调度器则可适当增加数值，但最大不应超过50
org.quartz.threadPool.threadCount = 5

#未按时执行的调度任务退后执行的时间，单位毫秒
org.quartz.jobStore.misfireThreshold = 30000

#插件配置，通常无需修改
org.quartz.plugin.shutdownHook.class = org.quartz.plugins.management.ShutdownHookPlugin
org.quartz.plugin.shutdownHook.cleanShutdown = true
