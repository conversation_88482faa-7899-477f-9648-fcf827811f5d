import argparse
import json


############################
## 验证程序
############################
def verify(data):
    pass


############################
## 销毁程序
############################
def destory(data_path):
    pass

############################
## 从文件中解析数据
############################
def parse_data(data_path):
    '''
    解析数据
    @param data_path: 文件路径
    '''
    with open(data_path, 'r', encoding='utf-8') as json_file: 
        json_data = json.dumps(json_file)
    return json_data

############################
## 主方法入口
############################
def main(data_path):
    '''
    主方法
    @param data_path: 数据路径
    '''
    data = parse_data(data_path)
    verify(data)
    destory(data_path)


if __name__=='__main__':
    parser = argparse.ArgumentParser(description='Run your customized verify bean!')
    parser.add_argument('--data_path', required=True, help='Data to be verified!')
    args = parser.parse_args()
    main(args.data_path)
    


