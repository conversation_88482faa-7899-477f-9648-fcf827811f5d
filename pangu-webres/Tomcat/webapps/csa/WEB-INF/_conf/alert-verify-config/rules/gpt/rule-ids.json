[
  {
    "id": "ids",
    "switchBeanName": "eventTypeSwitch",
    "flowTo": [
      {
        "id": "_malware_",
        "condition": "#swiRes.startsWith('/malware')",
        "verifyBeanName": "passAction",
        "nextId": "ids-malware-applicationProtocolSwitch"
      },
      {
        "id": "_att_xss_",
        "condition": "#swiRes.startsWith('/att/xss')",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-xss"
      },
      {
        "id": "_att_exec_",
        "condition": "#swiRes.startsWith('/att/file') || #swiRes.startsWith('/att/leakage') || #swiRes.startsWith('/att/code') || #swiRes.startsWith('/att/xml') || #swiRes.startsWith('/att/cmd')",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-exec"
      },
      {
        "id": "_att_",
        "condition": "#swiRes.startsWith('/att')",
        "verifyBeanName": "passAction",
        "nextId": "ids-att"
      },
      {
        "id": "_spy_",
        "condition": "#swiRes.startsWith('/spy')",
        "verifyBeanName": "passAction",
        "nextId": "ids-spy"
      },
      {
        "id": "_acc_",
        "condition": "#swiRes.startsWith('/acc')",
        "verifyBeanName": "passAction",
        "nextId": "ids-acc"
      }
    ]
  },
  {
    "id": "ids-att-xss",
    "verifyBeanName": "XSSFalsePositiveVerify",
    "nextId": "none"
  },
  {
    "id": "ids-att-exec",
    "verifyBeanName": "injectFalsePositiveVerify",
    "nextId": "none"
  },
  {
    "id": "ids-att",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_ids-att-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-srcip"
      }
    ]
  },
  //源IP
  {
    "id": "ids-att-srcip",
    "verifyBeanName": "threatIntelligenceVerifyAction",
    "args": {
      "type": "ip",
      "source": "true"
    },
    "nextId": "ids-att-srcip-newIpVerify"
  },
  {
    "id": "ids-att-srcip-newIpVerify",
    "verifyBeanName": "newIpVerify",
    "args": {
      "source": "true"
    },
    "nextId": "ids-att-srcip-awdbVerifyAction"
  },
  {
    "id": "ids-att-srcip-awdbVerifyAction",
    "verifyBeanName": "awdbVerifyAction",
    "args": {
      "source": "true"
    },
    "nextId": "ids-att-srcip-locationFirstVisitVerify"
  },
  {
    "id": "ids-att-srcip-locationFirstVisitVerify",
    "verifyBeanName": "locationFirstVisitVerify",
    "nextId": "ids-att-srcip-honeyPotVerifyAction"
  },
  {
    "id": "ids-att-srcip-honeyPotVerifyAction",
    "verifyBeanName": "honeyPotVerifyAction",
    "nextId": "ids-att-srcip-innerFirstVisitVerify"
  },
  {
    "id": "ids-att-srcip-innerFirstVisitVerify",
    "verifyBeanName": "innerFirstVisitVerify",
    "nextId": "ids-att-srcip-netSegmentFirstVisitDstipDstportVerify"
  },
  {
    "id": "ids-att-srcip-netSegmentFirstVisitDstipDstportVerify",
    "verifyBeanName": "netSegmentFirstVisitDstipDstportVerify",
    "nextId": "ids-att-srcip-regionVisitRelationVerify"
  },
  {
    "id": "ids-att-srcip-regionVisitRelationVerify",
    "verifyBeanName": "regionVisitRelationVerify",
    "nextId": "ids-att-attTypeCountVerifyAction"
  },
  {
    "id": "ids-att-attTypeCountVerifyAction",
    "verifyBeanName": "attTypeCountVerifyAction",
    "nextId": "ids-att-corrRuleNotAlertVerify"
  },
  {
    "id": "ids-att-corrRuleNotAlertVerify",
    "verifyBeanName": "corrRuleNotAlertVerify",
    "nextId": "ids-att-anomalyBehaviorVerifyAction"
  },
  {
    "id": "ids-att-anomalyBehaviorVerifyAction",
    "verifyBeanName": "anomalyBehaviorVerifyAction",
    "nextId": "ids-att-assetManufactureVerify"
  },
  {
    "id": "ids-att-assetManufactureVerify",
    "verifyBeanName": "assetManufactureVerify",
    "nextId": "ids-att-assetMiddlewareVerify"
  },
  {
    "id": "ids-att-assetMiddlewareVerify",
    "verifyBeanName": "assetMiddlewareVerify",
    "nextId": "ids-att-eventtypeSwitch"
  },
  {
    "id": "ids-att-eventtypeSwitch",
    "switchBeanName": "eventTypeSwitch",
    "flowTo": [
      {
        "id": "_ids-att-pwd_",
        "condition": "#swiRes.contains('/pwd')",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-pwd"
      },
      {
        "id": "_ids-att-threat_",
        "condition": "#swiRes.contains('/threat')",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-threat"
      },
      {
        "id": "_ids-att-others_",
        "condition": "!#swiRes.contains('/pwd') && !#swiRes.contains('/threat')",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-others"
      }
    ]
  },
  {
    "id": "ids-att-threat",
    "verifyBeanName": "iOCFalsePositiveVerify",
    "nextId": "none"
  },
  {
    "id": "ids-att-pwd",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "_ids-att-pwd-rdp_",
        "condition": "#swiRes=='3389'",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-pwd-rdp"
      },
      {
        "id": "_ids-att-pwd-ssh_",
        "condition": "#swiRes=='22'",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-pwd-ssh"
      },
      {
        "id": "_ids-att-pwd-other_",
        "condition": "#swiRes!='22' && #swiRes!='3389'",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-pwd-other"
      }
    ]
  },
  {
    "id": "ids-att-pwd-rdp",
    "verifyBeanName": "rdpPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "ids-att-pwd-ssh",
    "verifyBeanName": "sshPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "ids-att-pwd-other",
    "verifyBeanName": "otherPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "ids-att-others",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "_ids-att-others-http_",
        "condition": "#swiRes=='12'",
        "verifyBeanName": "passAction",
        "nextId": "ids-att-others-http"
      },
      {
        "id": "_ids-att-notpwd-nothttp_",
        "condition": "#swiRes!='12'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      }
    ]
  },
  {
    "id": "ids-att-others-http",
    "verifyBeanName": "httpRequestVerify",
    "nextId": "ids-att-http-systemCommandVerifyAction"
  },
  {
    "id": "ids-att-http-systemCommandVerifyAction",
    "verifyBeanName": "systemCommandVerifyAction",
    "nextId": "ids-att-http-attPayloadVerifyAction"
  },
  {
    "id": "ids-att-http-attPayloadVerifyAction",
    "verifyBeanName": "attPayloadVerifyAction",
    "nextId": "ids-att-http-encodeCheckVerifyAction"
  },
  {
    "id": "ids-att-http-encodeCheckVerifyAction",
    "verifyBeanName": "encodeCheckVerifyAction",
    "nextId": "ids-att-http-infoLeakVerifyAction"
  },
  {
    "id": "ids-att-http-infoLeakVerifyAction",
    "verifyBeanName": "infoLeakVerifyAction",
    "nextId": "ids-att-http-commandResultVerifyAction"
  },
  {
    "id": "ids-att-http-commandResultVerifyAction",
    "verifyBeanName": "commandResultVerifyAction",
    "nextId": "none"
  },
  {
    "id": "ids-spy",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_ids-spy-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "ids-spy-eventTypeSwitch"
      }
    ]
  },
  {
    "id": "ids-spy-eventTypeSwitch",
    "switchBeanName": "eventTypeSwitch",
    "flowTo": [
      {
        "id": "spy-net",
        "condition": "#swiRes.contains('/net')",
        "verifyBeanName": "passAction",
        "nextId": "ids-spy-net"
      },
      {
        "id": "spy-port",
        "condition": "#swiRes.contains('/port')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "spy-vuln",
        "condition": "#swiRes.contains('/vuln')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "spy-tra",
        "condition": "#swiRes.contains('/tra')",
        "verifyBeanName": "passAction",
        "nextId": "ids-spy-tra"
      },
      {
        "id": "spy-other",
        "condition": "#swiRes.contains('/other')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      }
    ]
  },
  {
    "id": "ids-spy-net",
    "verifyBeanName": "ipAndPortScanVerify",
    "nextId": "none"
  },
  {
    "id": "ids-spy-tra",
    "verifyBeanName": "traVerify",
    "nextId": "none"
  },
  {
    "id": "ids-acc",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_ids-acc-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "ids-acc-directionSwitch"
      }
    ]
  },
  {
    "id": "ids-acc-directionSwitch",
    "switchBeanName": "directionSwitch",
    "flowTo": [
      {
        "id": "inner-to-inner",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "ids-acc-ii"
      },
      {
        "id": "inner-to-outer",
        "condition": "#swiRes=='2'",
        "verifyBeanName": "passAction",
        "nextId": "ids-acc-io"
      },
      {
        "id": "outer-to-inner",
        "condition": "#swiRes=='3'",
        "verifyBeanName": "passAction",
        "nextId": "ids-acc-oi"
      }
    ]
  },
  {
    "id": "ids-acc-ii",
    "verifyBeanName": "srcIpVerify",
    "nextId": "ids-acc-ii-netSegmentVerify"
  },
  {
    "id": "ids-acc-ii-netSegmentVerify",
    "verifyBeanName": "netSegmentVerify",
    "nextId": "ids-acc-ii-srcIpDstIpPortVerify"
  },
  {
    "id": "ids-acc-ii-srcIpDstIpPortVerify",
    "verifyBeanName": "srcIpDstIpPortVerify",
    "nextId": "ids-acc-ii-firstOccurVerify"
  },
  {
    "id": "ids-acc-ii-firstOccurVerify",
    "verifyBeanName": "firstOccurVerify",
    "nextId": "none"
  },
  {
    "id": "ids-acc-io",
    "verifyBeanName": "srcIpVerify",
    "nextId": "ids-acc-io-netSegmentVerify"
  },
  {
    "id": "ids-acc-io-netSegmentVerify",
    "verifyBeanName": "netSegmentVerify",
    "nextId": "ids-acc-io-firstOccurVerify"
  },
  {
    "id": "ids-acc-io-firstOccurVerify",
    "verifyBeanName": "firstOccurVerify",
    "nextId": "none"
  },
  {
    "id": "ids-acc-oi",
    "verifyBeanName": "threatIntelligenceVerifyAction",
    "args": {
      "type": "ip",
      "source": "true"
    },
    "nextId": "ids-acc-oi-newIpVerify"
  },
  {
    "id": "ids-acc-oi-newIpVerify",
    "verifyBeanName": "newIpVerify",
    "args": {
      "source": "true"
    },
    "nextId": "ids-acc-oi-rareIpVerify"
  },
  {
    "id": "ids-acc-oi-rareIpVerify",
    "verifyBeanName": "rareIpVerify",
    "args": {
      "source": "true"
    },
    "nextId": "ids-acc-oi-awdbVerifyAction"
  },
  {
    "id": "ids-acc-oi-awdbVerifyAction",
    "verifyBeanName": "awdbVerifyAction",
    "args": {
      "source": "true"
    },
    "nextId": "ids-acc-oi-locationFirstVisitVerify"
  },
  {
    "id": "ids-acc-oi-locationFirstVisitVerify",
    "verifyBeanName": "locationFirstVisitVerify",
    "nextId": "ids-acc-oi-honeyPotVerifyAction"
  },
  {
    "id": "ids-acc-oi-honeyPotVerifyAction",
    "verifyBeanName": "honeyPotVerifyAction",
    "nextId": "ids-acc-oi-attTypeCountVerifyAction"
  },
  {
    "id": "ids-acc-oi-attTypeCountVerifyAction",
    "verifyBeanName": "attTypeCountVerifyAction",
    "nextId": "ids-acc-oi-corrRuleNotAlertVerify"
  },
  {
    "id": "ids-acc-oi-corrRuleNotAlertVerify",
    "verifyBeanName": "corrRuleNotAlertVerify",
    "nextId": "ids-acc-oi-anomalyBehaviorVerifyAction"
  },
  {
    "id": "ids-acc-oi-anomalyBehaviorVerifyAction",
    "verifyBeanName": "anomalyBehaviorVerifyAction",
    "nextId": "ids-acc-oi-srcIpDstIpPortVerify"
  },
  {
    "id": "ids-acc-oi-srcIpDstIpPortVerify",
    "verifyBeanName": "srcIpDstIpPortVerify",
    "nextId": "none"
  },
  {
    "id": "ids-malware-applicationProtocolSwitch",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "DNS",
        "condition": "#swiRes=='192'",
        "verifyBeanName": "passAction",
        "nextId": "ids-malware-dns"
      },
      {
        "id": "applicationProtocolSwitch-other",
        "verifyBeanName": "passAction",
        "nextId": "ids-malware-other"
      }
    ]
  },
  {
    "id": "ids-malware-dns",
    "verifyBeanName": "tiCollisionVerify",
    "nextId": "ids-malware-dns-newMainDomainVerify"
  },
  {
    "id": "ids-malware-dns-newMainDomainVerify",
    "verifyBeanName": "newMainDomainVerify",
    "nextId": "ids-malware-dns-newQryDomainVerify"
  },
  {
    "id": "ids-malware-dns-newQryDomainVerify",
    "verifyBeanName": "newQryDomainVerify",
    "nextId": "ids-malware-dns-rareMainDomainVerify"
  },
  {
    "id": "ids-malware-dns-rareMainDomainVerify",
    "verifyBeanName": "rareMainDomainVerify",
    "nextId": "ids-malware-dns-rareQryDomainVerify"
  },
  {
    "id": "ids-malware-dns-rareQryDomainVerify",
    "verifyBeanName": "rareQryDomainVerify",
    "nextId": "ids-malware-dns-multiConnectedDomainVerify"
  },
  {
    "id": "ids-malware-dns-multiConnectedDomainVerify",
    "verifyBeanName": "multiConnectedDomainVerify",
    "nextId": "none"
  },
  {
    "id": "ids-malware-other",
    "verifyBeanName": "threatIntelligenceVerifyAction",
    "args": {
      "type": "ip",
      "source": "false"
    },
    "nextId": "ids-malware-other-rareIpVerify"
  },
  {
    "id": "ids-malware-other-rareIpVerify",
    "verifyBeanName": "rareIpVerify",
    "args": {
      "source": "false"
    },
    "nextId": "ids-malware-other-newIpVerify"
  },
  {
    "id": "ids-malware-other-newIpVerify",
    "verifyBeanName": "newIpVerify",
    "args": {
      "source": "false"
    },
    "nextId": "ids-malware-other-highRiskPortVerifyAction"
  },
  {
    "id": "ids-malware-other-highRiskPortVerifyAction",
    "verifyBeanName": "highRiskPortVerifyAction",
    "args": {
      "source": "false"
    },
    "nextId": "none"
  }
]