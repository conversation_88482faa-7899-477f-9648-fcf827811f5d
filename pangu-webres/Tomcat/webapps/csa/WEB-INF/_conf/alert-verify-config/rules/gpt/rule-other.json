[{"id": "SQLInjectionVerify", "verifyBeanName": "SQLInjectionVerify", "nextId": "eventTypeFixAction"}, {"id": "eventTypeFixAction", "verifyBeanName": "eventTypeFixAction", "nextId": "srcDstPortFixAction"}, {"id": "srcDstPortFixAction", "verifyBeanName": "srcDstPortFixAction", "nextId": "aiProvenanceVerify"}, {"id": "aiProvenanceVerify", "verifyBeanName": "aiProvenanceVerify", "nextId": "fillHttpFieldVerify"}, {"id": "fillHttpFieldVerify", "verifyBeanName": "fillHttpFieldVerify", "nextId": "directoryTraversalVerify"}, {"id": "directoryTraversalVerify", "verifyBeanName": "directoryTraversalVerify", "nextId": "none"}]