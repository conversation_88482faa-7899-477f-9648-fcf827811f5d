[{"id": "whiteListVerify", "verifyBeanName": "ruleWhiteListAction", "nextId": "whiteListSwitch"}, {"id": "whiteListSwitch", "switchBeanName": "ruleWhiteListSwitch", "flowTo": [{"id": "_srcLogTypeSwitch_", "condition": "#swiRes=='0'", "verifyBeanName": "passAction", "nextId": "applicationProtocolVerify"}, {"id": "_none_", "condition": "#swiRes=='1'", "verifyBeanName": "passAction", "nextId": "none"}]}, {"id": "applicationProtocolVerify", "verifyBeanName": "applicationProtocolVerify", "nextId": "srcLogTypeSwitch"}, {"id": "srcLogTypeSwitch", "switchBeanName": "srcLogTypeSwitch", "flowTo": [{"id": "_IDS", "condition": "#swiRes.startsWith('/security/IDS')", "verifyBeanName": "passAction", "nextId": "ids"}, {"id": "_EDR", "condition": "#swiRes.startsWith('/security/EDR')", "verifyBeanName": "passAction", "nextId": "edr"}, {"id": "_UEBA", "condition": "#swiRes.startsWith('/pangu/SOC/UEBA')", "verifyBeanName": "passAction", "nextId": "ueba"}]}]