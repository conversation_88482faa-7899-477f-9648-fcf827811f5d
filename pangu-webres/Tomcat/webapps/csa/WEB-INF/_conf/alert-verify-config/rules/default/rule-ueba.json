[
  {
    "id": "ueba",
    "switchBeanName": "alertTypeSwitch",
    "flowTo": [
      {
        "id": "_malware_",
        "condition": "#swiRes.startsWith('/EqUIzUrCGMYqHkmgdy7gY') || #swiRes.contains('MTvgIQyXInlxnummsEYa3')",
        "verifyBeanName": "passAction",
        "nextId": "ueba-malware"
      },
      {
        "id": "_account_",
        "condition": "#swiRes.startsWith('/Ri8DpwprjtlSAXFG6lWyS/jLwKVGwFJAWa3oxcz611B')",
        "verifyBeanName": "passAction",
        "nextId": "ueba-account"
      },
      {
        "id": "_spy_",
        "condition": "#swiRes.startsWith('/IIZ3aCd5NZUMDI1wnywD7/HBJnLLm47LSjoWDWk7TlE')",
        "verifyBeanName": "passAction",
        "nextId": "ueba-spy"
      }
    ]
  },
  {
    "id": "ueba-malware",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "DNS",
        "condition": "#swiRes=='192'",
        "verifyBeanName": "passAction",
        "nextId": "ueba-malware-dns"
      },
      {
        "id": "HTTP",
        "verifyBeanName": "passAction",
        "nextId": "ueba-malware-http"
      },
      {
        "id": "HTTPs",
        "verifyBeanName": "passAction",
        "nextId": "ueba-malware-https"
      }
    ]
  },
  {
    "id": "ueba-malware-dns",
    "verifyBeanName": "aVerify",  //新增
    "nextId": "none"
  },
  {
    "id": "ueba-malware-http",
    "verifyBeanName": "suspiciousOutreachVerify",
    "nextId": "none"
  },
  {
    "id": "ueba-malware-https",
    "verifyBeanName": "suspiciousOutreachVerify",
    "nextId": "none"
  },



  //account

  {
    "id": "ueba-account",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_ueba-account-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "ueba-account-alertTypeSwitch"
      }
    ]
  },

  {
    "id": "ueba-account-alertTypeSwitch",
    "switchBeanName": "alertTypeSwitch",
    "flowTo": [
      {
        "id": "ueba-account-pwd",
        "condition": "#swiRes.contains('/Ri8DpwprjtlSAXFG6lWyS/jLwKVGwFJAWa3oxcz611B/t9cb7d6lDaA529wZq2ew4')",
        "verifyBeanName": "passAction",
        "nextId": "ueba-account-pwd"
      }
    ]
  },
  {
    "id": "ueba-account-pwd",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "_ueba-account-pwd-rdp_",
        "condition": "#swiRes=='3389'",
        "verifyBeanName": "passAction",
        "nextId": "ueba-account-pwd-rdp"
      },
      {
        "id": "_ueba-account-pwd-ssh_",
        "condition": "#swiRes=='22'",
        "verifyBeanName": "passAction",
        "nextId": "ueba-account-pwd-ssh"
      },
      {
        "id": "_ueba-account-pwd-other_",
        "condition": "#swiRes!='22' && #swiRes!='3389'",
        "verifyBeanName": "passAction",
        "nextId": "ueba-account-pwd-other"
      }
    ]
  },
  {
    "id": "ueba-account-pwd-rdp",
    "verifyBeanName": "rdpPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "ueba-account-pwd-ssh",
    "verifyBeanName": "sshPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "ueba-account-pwd-other",
    "verifyBeanName": "otherPwdGuessVerify",
    "nextId": "none"
  },

  {
    "id": "ueba-spy",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_ueba-spy-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "ueba-spy-alertTypeSwitch"
      }
    ]
  },

  {
    "id": "ueba-spy-alertTypeSwitch",
    "switchBeanName": "alertTypeSwitch",
    "flowTo": [
      {
        "id": "spy-net",
        "condition": "#swiRes.contains('/IIZ3aCd5NZUMDI1wnywD7/HBJnLLm47LSjoWDWk7TlE/g69o5aeASMhnWbylmt49J')",
        "verifyBeanName": "passAction",
        "nextId": "ueba-spy-net"
      }
    ]
  },


  {
    "id": "ueba-spy-net",
    "verifyBeanName": "ipAndPortScanVerify",
    "nextId": "none"
  }




]