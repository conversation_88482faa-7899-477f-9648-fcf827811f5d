[
  {
    "id": "edr",
    "switchBeanName": "eventTypeSwitch",
    "flowTo": [
      {
        "id": "_malware_",
        "condition": "#swiRes.startsWith('/malware')",
        "verifyBeanName": "passAction",
        "nextId": "edr-malware"
      },
      {
        "id": "_att_",
        "condition": "#swiRes.startsWith('/att')",
        "verifyBeanName": "passAction",
        "nextId": "edr-att"
      },
      {
        "id": "_spy_",
        "condition": "#swiRes.startsWith('/spy')",
        "verifyBeanName": "passAction",
        "nextId": "edr-spy"
      }
    ]
  },
  {
    "id": "edr-malware",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "DNS",
        "condition": "#swiRes=='192'",
        "verifyBeanName": "passAction",
        "nextId": "edr-malware-dns"
      },
      {
        "id": "applicationProtocolSwitch-other",
        "verifyBeanName": "passAction",
        "nextId": "edr-malware-other"
      }
    ]
  },
  {
    "id": "edr-malware-dns",
    "verifyBeanName": "tiCollisionVerify",
    "nextId": "edr-malware-dns-newQryDomainVerify"
  },
  {
    "id": "edr-malware-dns-newQryDomainVerify",
    "verifyBeanName": "newQryDomainVerify",
    "nextId": "edr-malware-dns-newMainDomainVerify"
  },
  {
      "id": "edr-malware-dns-newMainDomainVerify",
      "verifyBeanName": "newMainDomainVerify",
      "nextId": "edr-malware-dns-rareMainDomainVerify"
  },
  {
    "id": "edr-malware-dns-rareMainDomainVerify",
    "verifyBeanName": "rareMainDomainVerify",
    "nextId": "edr-malware-dns-rareQryDomainVerify"
  },
  {
      "id": "edr-malware-dns-rareQryDomainVerify",
      "verifyBeanName": "rareQryDomainVerify",
      "nextId": "edr-malware-dns-multiConnectedDomainVerify"
  },
  {
    "id": "edr-malware-dns-multiConnectedDomainVerify",
    "verifyBeanName": "multiConnectedDomainVerify",
    "nextId": "none"
  },
  {
    "id": "edr-malware-other",
    "verifyBeanName": "threatIntelligenceVerifyAction",
    "args":{
      "type":"ip",
      "source":"false"
    },
    "nextId": "edr-malware-other-rareIpVerify"
  },
  {
    "id": "edr-malware-other-rareIpVerify",
    "verifyBeanName": "rareIpVerify",
    "args":{
      "source":"false"
    },
    "nextId": "edr-malware-other-newIpVerify"
  },
  {
    "id": "edr-malware-other-newIpVerify",
    "verifyBeanName": "newIpVerify",
    "args":{
      "source":"false"
    },
    "nextId": "edr-malware-other-highRiskPortVerifyAction"
  },
  {
    "id": "edr-malware-other-highRiskPortVerifyAction",
    "verifyBeanName": "highRiskPortVerifyAction",
    "args":{
      "source":"false"
    },
    "nextId": "none"
  },
  
  
  
  //att
  
  {
    "id": "edr-att",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_edr-att-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "edr-att-eventtypeSwitch"
      }
    ]
  },
  
  {
    "id": "edr-att-eventtypeSwitch",
    "switchBeanName": "eventTypeSwitch",
    "flowTo": [
      {
        "id": "edr-att-pwd",
        "condition": "#swiRes.contains('/pwd')",
        "verifyBeanName": "passAction",
        "nextId": "edr-att-pwd_"
      },
      {
        "id": "att-pwd",
        "condition": "!#swiRes.contains('/pwd')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      }
    ]
  },
  {
    "id": "edr-att-pwd_",
    "switchBeanName": "applicationProtocolSwitch",
    "flowTo": [
      {
        "id": "_edr-att-pwd-rdp_",
        "condition": "#swiRes=='3389'",
        "verifyBeanName": "passAction",
        "nextId": "edr-att-pwd-rdp"
      },
      {
        "id": "_edr-att-pwd-ssh_",
        "condition": "#swiRes=='22'",
        "verifyBeanName": "passAction",
        "nextId": "edr-att-pwd-ssh"
      },
      {
        "id": "_edr-att-pwd-other_",
        "condition": "#swiRes!='22' && #swiRes!='3389'",
        "verifyBeanName": "passAction",
        "nextId": "edr-att-pwd-other"
      }
    ]
  },
  {
    "id": "edr-att-pwd-rdp",   
    "verifyBeanName": "rdpPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "edr-att-pwd-ssh",   
    "verifyBeanName": "sshPwdGuessVerify",
    "nextId": "none"
  },
  {
    "id": "edr-att-pwd-other",   
    "verifyBeanName": "otherPwdGuessVerify",
    "nextId": "none"
  },
  
  
    
  
  
  
  //spy
  
  {
    "id": "edr-spy",
    "switchBeanName": "scannerSwitch",
    "flowTo": [
      {
        "id": "_none_",
        "condition": "#swiRes=='1'",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "_edr-spy-accScannerSwitch_",
        "condition": "#swiRes=='0'",
        "verifyBeanName": "passAction",
        "nextId": "edr-spy-eventTypeSwitch"
      }
    ]
  },
  
  {
    "id": "edr-spy-eventTypeSwitch",
    "switchBeanName": "eventTypeSwitch",
    "flowTo": [
      {
        "id": "spy-net",
        "condition": "#swiRes.contains('/net')",
        "verifyBeanName": "passAction",
        "nextId": "edr-spy-net"
      },
      {
        "id": "spy-port",
        "condition": "#swiRes.contains('/port')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "spy-vuln",
        "condition": "#swiRes.contains('/vuln')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      },
      {
        "id": "spy-tra",
        "condition": "#swiRes.contains('/tra')",
        "verifyBeanName": "passAction",
        "nextId": "edr-spy-tra"
      },
      {
        "id": "spy-other",
        "condition": "#swiRes.contains('/other')",
        "verifyBeanName": "passAction",
        "nextId": "none"
      }
    ]
  },
  
  
  {
    "id": "edr-spy-net",
    "verifyBeanName": "ipAndPortScanVerify",
    "nextId": "none"
  }
]