server:
  runMode: release       #开发模式  debug, release, test
  addr: 127.0.0.1:8086            #http端口
  basicAuth: admin
  basicPass: secret+3s
  webPrefix: xdr-manager/api

mysql:
  database: ${pangu.mysql.database}                         #数据库名称
  host: ${pangu.mysql.host}                                 #数据库连接地址
  port: ${pangu.mysql.port}                                 #数据库连接端口
  userName: ${pangu.mysql.username}                         #用户名 （加密）
  password: ${pangu.mysql.password}                         #密码   （加密）
  suffix: ?charset=utf8mb4&parseTime=True                   #数据库方式
  showLog: false                                            #是否打印sql日志
  maxIdleConn: 10                                           #最大闲置连接数
  maxOpenConn: 20                                           #最大打开的连接数
  maxLifeTime: 2m                                           #连接重用最大时间，单位分钟

pangu:
  addr: ${pangu.rest.url}
  probeAddr: https://%s:8443
  token:
    url: /api/v1/token/get
    username: ${pangu.rest.username}
    apiSecret: ${pangu.rest.apisecret}
  api:
    query: /api/v2/ckh/_execute
    queryWithParams: /api/v2/ckh/_executePS
    query2: /api/v2/ckh/_execute2
    probe:

logPath: ${pangu.log.dir}/xdr-go-logs

