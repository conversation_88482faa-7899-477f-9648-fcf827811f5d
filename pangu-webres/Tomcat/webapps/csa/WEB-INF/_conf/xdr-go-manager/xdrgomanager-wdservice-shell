#!/bin/bash

SOURCE="$0"
SOURCE_DIR="$( dirname "$SOURCE" )"
SOURCE_ABSOLUTE_DIR="$( cd -P "$SOURCE_DIR" >/dev/null 2>&1 && pwd )"

SHELL_HOME="xdr-go-manager"

architecture=$(syscmd --arch)

start() {
   cd ${SOURCE_ABSOLUTE_DIR}
   if [ "$architecture" = "x86_64" ]; then
     chmod u+x xdr-go-manager
     nohup ./xdr-go-manager > /dev/null 2>&1 &
   elif [ "$architecture" = "aarch64" ]; then
     chmod u+x xdr-go-manager-arm64
     nohup ./xdr-go-manager-arm64 > /dev/null 2>&1 &
   else
     echo "请定制当前系统架构的xdr-go-manager-app：$architecture"
   fi
}


stop() {
   pid=`ps -ef | grep "$SHELL_HOME" |grep -v "grep" |awk '{print $2}'`
   kill -9 $pid
}

case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  *)
    echo "param support start/stop"
    exit 1
    ;;
esac
exit $?
