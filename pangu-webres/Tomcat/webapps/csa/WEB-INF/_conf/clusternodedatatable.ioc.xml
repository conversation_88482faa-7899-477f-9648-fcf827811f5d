
<ioc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="nutz-ioc-0.1.xsd"> 
<!-- 系统所有节点的基本信息  -->
    <!--<obj name="$setup_NodeDataTable" type="com.venustech.taihe.pangu.foundation.cluster.data.NodeDataTable">
      <field name="before"><str>$setup_ClusterMgmtEngine</str></field>
      <field name="restrictedTo"><str>cluster</str></field>
      <field name="nodeRegisteredInfoList">
              <list>
                  &lt;!&ndash; 去掉级联相关配置
                   <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>Cascade</str></field>
                        <field name="nodeName"><i18n key="cluster.cascade" bundle="cluster" default="级联节点" /></field>
                    </obj>&ndash;&gt;
                    &lt;!&ndash;
                    去掉cdh节点
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>CDH</str></field>
                        <field name="nodeName"><i18n key="cluster.storage" bundle="cluster" default="分布式存储节点" /></field>
                    </obj>&ndash;&gt;

                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>cdn</str></field>
                        <field name="nodeName"><i18n key="cluster.storage" bundle="cluster" default="分布式存储节点" /></field>
                    </obj>
            &lt;!&ndash;		<obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>CvsCollector</str></field>
                        <field name="nodeName"><i18n key="cluster.collector.cvscollector" bundle="cluster" default="配置采集器" /></field>
                    </obj>&ndash;&gt;
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>LoaderEvent</str></field>
                        <field name="nodeName"><i18n key="cluster.loader.event" bundle="cluster" default="事件加载结点" /></field>
                    </obj>
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>cl</str></field>
                        <field name="nodeName"><i18n key="cluster.collector.eventcollector" bundle="cluster" default="事件采集器" /></field>
                    </obj>
                  &lt;!&ndash; [if deploy_version != csm] &ndash;&gt;
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>PerfCollector</str></field>
                        <field name="nodeName"><i18n key="cluster.collector.perfcollector" bundle="cluster" default="性能采集器" /></field>
                    </obj>
                  &lt;!&ndash; [endif] &ndash;&gt;
                  <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                      <field name="nodeType"><str>aiss</str></field>
                      <field name="nodeName"><i18n key="cluster.collector.aiss" bundle="cluster" default="AI安全传感器" /></field>
                  </obj>
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>FlowCollector</str></field>
                        <field name="nodeName"><i18n key="cluster.collector.flowcollector" bundle="cluster" default="流量采集器" /></field>
                    </obj>
                     <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>AcCollector</str></field>
                        <field name="nodeName"><i18n key="cluster.accollector" bundle="cluster" default="资产发现采集器" /></field>
                    </obj>
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>Portal</str></field>
                        <field name="nodeName"><i18n key="cluster.portal" bundle="cluster" default="门户节点" /></field>
                    </obj>
                    <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>nbaCollector</str></field>
                        <field name="nodeName"><i18n key="cluster.collector.nbaCollector" bundle="cluster" default="NTA(NBA)" /></field>
                    </obj>
                    &lt;!&ndash;<obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                        <field name="nodeType"><str>Analysis</str></field>
                        <field name="nodeName"><i18n key="cluster.analysis" bundle="cluster" default="分析节点" /></field>
                    </obj>&ndash;&gt;
                  <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                      <field name="nodeType"><str>oen</str></field>
                      <field name="nodeName"><i18n key="cluster.node.orchestra" bundle="cluster" default="剧本执行节点" /></field>
                  </obj>
                  &lt;!&ndash; [if hasRack thsensor-csp] &ndash;&gt;
                  <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                      <field name="nodeType"><str>csp</str></field>
                      <field name="nodeName"><i18n key="cluster.collector.thsensor_csp" bundle="thsensor_csp" default="流量监测探针" /></field>
                  </obj>
                  &lt;!&ndash; [endif] &ndash;&gt;
                  &lt;!&ndash; [if hasRack flow-engine] &ndash;&gt;
                  <obj type="com.venustech.taihe.pangu.foundation.cluster.data.NodeRegisteredInfo" >
                      <field name="nodeType"><str>xfc</str></field>
                      <field name="nodeName"><i18n key="cluster.collector.xfc" bundle="flow-engine" default="xfc采集器" /></field>
                  </obj>
                  &lt;!&ndash; [endif] &ndash;&gt;
              </list>
        </field>
    </obj>-->
</ioc>
