<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">



    <bean id="menu-sysconfig-syslabel" class="com.venustech.taihe.pangu.foundation.definition.Menu">
        <property name="id" value="menu-sysconfig-syslabel"/>
        <property name="appId" value="sysconfig"/>
        <property name="name" value="#{$i18n.message('sysLabel', 'system.sysdict.syslabel')}"/>
        <property name="category" value="category-system"/>
        <property name="icon" value=""/>
        <property name="entry" value="sysconfig/labelManage/index"/>
        <property name="authBy" value="sysconfig.label"/>
        <property name="license" value="sysconfig"/>
    </bean>

</beans>
