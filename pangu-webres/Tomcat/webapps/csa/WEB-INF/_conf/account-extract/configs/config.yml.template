mysql:
  database: ${pangu.mysql.database}                   #数据库名称
  host: ${pangu.mysql.host}                           #数据库连接地址
  port: ${pangu.mysql.port}                           #数据库连接端口
  username: ${pangu.mysql.username}                   #用户名(不加密)
  password: ${pangu.mysql.password}                   #密码(加密)
  suffix: ?charset=utf8mb4&parseTime=True             #数据库方式
  showLog: false                                      #是否打印SQL日志
  maxIdleConn: 10                                     #最大闲置的连接数
  maxOpenConn: 20                                     #最大打开的连接数
  maxLifeTime: 2m                                     #连接重用的最大时间，单位分钟

kafka:
  host:
    - ${pangu.kafka.server.url}
  sasl:
    enable: true
    protocol: ${pangu.kafka.server.protocol}
    mechanism: ${pangu.kafka.server.saslMechanism}
    username: ${pangu.kafka.server.username}
    password: ${pangu.kafka.server.password}
  subMainAccountTopic: ueba-user-account-info
  ipAccountTopic: ueba-ip-account

logPath: ${pangu.log.dir}/account-extract-logs

setAccNameAndTypeEmpty: true
