#example:
#- topic: 日志输入的topic
#- groupId: 消费者组
#  regex_field_name: 进行正则的字段
#  regular_expression: 正则表达式
#  account_type: 子账号类型
#  account_type_code: 子账号类型编码
#  collector_ip_field_name: 采集器ip字段
#  ip_field_name: 抽取的ip字段，如果为空，数据丢弃，如果多个用英文,隔开，不要加空格
#  exclude_ip: 需要排除的ip，用逗号隔开，不要加空格
#  sub_account_field_name: 子账号字段，如果为空，则数据无效丢弃，不抽取账号
#  event_time_field_name: 时间字段，如果时间为空、等于0、或小于当前时间一小时，则数据无效丢弃，不抽取账号
#  expiration_time: 过期时间（毫秒）
- topic: log-security-IDS-SMTP
  groupId: account-ids-smtp
  regex_field_name: csrcip
  regular_expression: ^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$ #是否是正确的ip
  account_type: MAIL
  account_type_code: 1
  collector_ip_field_name: ccollectorip
  ip_field_name: csrcip
  exclude_ip:
  sub_account_field_name: cuseraccountname
  event_time_field_name: loccurtime
  expiration_time: ******** #12小时：12*60*60*1000=********
- topic: log-security-VPN-SSL_VPN
  groupId: account-ssl-vpn
  regex_field_name: ip_assigned
  regular_expression: ^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$ #是否是正确的ip
  account_type: VPN
  account_type_code: 2
  collector_ip_field_name: ccollectorip
  ip_field_name: ip_assigned
  exclude_ip:
  sub_account_field_name: cuseraccountname
  event_time_field_name: loccurtime
  expiration_time: ******** #12小时：12*60*60*1000=********
- topic: log-security-NAC-Authentication
  groupId: account-nac-auth
  regex_field_name: coperation
  regular_expression: /login  #是否等于 "/login"
  account_type: NAC
  account_type_code: 8
  collector_ip_field_name: ccollectorip
  ip_field_name: csrcip
  exclude_ip:
  sub_account_field_name: cuseraccountname
  event_time_field_name: loccurtime
  expiration_time: ******** #12小时：12*60*60*1000=********
