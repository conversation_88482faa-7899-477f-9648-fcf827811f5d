
<ioc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	 xsi:noNamespaceSchemaLocation="nutz-ioc-0.1.xsd">

	<!-- 事件字典表描述文件，所有的字典表数据全部存放在此文件中，通过spring ioc读入EventDicTable类中 -->



	<!-- 协议和应用协议列表 -->
	<obj name="iprotocolList" type="java.util.ArrayList">
		<args>
			<list>
				<obj name="iprotocol-51" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>51</str></field>
					<field name="text"><str>ah</str></field>
				</obj>
				<obj name="iprotocol-68" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>68</str></field>
					<field name="text"><str>anydistribfs</str></field>
				</obj>
				<obj name="iprotocol-99" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>99</str></field>
					<field name="text"><str>anyencrypt</str></field>
				</obj>
				<obj name="iprotocol-61" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>61</str></field>
					<field name="text"><str>anyhost</str></field>
				</obj>				
				<obj name="iprotocol-63" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>63</str></field>
					<field name="text"><str>anylocalnet</str></field>
				</obj>
				<obj name="iprotocol-114" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>114</str></field>
					<field name="text"><str>any0hop</str></field>
				</obj>
				<obj name="iprotocol-13" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>13</str></field>
					<field name="text"><str>argus</str></field>
				</obj>
				<obj name="iprotocol-104" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>104</str></field>
					<field name="text"><str>aris</str></field>
				</obj>	
				<obj name="iprotocol-107" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>107</str></field>
					<field name="text"><str>a/n</str></field>
				</obj>
				<obj name="iprotocol-93" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>93</str></field>
					<field name="text"><str>ax.25</str></field>
				</obj>
				<obj name="iprotocol-10" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><str>bbn-rcc-mon</str></field>
				</obj>
				<obj name="iprotocol-49" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>49</str></field>
					<field name="text"><str>bna</str></field>
				</obj>				
				<obj name="iprotocol-76" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>76</str></field>
					<field name="text"><str>br-sat-mon</str></field>
				</obj>
				
				<obj name="iprotocol-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><str>cbt</str></field>
				</obj>
				<obj name="iprotocol-62" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>62</str></field>
					<field name="text"><str>cftp</str></field>
				</obj>
				<obj name="iprotocol-16" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>16</str></field>
					<field name="text"><str>chaos</str></field>
				</obj>
				<obj name="iprotocol-110" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>110</str></field>
					<field name="text"><str>compaq-peer</str></field>
				</obj>
				<obj name="iprotocol-72" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>72</str></field>
					<field name="text"><str>cpnx</str></field>
				</obj>
				<obj name="iprotocol-73" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>73</str></field>
					<field name="text"><str>cphb</str></field>
				</obj>
				<obj name="iprotocol-127" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>127</str></field>
					<field name="text"><str>crudp</str></field>
				</obj>
				<obj name="iprotocol-126" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>126</str></field>
					<field name="text"><str>crtp</str></field>
				</obj>
				
				
				<obj name="iprotocol-33" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>33</str></field>
					<field name="text"><str>dccp</str></field>
				</obj>
				<obj name="iprotocol-19" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>19</str></field>
					<field name="text"><str>dcn-meas</str></field>
				</obj>
				<obj name="iprotocol-37" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>37</str></field>
					<field name="text"><str>ddp</str></field>
				</obj>
				<obj name="iprotocol-116" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>116</str></field>
					<field name="text"><str>ddx</str></field>
				</obj>
				<obj name="iprotocol-86" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>86</str></field>
					<field name="text"><str>dgp</str></field>
				</obj>
				
				<obj name="iprotocol-8" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><str>egp</str></field>
				</obj>
				<obj name="iprotocol-14" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>14</str></field>
					<field name="text"><str>emcon</str></field>
				</obj>
				<obj name="iprotocol-98" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>98</str></field>
					<field name="text"><str>encap</str></field>
				</obj>
				<obj name="iprotocol-88" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>88</str></field>
					<field name="text"><str>eigrp</str></field>
				</obj>
				<obj name="iprotocol-50" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>50</str></field>
					<field name="text"><str>esp</str></field>
				</obj>
				<obj name="iprotocol-97" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>97</str></field>
					<field name="text"><str>etherip</str></field>
				</obj>
				<obj name="iprotocol-253" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>253</str></field>
					<field name="text"><str>experimental1</str></field>
				</obj>
				<obj name="iprotocol-254" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>254</str></field>
					<field name="text"><str>experimental2</str></field>
				</obj>
				
				
				<obj name="iprotocol-133" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>133</str></field>
					<field name="text"><str>fc</str></field>
				</obj>
				<obj name="iprotocol-125" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>125</str></field>
					<field name="text"><str>fire</str></field>
				</obj>
				
				<obj name="iprotocol-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3</str></field>
					<field name="text"><str>ggp</str></field>
				</obj>
				<obj name="iprotocol-100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>100</str></field>
					<field name="text"><str>gmtp</str></field>
				</obj>
				<obj name="iprotocol-47" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>47</str></field>
					<field name="text"><str>gre</str></field>
				</obj>
				
				<obj name="iprotocol-20" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><str>hmp</str></field>
				</obj>
				<obj name="iprotocol-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>0</str></field>
					<field name="text"><str>hopopt</str></field>
				</obj>
				
				<obj name="iprotocol-117" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>117</str></field>
					<field name="text"><str>iatp</str></field>
				</obj>
				<obj name="iprotocol-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><str>icmp</str></field>
				</obj>
				<obj name="iprotocol-35" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>35</str></field>
					<field name="text"><str>idpr</str></field>
				</obj>				
				<obj name="iprotocol-38" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>38</str></field>
					<field name="text"><str>idpr-cmtp</str></field>
				</obj>
				<obj name="iprotocol-45" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>45</str></field>
					<field name="text"><str>idrp</str></field>					
				</obj>
				<obj name="iprotocol-101" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>101</str></field>
					<field name="text"><str>ifmp</str></field>
				</obj>
				<obj name="iprotocol-9" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>9</str></field>
					<field name="text"><str>igp</str></field>
				</obj>
				<obj name="iprotocol-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><str>igmp</str></field>
				</obj>
				<obj name="iprotocol-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><str>ip</str></field>
				</obj>
				<obj name="iprotocol-71" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>71</str></field>
					<field name="text"><str>ipcv</str></field>
				</obj>		
				<obj name="iprotocol-108" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>108</str></field>
					<field name="text"><str>ipcomp</str></field>
				</obj>		
				<obj name="iprotocol-67" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>67</str></field>
					<field name="text"><str>ippc</str></field>
				</obj>
				<obj name="iprotocol-94" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>94</str></field>
					<field name="text"><str>ipip</str></field>
				</obj>
				<obj name="iprotocol-40" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>40</str></field>
					<field name="text"><str>il</str></field>
				</obj>
				<obj name="iprotocol-129" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>129</str></field>
					<field name="text"><str>iplt</str></field>
				</obj>
				<obj name="iprotocol-41" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>41</str></field>
					<field name="text"><str>ipv6</str></field>
				</obj>
				<obj name="iprotocol-44" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>44</str></field>
					<field name="text"><str>ipv6-frag</str></field>
				</obj>
				<obj name="iprotocol-58" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>58</str></field>
					<field name="text"><str>ipv6-icmp</str></field>
				</obj>
				<obj name="iprotocol-59" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>59</str></field>
					<field name="text"><str>ipv6-nonxt</str></field>
				</obj>
				<obj name="iprotocol-60" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>60</str></field>
					<field name="text"><str>ipv6-opts</str></field>
				</obj>				
				<obj name="iprotocol-43" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>43</str></field>
					<field name="text"><str>ipv6-route</str></field>
				</obj>
				<obj name="iprotocol-111" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>111</str></field>
					<field name="text"><str>ipx-in-ip</str></field>
				</obj>
				<obj name="iprotocol-28" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>28</str></field>
					<field name="text"><str>irtp</str></field>
				</obj>
				<obj name="iprotocol-80" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>80</str></field>
					<field name="text"><str>iso-ip</str></field>
				</obj>
				<obj name="iprotocol-29" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>29</str></field>
					<field name="text"><str>iso-tp4</str></field>
				</obj>
				<obj name="iprotocol-124" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>124</str></field>
					<field name="text"><str>isis-ipv4</str></field>
				</obj>
				<obj name="iprotocol-52" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>52</str></field>
					<field name="text"><str>i-nlsp</str></field>
				</obj>
				
				
				<obj name="iprotocol-65" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>65</str></field>
					<field name="text"><str>kryptolan</str></field>
				</obj>
				
				<obj name="iprotocol-91" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>91</str></field>
					<field name="text"><str>larp</str></field>
				</obj>
				<obj name="iprotocol-25" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>25</str></field>
					<field name="text"><str>leaf-1</str></field>
				</obj>
				<obj name="iprotocol-26" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>26</str></field>
					<field name="text"><str>leaf-2</str></field>
				</obj>
				<obj name="iprotocol-115" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>115</str></field>
					<field name="text"><str>l2tp</str></field>
				</obj>
				
				<obj name="iprotocol-32" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>32</str></field>
					<field name="text"><str>merit-inp</str></field>
				</obj>
				<obj name="iprotocol-31" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><str>mfe-nsp</str></field>
				</obj>
				<obj name="iprotocol-55" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>55</str></field>
					<field name="text"><str>mobile</str></field>
				</obj>
				<obj name="iprotocol-135" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>135</str></field>
					<field name="text"><str>mobility-hdr</str></field>
				</obj>
				<obj name="iprotocol-137" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>137</str></field>
					<field name="text"><str>mpls-in-ip</str></field>
				</obj>
				<obj name="iprotocol-48" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>48</str></field>
					<field name="text"><str>mhrp</str></field>
				</obj>
				<obj name="iprotocol-95" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>95</str></field>
					<field name="text"><str>micp</str></field>
				</obj>
				<obj name="iprotocol-92" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>92</str></field>
					<field name="text"><str>mtp</str></field>
				</obj>
				<obj name="iprotocol-18" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>18</str></field>
					<field name="text"><str>mux</str></field>
				</obj>
				
				
				<obj name="iprotocol-54" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>54</str></field>
					<field name="text"><str>narp</str></field>
				</obj>
				<obj name="iprotocol-30" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><str>netblt</str></field>
				</obj>
				<obj name="iprotocol-85" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>85</str></field>
					<field name="text"><str>nsfnet-igp</str></field>
				</obj>
				<obj name="iprotocol-11" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><str>nvp-ii</str></field>
				</obj>
				
				<obj name="iprotocol-89" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>89</str></field>
					<field name="text"><str>ospfigp</str></field>
				</obj>
				
				
				<obj name="iprotocol-113" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>113</str></field>
					<field name="text"><str>pgm</str></field>
				</obj>
				<obj name="iprotocol-103" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>103</str></field>
					<field name="text"><str>pim</str></field>
				</obj>
				<obj name="iprotocol-131" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>131</str></field>
					<field name="text"><str>pipe</str></field>
				</obj>
				<obj name="iprotocol-102" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>102</str></field>
					<field name="text"><str>pnni</str></field>
				</obj>
				<obj name="iprotocol-21" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>21</str></field>
					<field name="text"><str>prm</str></field>
				</obj>
				<obj name="iprotocol-123" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>123</str></field>
					<field name="text"><str>ptp</str></field>
				</obj>
				<obj name="iprotocol-12" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>12</str></field>
					<field name="text"><str>pup</str></field>
				</obj>
				<obj name="iprotocol-75" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>75</str></field>
					<field name="text"><str>pvp</str></field>
				</obj>
				
				<obj name="iprotocol-106" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>106</str></field>
					<field name="text"><str>qnx</str></field>
				</obj>
				
				<obj name="iprotocol-27" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>27</str></field>
					<field name="text"><str>rdp</str></field>
				</obj>				
				<obj name="iprotocol-46" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>46</str></field>
					<field name="text"><str>rsvp</str></field>
				</obj>
				<obj name="iprotocol-134" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>134</str></field>
					<field name="text"><str>rsvp-e2e-ignore</str></field>
				</obj>
				<obj name="iprotocol-66" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>66</str></field>
					<field name="text"><str>rvd</str></field>
				</obj>
				
				
				<obj name="iprotocol-64" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>64</str></field>
					<field name="text"><str>sat-expak</str></field>
				</obj>
				<obj name="iprotocol-69" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>69</str></field>
					<field name="text"><str>sat-mon</str></field>
				</obj>				
				<obj name="iprotocol-96" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>96</str></field>
					<field name="text"><str>scc-sp</str></field>
				</obj>
				<obj name="iprotocol-105" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>105</str></field>
					<field name="text"><str>scps</str></field>
				</obj>
				<obj name="iprotocol-132" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>132</str></field>
					<field name="text"><str>sctp</str></field>
				</obj>				
				<obj name="iprotocol-42" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>42</str></field>
					<field name="text"><str>sdrp</str></field>
				</obj>
				<obj name="iprotocol-82" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>82</str></field>
					<field name="text"><str>secure-vmtp</str></field>
				</obj>
				<obj name="iprotocol-57" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>57</str></field>
					<field name="text"><str>skip</str></field>
				</obj>
				<obj name="iprotocol-122" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>122</str></field>
					<field name="text"><str>sm</str></field>
				</obj>
				<obj name="iprotocol-121" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>121</str></field>
					<field name="text"><str>smp</str></field>
				</obj>
				<obj name="iprotocol-109" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>109</str></field>
					<field name="text"><str>snp</str></field>
				</obj>
				<obj name="iprotocol-130" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>130</str></field>
					<field name="text"><str>sps</str></field>
				</obj>
				
				<obj name="iprotocol-90" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>90</str></field>
					<field name="text"><str>sprite-rpc</str></field>
				</obj>
				<obj name="iprotocol-119" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>119</str></field>
					<field name="text"><str>srp</str></field>
				</obj>
				<obj name="iprotocol-128" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>128</str></field>
					<field name="text"><str>sscopmce</str></field>
				</obj>
				<obj name="iprotocol-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><str>st</str></field>
				</obj>
				<obj name="iprotocol-118" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>118</str></field>
					<field name="text"><str>stp</str></field>
				</obj>
				<obj name="iprotocol-77" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>77</str></field>
					<field name="text"><str>sun-nd</str></field>
				</obj>
				<obj name="iprotocol-53" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>53</str></field>
					<field name="text"><str>swipe</str></field>
				</obj>
				
				<obj name="iprotocol-87" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>87</str></field>
					<field name="text"><str>tcf</str></field>
				</obj>
				<obj name="iprotocol-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><str>tcp</str></field>
				</obj>
				<obj name="iprotocol-56" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>56</str></field>
					<field name="text"><str>tlsp</str></field>
				</obj>
				<obj name="iprotocol-39" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>39</str></field>
					<field name="text"><str>tp++</str></field>
				</obj>
				<obj name="iprotocol-23" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>23</str></field>
					<field name="text"><str>trunk-1</str></field>
				</obj>
				<obj name="iprotocol-24" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>24</str></field>
					<field name="text"><str>trunk-2</str></field>
				</obj>
				<obj name="iprotocol-84" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>84</str></field>
					<field name="text"><str>ttp</str></field>
				</obj>
				
				
				<obj name="iprotocol-17" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>17</str></field>
					<field name="text"><str>udp</str></field>
				</obj>
				<obj name="iprotocol-136" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>136</str></field>
					<field name="text"><str>udplite</str></field>
				</obj>				
				<obj name="iprotocol-120" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>120</str></field>
					<field name="text"><str>uti</str></field>
				</obj>
				
				
				<obj name="iprotocol-83" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>83</str></field>
					<field name="text"><str>vines</str></field>
				</obj>				
				<obj name="iprotocol-70" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>70</str></field>
					<field name="text"><str>visa</str></field>
				</obj>
				<obj name="iprotocol-81" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>81</str></field>
					<field name="text"><str>vmtp</str></field>
				</obj>
				<obj name="iprotocol-112" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>112</str></field>
					<field name="text"><str>vrrp</str></field>
				</obj>
				
				
				<obj name="iprotocol-79" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>79</str></field>
					<field name="text"><str>wb-expak</str></field>
				</obj>
				<obj name="iprotocol-78" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>78</str></field>
					<field name="text"><str>wb-mon</str></field>
				</obj>
				<obj name="iprotocol-74" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>74</str></field>
					<field name="text"><str>wsn</str></field>
				</obj>
				
				<obj name="iprotocol-15" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>15</str></field>
					<field name="text"><str>xnet</str></field>
				</obj>
				<obj name="iprotocol-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><str>xns-idp</str></field>
				</obj>
				<obj name="iprotocol-36" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>36</str></field>
					<field name="text"><str>xtp</str></field>
				</obj>
				
				<obj name="iprotocol-34" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>34</str></field>
					<field name="text"><str>3pc</str></field>
				</obj>
					
				<obj name="iprotocol-unknown" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>-1</str></field>
					<field name="text"><str>unknown</str></field>
				</obj>
				<obj name="iprotocol-arp" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>255</str></field>
					<field name="text"><str>arp</str></field>
				</obj>
			</list>
		</args>
	</obj>

	<obj name="iappProtocolList" type="java.util.ArrayList">
		<args>
			<list>
				<obj name="iappprotocol-67" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>67</str></field>
					<field name="text"><str>bootps</str></field>
				</obj>
				<obj name="iappprotocol-53" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>53</str></field>
					<field name="text"><str>domain</str></field>
				</obj>
				<obj name="iappprotocol-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><str>echo</str></field>
				</obj>
				<obj name="iappprotocol-79" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>79</str></field>
					<field name="text"><str>finger</str></field>
				</obj>
				<obj name="iappprotocol-21" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>21</str></field>
					<field name="text"><str>ftp</str></field>
				</obj>
				<obj name="iappprotocol-443" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>443</str></field>
					<field name="text"><str>https</str></field>
				</obj>
				<obj name="iappprotocol-220" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>220</str></field>
					<field name="text"><str>imap3</str></field>
				</obj>
				<obj name="iappprotocol-389" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>389</str></field>
					<field name="text"><str>ldap</str></field>
				</obj>
				<obj name="iappprotocol-1755" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1755</str></field>
					<field name="text"><str>MMS</str></field>
				</obj>
				<obj name="iappprotocol-137" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>137</str></field>
					<field name="text"><str>netbios-ns</str></field>
				</obj>
				<obj name="iappprotocol-110" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>110</str></field>
					<field name="text"><str>pop3</str></field>
				</obj>
				<obj name="iappprotocol-515" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>515</str></field>
					<field name="text"><str>printer</str></field>
				</obj>
				<obj name="iappprotocol-3389" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3389</str></field>
					<field name="text"><str>RDP</str></field>
				</obj>
				<obj name="iappprotocol-513" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>513</str></field>
					<field name="text"><str>rlogin</str></field>
				</obj>
				<obj name="iappprotocol-554" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>554</str></field>
					<field name="text"><str>RSTP</str></field>
				</obj>
				<obj name="iappprotocol-25" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>25</str></field>
					<field name="text"><str>smtp</str></field>
				</obj>
				<obj name="iappprotocol-161" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>161</str></field>
					<field name="text"><str>snmp</str></field>
				</obj>
				<obj name="iappprotocol-162" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>162</str></field>
					<field name="text"><str>snmp-traps</str></field>
				</obj>
				<obj name="iappprotocol-22" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>22</str></field>
					<field name="text"><str>SSH</str></field>
				</obj>
				<obj name="iappprotocol-111" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>111</str></field>
					<field name="text"><str>sun-rpc</str></field>
				</obj>
				<obj name="iappprotocol-23" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>23</str></field>
					<field name="text"><str>telnet</str></field>
				</obj>
				<obj name="iappprotocol-401" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>401</str></field>
					<field name="text"><str>ups</str></field>
				</obj>
				<obj name="iappprotocol-80" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>80</str></field>
					<field name="text"><str>www-http</str></field>
				</obj>

				<obj name="iappprotocol-1521" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1521</str></field>
					<field name="text"><str>oracle</str></field>
				</obj>
				<obj name="iappprotocol-3306" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3306</str></field>
					<field name="text"><str>mysql</str></field>
				</obj>
				<obj name="iappprotocol-1443" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1443</str></field>
					<field name="text"><str>mssql server</str></field>
				</obj>
				<obj name="iappprotocol-unknown" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>-1</str></field>
					<field name="text"><str>unknown</str></field>
				</obj>
				<!--  流量开始-->
				<obj name="il7prototype-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>0</str></field>
					<field name="text"><str>&#45;&#45;</str></field>
				</obj>
				<obj name="il7prototype-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><str>FTP_CONTROL</str></field>
				</obj>
				<obj name="il7prototype-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><str>FTP_DATA</str></field>
				</obj>
				<obj name="il7prototype-14" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>14</str></field>
					<field name="text"><str>emcon</str></field>
				</obj>
				<obj name="il7prototype-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><str>pop2</str></field>
				</obj>
				<obj name="il7prototype-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>200</str></field>
					<field name="text"><str>POPS</str></field>
				</obj>
				<obj name="il7prototype-8" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><str>IMAP</str></field>
				</obj>
				<obj name="il7prototype-9" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>9</str></field>
					<field name="text"><str>IMAPS</str></field>
				</obj>
				<obj name="il7prototype-10" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><str>SMTP</str></field>
				</obj>
				<obj name="il7prototype-11" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><str>SMTPS</str></field>
				</obj>
				<obj name="il7prototype-12" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>12</str></field>
					<field name="text"><str>HTTP</str></field>
				</obj>
				<obj name="il7prototype-13" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>13</str></field>
					<field name="text"><str>SSL</str></field>
				</obj>
				<obj name="il7prototype-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><str>tcp</str></field>
				</obj>
				<obj name="il7prototype-17" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>17</str></field>
					<field name="text"><str>MSN</str></field>
				</obj>
				<obj name="il7prototype-18" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>18</str></field>
					<field name="text"><str>QQ</str></field>
				</obj>
				<obj name="il7prototype-19" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>19</str></field>
					<field name="text"><str>BitTorrent</str></field>
				</obj>
				<obj name="il7prototype-20" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><str>IEC104</str></field>
				</obj>
				<obj name="il7prototype-21" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>201</str></field>
					<field name="text"><str>Tase2</str></field>
				</obj>
				<obj name="il7prototype-22" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>202</str></field>
					<field name="text"><str>DL476-92</str></field>
				</obj>
				<obj name="il7prototype-23" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>203</str></field>
					<field name="text"><str>SSDP</str></field>
				</obj>
				<obj name="il7prototype-24" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>24</str></field>
					<field name="text"><str>AppleJuice</str></field>
				</obj>
				<obj name="il7prototype-25" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>204</str></field>
					<field name="text"><str>DirectConnect</str></field>
				</obj>
				<obj name="il7prototype-26" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>26</str></field>
					<field name="text"><str>Socrates</str></field>
				</obj>
				<obj name="il7prototype-27" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>27</str></field>
					<field name="text"><str>WinMX</str></field>
				</obj>
				<obj name="il7prototype-28" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>28</str></field>
					<field name="text"><str>VMware</str></field>
				</obj>
				<obj name="il7prototype-29" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>29</str></field>
					<field name="text"><str>NFS</str></field>
				</obj>
				<obj name="il7prototype-30" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><str>Filetopia</str></field>
				</obj>
				<obj name="il7prototype-31" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><str>iMESH</str></field>
				</obj>
				<obj name="il7prototype-32" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>32</str></field>
					<field name="text"><str>Kontiki</str></field>
				</obj>
				<obj name="il7prototype-33" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>33</str></field>
					<field name="text"><str>OpenFT</str></field>
				</obj>
				<obj name="il7prototype-34" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>34</str></field>
					<field name="text"><str>FastTrack</str></field>
				</obj>
				<obj name="il7prototype-35" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>35</str></field>
					<field name="text"><str>Gnutella</str></field>
				</obj>
				<obj name="il7prototype-36" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>36</str></field>
					<field name="text"><str>eDonkey</str></field>
				</obj>
				<obj name="il7prototype-37" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>37</str></field>
					<field name="text"><str>PostgreSQL</str></field>
				</obj>
				<obj name="il7prototype-38" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>38</str></field>
					<field name="text"><str>EPP</str></field>
				</obj>
				<obj name="il7prototype-39" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>39</str></field>
					<field name="text"><str>AVI</str></field>
				</obj>
				<obj name="il7prototype-40" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>40</str></field>
					<field name="text"><str>Flash</str></field>
				</obj>
				<obj name="il7prototype-41" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>41</str></field>
					<field name="text"><str>OggVorbis</str></field>
				</obj>
				<obj name="il7prototype-42" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>42</str></field>
					<field name="text"><str>MPEG</str></field>
				</obj>
				<obj name="il7prototype-43" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>43</str></field>
					<field name="text"><str>QuickTime</str></field>
				</obj>
				<obj name="il7prototype-44" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>44</str></field>
					<field name="text"><str>RealMedia</str></field>
				</obj>
				<obj name="il7prototype-45" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>45</str></field>
					<field name="text"><str>WindowsMedia</str></field>
				</obj>
				<obj name="il7prototype-47" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>47</str></field>
					<field name="text"><str>Xbox</str></field>
				</obj>
				<obj name="il7prototype-48" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>48</str></field>
					<field name="text"><str>DHCP</str></field>
				</obj>
				<obj name="il7prototype-49" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>49</str></field>
					<field name="text"><str>Move</str></field>
				</obj>
				<obj name="il7prototype-50" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>50</str></field>
					<field name="text"><str>RTSP</str></field>
				</obj>
				<obj name="il7prototype-51" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>51</str></field>
					<field name="text"><str>NTP</str></field>
				</obj>
				<obj name="il7prototype-52" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>52</str></field>
					<field name="text"><str>IceCast</str></field>
				</obj>
				<obj name="il7prototype-53" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>205</str></field>
					<field name="text"><str>PPLive</str></field>
				</obj>
				<obj name="il7prototype-54" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>54</str></field>
					<field name="text"><str>PPStream</str></field>
				</obj>
				<obj name="il7prototype-55" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>55</str></field>
					<field name="text"><str>Zattoo</str></field>
				</obj>
				<obj name="il7prototype-56" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>56</str></field>
					<field name="text"><str>ShoutCast</str></field>
				</obj>
				<obj name="il7prototype-57" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>57</str></field>
					<field name="text"><str>Sopcast</str></field>
				</obj>
				<obj name="il7prototype-58" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>58</str></field>
					<field name="text"><str>Tvants</str></field>
				</obj>
				<obj name="il7prototype-59" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>59</str></field>
					<field name="text"><str>TVUplayer</str></field>
				</obj>
				<obj name="il7prototype-60" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>60</str></field>
					<field name="text"><str>HTTP_APPLICATION_VEOHTV</str></field>
				</obj>
				<obj name="il7prototype-61" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>61</str></field>
					<field name="text"><str>QQLive</str></field>
				</obj>
				<obj name="il7prototype-62" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>62</str></field>
					<field name="text"><str>Thunder</str></field>
				</obj>
				<obj name="il7prototype-63" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>63</str></field>
					<field name="text"><str>Soulseek</str></field>
				</obj>
				<obj name="il7prototype-64" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>64</str></field>
					<field name="text"><str>SSL_No_Cert</str></field>
				</obj>
				<obj name="il7prototype-65" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>65</str></field>
					<field name="text"><str>IRC</str></field>
				</obj>
				<obj name="il7prototype-66" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>66</str></field>
					<field name="text"><str>Ayiya</str></field>
				</obj>
				<obj name="il7prototype-67" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>206</str></field>
					<field name="text"><str>Unencryped_Jabber</str></field>
				</obj>
				<obj name="il7prototype-68" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>68</str></field>
					<field name="text"><str>Syslog</str></field>
				</obj>
				<obj name="il7prototype-69" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>69</str></field>
					<field name="text"><str>Oscar</str></field>
				</obj>
				<obj name="il7prototype-70" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>70</str></field>
					<field name="text"><str>Yahoo</str></field>
				</obj>
				<obj name="il7prototype-71" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>71</str></field>
					<field name="text"><str>BattleField</str></field>
				</obj>
				<obj name="il7prototype-72" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>72</str></field>
					<field name="text"><str>Quake</str></field>
				</obj>
				<obj name="il7prototype-73" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>73</str></field>
					<field name="text"><str>VRRP</str></field>
				</obj>
				<obj name="il7prototype-74" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>74</str></field>
					<field name="text"><str>Steam</str></field>
				</obj>
				<obj name="il7prototype-75" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>75</str></field>
					<field name="text"><str>HalfLife2</str></field>
				</obj>
				<obj name="il7prototype-76" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>76</str></field>
					<field name="text"><str>WorldOfWarcraft</str></field>
				</obj>
				<obj name="il7prototype-77" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>77</str></field>
					<field name="text"><str>NetBIOS</str></field>
				</obj>
				<obj name="il7prototype-78" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>78</str></field>
					<field name="text"><str>STUN</str></field>
				</obj>
				<obj name="il7prototype-79" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>207</str></field>
					<field name="text"><str>IPsec</str></field>
				</obj>
				<obj name="il7prototype-80" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>208</str></field>
					<field name="text"><str>GRE</str></field>
				</obj>
				<obj name="il7prototype-81" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>81</str></field>
					<field name="text"><str>ICMP</str></field>
				</obj>
				<obj name="il7prototype-82" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>82</str></field>
					<field name="text"><str>IGMP</str></field>
				</obj>
				<obj name="il7prototype-83" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>83</str></field>
					<field name="text"><str>EGP</str></field>
				</obj>
				<obj name="il7prototype-84" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>84</str></field>
					<field name="text"><str>SCTP</str></field>
				</obj>
				<obj name="il7prototype-85" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>85</str></field>
					<field name="text"><str>OSPF</str></field>
				</obj>
				<obj name="il7prototype-86" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>86</str></field>
					<field name="text"><str>IP_in_IP</str></field>
				</obj>
				<obj name="il7prototype-87" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>87</str></field>
					<field name="text"><str>RTP</str></field>
				</obj>
				<obj name="il7prototype-89" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>89</str></field>
					<field name="text"><str>VNC</str></field>
				</obj>
				<obj name="il7prototype-90" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>90</str></field>
					<field name="text"><str>PcAnywhere</str></field>
				</obj>
				<obj name="il7prototype-91" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>91</str></field>
					<field name="text"><str>BGP</str></field>
				</obj>
				<obj name="il7prototype-92" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>92</str></field>
					<field name="text"><str>MDNS</str></field>
				</obj>
				<obj name="il7prototype-93" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>93</str></field>
					<field name="text"><str>Usenet</str></field>
				</obj>
				<obj name="il7prototype-94" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>94</str></field>
					<field name="text"><str>MGCP</str></field>
				</obj>
				<obj name="il7prototype-95" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>95</str></field>
					<field name="text"><str>IAX</str></field>
				</obj>
				<obj name="il7prototype-96" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>96</str></field>
					<field name="text"><str>TFTP</str></field>
				</obj>
				<obj name="il7prototype-97" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>97</str></field>
					<field name="text"><str>AFP</str></field>
				</obj>
				<obj name="il7prototype-98" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>98</str></field>
					<field name="text"><str>Stealthnet</str></field>
				</obj>
				<obj name="il7prototype-99" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>99</str></field>
					<field name="text"><str>Aimini</str></field>
				</obj>
				<obj name="il7prototype-100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>100</str></field>
					<field name="text"><str>SIP</str></field>
				</obj>
				<obj name="il7prototype-101" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>101</str></field>
					<field name="text"><str>TruPhone</str></field>
				</obj>
				<obj name="il7prototype-102" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>102</str></field>
					<field name="text"><str>ICMPV6</str></field>
				</obj>
				<obj name="il7prototype-103" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>103</str></field>
					<field name="text"><str>DHCPV6</str></field>
				</obj>
				<obj name="il7prototype-104" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>104</str></field>
					<field name="text"><str>Armagetron</str></field>
				</obj>
				<obj name="il7prototype-105" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>105</str></field>
					<field name="text"><str>Crossfire</str></field>
				</obj>
				<obj name="il7prototype-106" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>106</str></field>
					<field name="text"><str>Dofus</str></field>
				</obj>
				<obj name="il7prototype-107" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>107</str></field>
					<field name="text"><str>Fiesta</str></field>
				</obj>
				<obj name="il7prototype-108" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>108</str></field>
					<field name="text"><str>Florensia</str></field>
				</obj>
				<obj name="il7prototype-109" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>109</str></field>
					<field name="text"><str>Guildwars</str></field>
				</obj>
				<obj name="il7prototype-110" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>209</str></field>
					<field name="text"><str>HTTP_Application_ActiveSync</str></field>
				</obj>
				<obj name="il7prototype-111" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>210</str></field>
					<field name="text"><str>Kerberos</str></field>
				</obj>
				<obj name="il7prototype-112" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>112</str></field>
					<field name="text"><str>LDAP</str></field>
				</obj>
				<obj name="il7prototype-113" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>113</str></field>
					<field name="text"><str>MapleStory</str></field>
				</obj>
				<obj name="il7prototype-114" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>114</str></field>
					<field name="text"><str>SMB</str></field>
				</obj>
				<obj name="il7prototype-115" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>115</str></field>
					<field name="text"><str>PPTP</str></field>
				</obj>
				<obj name="il7prototype-116" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>116</str></field>
					<field name="text"><str>Warcraft3</str></field>
				</obj>
				<obj name="il7prototype-117" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>117</str></field>
					<field name="text"><str>WorldOfKungFu</str></field>
				</obj>
				<obj name="il7prototype-118" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>118</str></field>
					<field name="text"><str>Meebo</str></field>
				</obj>
				<obj name="il7prototype-119" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>119</str></field>
					<field name="text"><str>Facebook</str></field>
				</obj>
				<obj name="il7prototype-120" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>120</str></field>
					<field name="text"><str>Twitter</str></field>
				</obj>
				<obj name="il7prototype-121" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>121</str></field>
					<field name="text"><str>DropBox</str></field>
				</obj>
				<obj name="il7prototype-122" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>122</str></field>
					<field name="text"><str>GMail</str></field>
				</obj>
				<obj name="il7prototype-123" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>123</str></field>
					<field name="text"><str>GoogleMaps</str></field>
				</obj>
				<obj name="il7prototype-124" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>124</str></field>
					<field name="text"><str>YouTube</str></field>
				</obj>
				<obj name="il7prototype-125" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>125</str></field>
					<field name="text"><str>Skype</str></field>
				</obj>
				<obj name="il7prototype-126" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>126</str></field>
					<field name="text"><str>Google</str></field>
				</obj>
				<obj name="il7prototype-127" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>127</str></field>
					<field name="text"><str>DCE_RPC</str></field>
				</obj>
				<obj name="il7prototype-128" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>128</str></field>
					<field name="text"><str>NetFlow</str></field>
				</obj>
				<obj name="il7prototype-129" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>129</str></field>
					<field name="text"><str>sFlow</str></field>
				</obj>
				<obj name="il7prototype-130" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>130</str></field>
					<field name="text"><str>HTTP_Connect</str></field>
				</obj>
				<obj name="il7prototype-131" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>131</str></field>
					<field name="text"><str>HTTP_Proxy</str></field>
				</obj>
				<obj name="il7prototype-132" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>132</str></field>
					<field name="text"><str>Citrix</str></field>
				</obj>
				<obj name="il7prototype-133" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>133</str></field>
					<field name="text"><str>NetFlix</str></field>
				</obj>
				<obj name="il7prototype-134" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>134</str></field>
					<field name="text"><str>LastFM</str></field>
				</obj>
				<obj name="il7prototype-135" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>135</str></field>
					<field name="text"><str>GrooveShark</str></field>
				</obj>
				<obj name="il7prototype-136" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>136</str></field>
					<field name="text"><str>SkyFile_PrePaid</str></field>
				</obj>
				<obj name="il7prototype-137" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>211</str></field>
					<field name="text"><str>SkyFile_Rudics</str></field>
				</obj>
				<obj name="il7prototype-138" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>138</str></field>
					<field name="text"><str>SkyFile_PostPaid</str></field>
				</obj>
				<obj name="il7prototype-139" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>139</str></field>
					<field name="text"><str>Citrix_Online</str></field>
				</obj>
				<obj name="il7prototype-140" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>140</str></field>
					<field name="text"><str>Apple</str></field>
				</obj>
				<obj name="il7prototype-141" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>141</str></field>
					<field name="text"><str>Webex</str></field>
				</obj>
				<obj name="il7prototype-142" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>142</str></field>
					<field name="text"><str>WhatsApp</str></field>
				</obj>
				<obj name="il7prototype-143" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>143</str></field>
					<field name="text"><str>AppleiCloud</str></field>
				</obj>
				<obj name="il7prototype-144" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>144</str></field>
					<field name="text"><str>Viber</str></field>
				</obj>
				<obj name="il7prototype-145" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>145</str></field>
					<field name="text"><str>AppleiTunes</str></field>
				</obj>
				<obj name="il7prototype-146" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>146</str></field>
					<field name="text"><str>Radius</str></field>
				</obj>
				<obj name="il7prototype-147" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>147</str></field>
					<field name="text"><str>WindowsUpdate</str></field>
				</obj>
				<obj name="il7prototype-148" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>148</str></field>
					<field name="text"><str>TeamViewer</str></field>
				</obj>
				<obj name="il7prototype-149" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>149</str></field>
					<field name="text"><str>Tuenti</str></field>
				</obj>
				<obj name="il7prototype-150" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>150</str></field>
					<field name="text"><str>LotusNotes</str></field>
				</obj>
				<obj name="il7prototype-151" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>151</str></field>
					<field name="text"><str>SAP</str></field>
				</obj>
				<obj name="il7prototype-152" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>152</str></field>
					<field name="text"><str>GTP</str></field>
				</obj>
				<obj name="il7prototype-153" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>153</str></field>
					<field name="text"><str>UPnP</str></field>
				</obj>
				<obj name="il7prototype-154" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>154</str></field>
					<field name="text"><str>LLMNR</str></field>
				</obj>
				<obj name="il7prototype-155" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>155</str></field>
					<field name="text"><str>RemoteScan</str></field>
				</obj>
				<obj name="il7prototype-156" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>156</str></field>
					<field name="text"><str>Spotify</str></field>
				</obj>
				<obj name="il7prototype-157" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>157</str></field>
					<field name="text"><str>WebM</str></field>
				</obj>
				<obj name="il7prototype-158" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>158</str></field>
					<field name="text"><str>H323</str></field>
				</obj>
				<obj name="il7prototype-159" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>159</str></field>
					<field name="text"><str>OpenVPN</str></field>
				</obj>
				<obj name="il7prototype-160" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>212</str></field>
					<field name="text"><str>NOE</str></field>
				</obj>
				<obj name="il7prototype-161" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>213</str></field>
					<field name="text"><str>CiscoVPN</str></field>
				</obj>
				<obj name="il7prototype-162" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>162</str></field>
					<field name="text"><str>TeamSpeak</str></field>
				</obj>
				<obj name="il7prototype-163" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>163</str></field>
					<field name="text"><str>TOR</str></field>
				</obj>
				<obj name="il7prototype-164" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>164</str></field>
					<field name="text"><str>CiscoSkinny</str></field>
				</obj>
				<obj name="il7prototype-165" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>165</str></field>
					<field name="text"><str>RTCP</str></field>
				</obj>
				<obj name="il7prototype-166" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>166</str></field>
					<field name="text"><str>RSYNC</str></field>
				</obj>
				<obj name="il7prototype-167" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>167</str></field>
					<field name="text"><str>XDMCP</str></field>
				</obj>
				<obj name="il7prototype-168" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>168</str></field>
					<field name="text"><str>Corba</str></field>
				</obj>
				<obj name="il7prototype-169" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>169</str></field>
					<field name="text"><str>UbuntuONE</str></field>
				</obj>
				<obj name="il7prototype-170" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>170</str></field>
					<field name="text"><str>Whois-DAS</str></field>
				</obj>
				<obj name="il7prototype-171" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>171</str></field>
					<field name="text"><str>Collectd</str></field>
				</obj>
				<obj name="il7prototype-172" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>172</str></field>
					<field name="text"><str>SOCKS5</str></field>
				</obj>
				<obj name="il7prototype-173" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>173</str></field>
					<field name="text"><str>SOCKS4</str></field>
				</obj>
				<obj name="il7prototype-174" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>174</str></field>
					<field name="text"><str>RTMP</str></field>
				</obj>
				<obj name="il7prototype-175" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>175</str></field>
					<field name="text"><str>IPP</str></field>
				</obj>
				<obj name="il7prototype-176" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>176</str></field>
					<field name="text"><str>Wikipedia</str></field>
				</obj>
				<obj name="il7prototype-177" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>177</str></field>
					<field name="text"><str>VHUA</str></field>
				</obj>
				<obj name="il7prototype-178" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>178</str></field>
					<field name="text"><str>Amazon</str></field>
				</obj>
				<obj name="il7prototype-179" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>179</str></field>
					<field name="text"><str>eBay</str></field>
				</obj>
				<obj name="il7prototype-180" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>180</str></field>
					<field name="text"><str>CNN</str></field>
				</obj>
				<obj name="il7prototype-181" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>181</str></field>
					<field name="text"><str>Telegram</str></field>
				</obj>
				<obj name="il7prototype-182" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>182</str></field>
					<field name="text"><str>Vevo</str></field>
				</obj>
				<obj name="il7prototype-183" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>183</str></field>
					<field name="text"><str>Pandora</str></field>
				</obj>
				<obj name="il7prototype-184" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>184</str></field>
					<field name="text"><str>Quic</str></field>
				</obj>
				<obj name="il7prototype-185" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>185</str></field>
					<field name="text"><str>Pando_Media_Booster</str></field>
				</obj>
				<obj name="il7prototype-186" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>186</str></field>
					<field name="text"><str>Megaco</str></field>
				</obj>
				<obj name="il7prototype-187" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>187</str></field>
					<field name="text"><str>Redis</str></field>
				</obj>
				<obj name="il7prototype-188" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>188</str></field>
					<field name="text"><str>ZeroMQ</str></field>
				</obj>
				<obj name="il7prototype-189" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>189</str></field>
					<field name="text"><str>TDS</str></field>
				</obj>
				<obj name="il7prototype-190" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>190</str></field>
					<field name="text"><str>Direct_Download_Link</str></field>
				</obj>
				<obj name="il7prototype-191" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>191</str></field>
					<field name="text"><str>SNMP</str></field>
				</obj>
				<obj name="il7prototype-192" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>192</str></field>
					<field name="text"><str>DNS</str></field>
				</obj>
				<obj name="il7prototype-193" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>193</str></field>
					<field name="text"><str>IEC103</str></field>
				</obj>
				<obj name="il7prototype-194" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>194</str></field>
					<field name="text"><str>Modbus</str></field>
				</obj>
				<obj name="il7prototype-195" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>195</str></field>
					<field name="text"><str>ENIP</str></field>
				</obj>
				<obj name="il7prototype-196" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>196</str></field>
					<field name="text"><str>DNP3</str></field>
				</obj>
				<obj name="il7prototype-197" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>197</str></field>
					<field name="text"><str>S7</str></field>
				</obj>
				<obj name="il7prototype-198" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>198</str></field>
					<field name="text"><str>teredo</str></field>
				</obj>
				<obj name="il7prototype-199" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>199</str></field>
					<field name="text"><str>IEC61850_MMS</str></field>
				</obj>
				<obj name="il7prototype-10001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10001</str></field>
					<field name="text"><str>DCOM</str></field>
				</obj>
				<obj name="il7prototype-10002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10002</str></field>
					<field name="text"><str>OPC</str></field>
				</obj>
				<obj name="il7prototype-10003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10003</str></field>
					<field name="text"><str>Profinet</str></field>
				</obj>
				<obj name="il7prototype-10004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10004</str></field>
					<field name="text"><str>IEC61850_GOOSE</str></field>
				</obj>
				<obj name="il7prototype-10004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10005</str></field>
					<field name="text"><str>SMB2</str></field>
				</obj>
				<obj name="il7prototype-unknown" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>-1</str></field>
					<field name="text"><str>unknown</str></field>
				</obj>
				<!--  流量结束-->
			</list>
		</args>
	</obj>

	<!--  tcp 标志位字典-->
	<obj name="itcp_flags" type="java.util.ArrayList">
		<args>
			<list>
				<obj name="itcp_flags-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>0</str></field>
					<field name="text"><str>&#45;&#45;</str></field>
				</obj>
				<obj name="itcp_flags-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><str>FIN</str></field>
				</obj>
				<obj name="itcp_flags-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><str>SYN</str></field>
				</obj>
				<obj name="itcp_flags-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3</str></field>
					<field name="text"><str>FIN SYN</str></field>
				</obj>
				<obj name="itcp_flags-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><str>RST</str></field>
				</obj>
				<obj name="itcp_flags-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><str>FIN RST</str></field>
				</obj>
				<obj name="itcp_flags-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><str>SYN RST</str></field>
				</obj>
				<obj name="itcp_flags-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><str>FIN SYN RST</str></field>
				</obj>
				<obj name="itcp_flags-8" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><str>PSH</str></field>
				</obj>
				<obj name="itcp_flags-9" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>9</str></field>
					<field name="text"><str>FIN PSH</str></field>
				</obj>
				<obj name="itcp_flags-10" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><str>SYN PSH</str></field>
				</obj>
				<obj name="itcp_flags-11" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><str>FIN SYN PSH</str></field>
				</obj>
				<obj name="itcp_flags-12" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>12</str></field>
					<field name="text"><str>RST PSH</str></field>
				</obj>
				<obj name="itcp_flags-13" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>13</str></field>
					<field name="text"><str>FIN RST PSH</str></field>
				</obj>
				<obj name="itcp_flags-14" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>14</str></field>
					<field name="text"><str>SYN RST PSH</str></field>
				</obj>
				<obj name="itcp_flags-15" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>15</str></field>
					<field name="text"><str>FIN SYN RST PSH</str></field>
				</obj>
				<obj name="itcp_flags-16" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>16</str></field>
					<field name="text"><str>ACK</str></field>
				</obj>
				<obj name="itcp_flags-17" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>17</str></field>
					<field name="text"><str>FIN ACK</str></field>
				</obj>
				<obj name="itcp_flags-18" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>18</str></field>
					<field name="text"><str>SYN ACK</str></field>
				</obj>
				<obj name="itcp_flags-19" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>19</str></field>
					<field name="text"><str>FIN SYN ACK</str></field>
				</obj>
				<obj name="itcp_flags-20" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><str>RST ACK</str></field>
				</obj>
				<obj name="itcp_flags-21" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>21</str></field>
					<field name="text"><str>FIN RST ACK</str></field>
				</obj>
				<obj name="itcp_flags-22" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>22</str></field>
					<field name="text"><str>SYN RST ACK</str></field>
				</obj>
				<obj name="itcp_flags-23" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>23</str></field>
					<field name="text"><str>FIN SYN RST ACK</str></field>
				</obj>
				<obj name="itcp_flags-24" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>24</str></field>
					<field name="text"><str>PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-25" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>25</str></field>
					<field name="text"><str>FIN PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-26" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>26</str></field>
					<field name="text"><str>SYN PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-27" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>27</str></field>
					<field name="text"><str>FIN SYN PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-28" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>28</str></field>
					<field name="text"><str>RST PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-29" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>29</str></field>
					<field name="text"><str>RST PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-30" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><str>SYN RST PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-31" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><str>FIN SYN RST PSH ACK</str></field>
				</obj>
				<obj name="itcp_flags-32" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>32</str></field>
					<field name="text"><str>URG</str></field>
				</obj>
				<obj name="itcp_flags-33" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>33</str></field>
					<field name="text"><str>FIN URG</str></field>
				</obj>
				<obj name="itcp_flags-34" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>34</str></field>
					<field name="text"><str>SYN URG</str></field>
				</obj>
				<obj name="itcp_flags-35" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>35</str></field>
					<field name="text"><str>FIN SYN URG</str></field>
				</obj>
				<obj name="itcp_flags-36" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>36</str></field>
					<field name="text"><str>RST URG</str></field>
				</obj>
				<obj name="itcp_flags-37" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>37</str></field>
					<field name="text"><str>FIN RST URG</str></field>
				</obj>
				<obj name="itcp_flags-38" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>38</str></field>
					<field name="text"><str>SYN RST URG</str></field>
				</obj>
				<obj name="itcp_flags-39" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>39</str></field>
					<field name="text"><str>FIN SYN RST URG</str></field>
				</obj>
				<obj name="itcp_flags-40" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>40</str></field>
					<field name="text"><str>PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-41" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>41</str></field>
					<field name="text"><str>FIN PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-42" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>42</str></field>
					<field name="text"><str>SYN PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-43" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>43</str></field>
					<field name="text"><str>FIN SYN PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-44" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>44</str></field>
					<field name="text"><str>RST PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-45" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>45</str></field>
					<field name="text"><str>FIN RST PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-46" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>46</str></field>
					<field name="text"><str>SYN RST PSH URG</str></field>
				</obj>
				<obj name="itcp_flags-47" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>47</str></field>
					<field name="text"><str>F/S/R/P/URG</str></field>
				</obj>
				<obj name="itcp_flags-48" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>48</str></field>
					<field name="text"><str>ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-49" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>49</str></field>
					<field name="text"><str>FIN ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-50" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>50</str></field>
					<field name="text"><str>SYN ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-51" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>51</str></field>
					<field name="text"><str>FIN SYN ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-52" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>52</str></field>
					<field name="text"><str>RST ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-53" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>53</str></field>
					<field name="text"><str>FIN RST ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-54" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>54</str></field>
					<field name="text"><str>SYN RST ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-55" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>55</str></field>
					<field name="text"><str>FSRAU</str></field>
				</obj>
				<obj name="itcp_flags-56" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>56</str></field>
					<field name="text"><str>PSH ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-57" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>57</str></field>
					<field name="text"><str>FIN PSH ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-58" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>58</str></field>
					<field name="text"><str>SYN PSH ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-59" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>59</str></field>
					<field name="text"><str>FSPAU</str></field>
				</obj>
				<obj name="itcp_flags-60" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>60</str></field>
					<field name="text"><str>RST PSH ACK URG</str></field>
				</obj>
				<obj name="itcp_flags-61" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>61</str></field>
					<field name="text"><str>FRPAU</str></field>
				</obj>
				<obj name="itcp_flags-62" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>62</str></field>
					<field name="text"><str>SRPAU</str></field>
				</obj>
				<obj name="itcp_flags-63" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>63</str></field>
					<field name="text"><str>FSRPAU</str></field>
				</obj>
				<obj name="itcp_flags-unknown" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>-1</str></field>
					<field name="text"><str>&#45;&#45;</str></field>
				</obj>
			</list>
		</args>
	</obj>

	<!-- 流量方向-->
	<obj name="directionList" type="java.util.ArrayList">
		<args>
			<list>
				<obj name="direction-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>00</str></field>
					<field name="text"><str>LtoL</str></field>
				</obj>
				<obj name="direction-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>01</str></field>
					<field name="text"><str>LtoR</str></field>
				</obj>
				<obj name="direction-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><str>RtoR</str></field>
				</obj>
				<obj name="direction-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><str>RtoL</str></field>
				</obj>
				<obj name="direction-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>-1</str></field>
					<field name="text"><str>&#45;&#45;</str></field>
				</obj>
			</list>
		</args>
	</obj>

	<!-- 设备类型-->
	<obj name="devtypeList" type="java.util.ArrayList">
		<args>
			<list>
				<!-- 主机 -->
				<obj name="cdevtype-server" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server</str></field>
					<field name="text"><i18n key="edt.data.devtype.host" bundle="edt" default="/主机" /></field>
				</obj>
				<obj name="cdevtype-server_Linux" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/Linux</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.linux" bundle="edt" default="/主机/Linux" /></field>
				</obj>
				<obj name="cdevtype-server_Windows" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/Windows</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.windows" bundle="edt" default="/主机/Windows" /></field>
				</obj>
				<obj name="cdevtype-server_AIX" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/AIX</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.aix" bundle="edt" default="/主机/AIX" /></field>
				</obj>
				<obj name="cdevtype-server_Solaris" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/Solaris</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.solaris" bundle="edt" default="/主机/Solaris" /></field>
				</obj>
				<obj name="cdevtype-server_HP-UX" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/HP-UX</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.hpux" bundle="edt" default="/主机/HP-UX" /></field>
				</obj>
				<obj name="cdevtype-server_Neokylin" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/Neokylin</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.neokylin" bundle="edt" default="/主机/中标麒麟" /></field>
				</obj>
				<obj name="cdevtype-server_Meditation" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/Meditation</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.meditation" bundle="edt" default="/主机/凝思磐石" /></field>
				</obj>
				<obj name="cdevtype-server_waterhouse" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/waterhouse</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.waterhouse" bundle="edt" default="/主机/普华" /></field>
				</obj>
				<obj name="cdevtype-server_Solaris" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/Solaris</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.solaris" bundle="edt" default="/主机/Solaris" /></field>
				</obj>
				<obj name="cdevtype-server_FileSystem" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/fileSystem</str></field>
					<field name="text"><i18n key="edt.data.devtype.host.fileSystem" bundle="edt" default="/主机/文件系统"/></field>
				</obj>
				<obj name="cdevtype-server_Inspur" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/inspur</str></field>
					<field name="text"><str>浪潮</str></field>
				</obj>
				<obj name="cdevtype-server_H3c" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/h3c</str></field>
					<field name="text"><str>华三</str></field>
				</obj>
				<obj name="cdevtype-server_Dell" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/server/dell</str></field>
					<field name="text"><str>戴尔</str></field>
				</obj>

				<!-- 网络设备 -->
				<obj name="cdevtype-network" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/network</str></field>
					<field name="text"><i18n key="edt.data.devtype.net" bundle="edt" default="/网络设备" /></field>
				</obj>
				<obj name="cdevtype-network_Switch" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/network/Switch</str></field>
					<field name="text"><i18n key="edt.data.devtype.net.switch" bundle="edt" default="/网络设备/交换机" /></field>
				</obj>
				<obj name="cdevtype-network_WLAN" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/network/WLAN</str></field>
					<field name="text"><i18n key="edt.data.devtype.net.wlan" bundle="edt" default="/网络设备/WLAN" /></field>
				</obj>
				<obj name="cdevtype-network_Router" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/network/Router</str></field>
					<field name="text"><i18n key="edt.data.devtype.net.router" bundle="edt" default="/网络设备/路由器" /></field>
				</obj>
				<obj name="cdevtype-network_LB" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/network/LB</str></field>
					<field name="text"><i18n key="edt.data.devtype.net.loadbalance" bundle="edt" default="/网络设备/负载均衡" /></field>
				</obj>
				<obj name="cdevtype-network_Hub" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/network/Hub</str></field>
					<field name="text"><i18n key="edt.data.devtype.net.hub" bundle="edt" default="/网络设备/Hub" /></field>
				</obj>

				<!-- 安全设备 -->

				<obj name="cdevtype-security" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec" bundle="edt" default="/安全设备" /></field>
				</obj>
				<obj name="cdevtype-security_FW" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/FW</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.fw" bundle="edt" default="/安全设备/防火墙" /></field>
				</obj>
				<obj name="cdevtype-security_UTM" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/UTM</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.utm" bundle="edt" default="/安全设备/安全防护网关" /></field>
				</obj>
				<obj name="cdevtype-security_WAF" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/WAF</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.waf" bundle="edt" default="/安全设备/Web应用安全网关" /></field>
				</obj>
				<obj name="cdevtype-security_IDS" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/IDS</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.ids" bundle="edt" default="/安全设备/入侵检测" /></field>
				</obj>
				<obj name="cdevtype-security_IPS" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/IPS</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.ips" bundle="edt" default="/安全设备/入侵防御" /></field>
				</obj>
				<obj name="cdevtype-security_VPN" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/VPN</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.vpn" bundle="edt" default="/安全设备/VPN" /></field>
				</obj>
				<obj name="cdevtype-security_Vulns" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Vulns</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.vulns" bundle="edt" default="/安全设备/漏洞扫描" /></field>
				</obj>
				<obj name="cdevtype-security_CVS" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/CVS</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.cvs" bundle="edt" default="/安全设备/配置核查" /></field>
				</obj>
				<obj name="cdevtype-security_MDS" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/MDS</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.mds" bundle="edt" default="/安全设备/恶意代码检测" /></field>
				</obj>
				<obj name="cdevtype-security_GAP" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/GAP</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.gatekeeper" bundle="edt" default="/安全设备/网闸" /></field>
				</obj>
				<obj name="cdevtype-security_Terminal" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Terminal</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.terminal" bundle="edt" default="/安全设备/终端安全管理" /></field>
				</obj>
				<obj name="cdevtype-security_SEG" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/SEG</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.seg" bundle="edt" default="/安全设备/邮件安全网关" /></field>
				</obj>
				<obj name="cdevtype-security_Bastion" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Bastion</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.bastion" bundle="edt" default="/安全设备/堡垒机" /></field>
				</obj>
				<obj name="cdevtype-security_PKI" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/PKI</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.pki" bundle="edt" default="/安全设备/身份认证" /></field>
				</obj>
				<obj name="cdevtype-security_WTP" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/WTP</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.wtp" bundle="edt" default="/安全设备/网页防篡改" /></field>
				</obj>
				<obj name="cdevtype-security_Detector" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Detector</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.detector" bundle="edt" default="/安全设备/流量检测设备" /></field>
				</obj>
				<obj name="cdevtype-security_Guard" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Guard</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.guard" bundle="edt" default="/安全设备/流量清洗设备" /></field>
				</obj>
				<obj name="cdevtype-security_AV" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/AV</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.av" bundle="edt" default="/安全设备/防病毒系统" /></field>
				</obj>
				<obj name="cdevtype-security_SOC" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/SOC</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.soc" bundle="edt" default="/安全设备/安全管理平台" /></field>
				</obj>
				<obj name="cdevtype-security_NBA" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/NBA</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.nba" bundle="edt" default="/安全设备/网络行为分析" /></field>
				</obj>
				<obj name="cdevtype-security_SA" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/SA</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.sa" bundle="edt" default="/安全设备/安全审计" /></field>
				</obj>
				<obj name="cdevtype-security_IBA" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/IBA</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.iba" bundle="edt" default="/安全设备/上网行为审计" /></field>
				</obj>
				<obj name="cdevtype-security_LA" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/LA</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.la" bundle="edt" default="/安全设备/日志审计" /></field>
				</obj>
				<obj name="cdevtype-security_DBA" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/DBA</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.dba" bundle="edt" default="/安全设备/数据库审计" /></field>
				</obj>
				<obj name="cdevtype-security_Honeypot" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Honeypot</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.honeypot" bundle="edt" default="/安全设备/蜜罐系统" /></field>
				</obj>
				<obj name="cdevtype-security_Sandbox" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/Sandbox</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.sandbox" bundle="edt" default="/安全设备/沙箱系统" /></field>
				</obj>
				<obj name="cdevtype-security_AST" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/AST</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.ast" bundle="edt" default="/安全设备/应用安全测试" /></field>
				</obj>
				<obj name="cdevtype-security_DLP" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/DLP</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.dlp" bundle="edt" default="/安全设备/数据防泄密系统" /></field>
				</obj>
				<obj name="cdevtype-security_DM" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/security/DM</str></field>
					<field name="text"><i18n key="edt.data.devtype.sec.datamasking" bundle="edt" default="/安全设备/数据脱敏系统" /></field>
				</obj>

				<!-- 虚拟化 -->

				<obj name="cdevtype-virtual" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual" bundle="edt" default="/虚拟化设备" /></field>
				</obj>
				<obj name="cdevtype-virtual_ESXi" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/ESXi</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.esxi" bundle="edt" default="/虚拟化设备/ESX(i)" /></field>
				</obj>
				<obj name="cdevtype-virtual_vCenter" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/vCenter</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.xcenter" bundle="edt" default="/虚拟化设备/vCenter" /></field>
				</obj>
				<obj name="cdevtype-virtual_vm" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/vm</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.vm" bundle="edt" default="/虚拟化设备/虚拟主机" /></field>
				</obj>
				<obj name="cdevtype-virtual_Xen" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/Xen</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.xen" bundle="edt" default="/虚拟化设备/Xen" /></field>
				</obj>
				<obj name="cdevtype-virtual_Xenserver" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/Xenserver</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.xenserver" bundle="edt" default="/虚拟化设备/XenServer" /></field>
				</obj>
				<obj name="cdevtype-virtual_Hyper-V" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/Hyper-V</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.hyper-v" bundle="edt" default="/虚拟化设备/Hyper-V" /></field>
				</obj>

				<obj name="cdevtype-virtual_KVM" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/KVM</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.kvm" bundle="edt" default="/虚拟化设备/KVM" /></field>
				</obj>
				<obj name="cdevtype-virtual_KVM-VM" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/KVM/VM</str></field>
					<field name="text"><i18n key="edt.data.devtype.kvm.vm" bundle="edt" default="/虚拟化设备/KVM/VM" /></field>
				</obj>
				<obj name="cdevtype-virtual_KVM-HOST" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/KVM/HOST</str></field>
					<field name="text"><i18n key="edt.data.devtype.kvm.host" bundle="edt" default="虚拟化设备/KVM/宿主机" /></field>
				</obj>

				<obj name="cdevtype-virtual_FusionSphere" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/FusionSphere</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.fusionsphere" bundle="edt" default="/虚拟化设备/FusionSphere" /></field>
				</obj>
				<obj name="cdevtype-virtual_CloudCell" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/virtual/CloudCell</str></field>
					<field name="text"><i18n key="edt.data.devtype.visual.cloudcell" bundle="edt" default="/虚拟化设备/CloudCell" /></field>
				</obj>


				<!-- 数据库 -->

				<obj name="cdevtype-database" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database</str></field>
					<field name="text"><i18n key="edt.data.result.database" bundle="edt" default="/数据库" /></field>
				</obj>
				<obj name="cdevtype-database_oracle" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Oracle</str></field>
					<field name="text"><i18n key="edt.data.result.database.oracle" bundle="edt" default="/数据库/Oracle" /></field>
				</obj>
				<obj name="cdevtype-database_Cassandra" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Cassandra</str></field>
					<field name="text"><i18n key="edt.data.result.database.oracle" bundle="edt" default="/数据库/Cassandra" /></field>
				</obj>
				<obj name="cdevtype-database_sqlserver" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Sqlserver</str></field>
					<field name="text"><i18n key="edt.data.result.database.sqlserver" bundle="edt" default="/数据库/SQLServer" /></field>
				</obj>
				<obj name="cdevtype-database_mysql" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Mysql</str></field>
					<field name="text"><i18n key="edt.data.result.database.mysql" bundle="edt" default="/数据库/MySQL" /></field>
				</obj>
				<obj name="cdevtype-database_db2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/DB2</str></field>
					<field name="text"><i18n key="edt.data.result.database.db2" bundle="edt" default="/数据库/DB2" /></field>
				</obj>
				<obj name="cdevtype-database_sybase" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Sybase</str></field>
					<field name="text"><i18n key="edt.data.result.database.sybase" bundle="edt" default="/数据库/Sybase" /></field>
				</obj>
				<obj name="cdevtype-database_Informix" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Informix</str></field>
					<field name="text"><i18n key="edt.data.result.database.informix" bundle="edt" default="/数据库/Informix" /></field>
				</obj>
				<obj name="cdevtype-database_PostgreSQL" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/PostgreSQL</str></field>
					<field name="text"><i18n key="edt.data.result.database.postgresql" bundle="edt" default="/数据库/PostgreSQL" /></field>
				</obj>
				<obj name="cdevtype-database_MongoDB" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/MongoDB</str></field>
					<field name="text"><i18n key="edt.data.result.database.mongodb" bundle="edt" default="/数据库/MongoDB" /></field>
				</obj>
				<obj name="cdevtype-database_HBase" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/HBase</str></field>
					<field name="text"><i18n key="edt.data.result.database.hbase" bundle="edt" default="/数据库/HBase" /></field>
				</obj>
				<obj name="cdevtype-database_Hive" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Hive</str></field>
					<field name="text"><i18n key="edt.data.result.database.hive" bundle="edt" default="/数据库/Hive" /></field>
				</obj>
				<obj name="cdevtype-database_Dameng" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Dameng</str></field>
					<field name="text"><i18n key="edt.data.result.database.dameng" bundle="edt" default="/数据库/武汉达梦" /></field>
				</obj>
				<obj name="cdevtype-database_Kingbase" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Kingbase</str></field>
					<field name="text"><i18n key="edt.data.result.database.kingbase" bundle="edt" default="/数据库/人大金仓" /></field>
				</obj>
				<obj name="cdevtype-database_shentong" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Shentong</str></field>
					<field name="text"><i18n key="edt.data.result.database.shentong" bundle="edt" default="/数据库/神舟通用" /></field>
				</obj>
				<obj name="cdevtype-database_Gbase" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/database/Gbase</str></field>
					<field name="text"><i18n key="edt.data.result.database.gbase" bundle="edt" default="/数据库/南大通用" /></field>
				</obj>

				<!-- 中间件 -->

				<obj name="cdevtype-middleware" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware</str></field>
					<field name="text"><i18n key="edt.data.result.middleware" bundle="edt" default="/中间件" /></field>
				</obj>
				<obj name="cdevtype-middleware_Websphere" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Websphere</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.websphere" bundle="edt" default="/中间件/WebSphere" /></field>
				</obj>
				<obj name="cdevtype-middleware_Weblogic" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Weblogic</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.weblogic" bundle="edt" default="/中间件/WebLogic" /></field>
				</obj>
				<obj name="cdevtype-middleware_Tomcat" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Tomcat</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.tomcat" bundle="edt" default="/中间件/Tomcat" /></field>
				</obj>
				<obj name="cdevtype-middleware_IIS" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/IIS</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.iis" bundle="edt" default="/中间件/IIS" /></field>
				</obj>
				<obj name="cdevtype-middleware_Apache" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Apache</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.apache" bundle="edt" default="/中间件/Apache" /></field>
				</obj>
				<obj name="cdevtype-middleware_Bind" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Bind</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.bind" bundle="edt" default="/中间件/Bind" /></field>
				</obj>
				<obj name="cdevtype-middleware_Jboss" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Jboss</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.jboss" bundle="edt" default="/中间件/JBoss" /></field>
				</obj>
				<obj name="cdevtype-middleware_TongWeb" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Tongweb</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.tongweb" bundle="edt" default="/中间件/Tong Web" /></field>
				</obj>
				<obj name="cdevtype-middleware_Domino" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Domino</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.domino" bundle="edt" default="/中间件/Domino" /></field>
				</obj>
				<obj name="cdevtype-middleware_Resin" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Resin</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.resin" bundle="edt" default="/中间件/Resin" /></field>
				</obj>
				<obj name="cdevtype-middleware_Nginx" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Nginx</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.nginx" bundle="edt" default="/中间件/Nginx" /></field>
				</obj>
				<obj name="cdevtype-middleware_Tuxedo" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Tuxedo</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.tuxedo" bundle="edt" default="/中间件/Tuxedo" /></field>
				</obj>
				<obj name="cdevtype-middleware_Webspheremq" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Webspheremq</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.webspheremq" bundle="edt" default="/中间件/webspheremq" /></field>
				</obj>
				<obj name="cdevtype-middleware_RabbitMq" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Rabbitmq</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.rabbitmq" bundle="edt" default="/中间件/RabbitMq" /></field>
				</obj>
				<obj name="cdevtype-middleware_ActiveMQ" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Activemq</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.activemq" bundle="edt" default="/中间件/ActiveMQ" /></field>
				</obj>
				<obj name="cdevtype-middleware_TongLinkQ" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/middleware/Tonglinkq</str></field>
					<field name="text"><i18n key="edt.data.result.middleware.tonglinkq" bundle="edt" default="/中间件/TongLinkQ" /></field>
				</obj>
				<!-- 存储设备 -->

				<obj name="cdevtype-storage" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage" bundle="edt" default="/存储设备" /></field>
				</obj>
				<obj name="cdevtype-storage_EMC" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage/EMC</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage.emc" bundle="edt" default="/存储设备/EMC" /></field>
				</obj>
				<obj name="cdevtype-storage_Netapp" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage/Netapp</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage.netapp" bundle="edt" default="/存储设备/Netapp" /></field>
				</obj>
				<obj name="cdevtype-storage_SAN" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage/SAN</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage.san" bundle="edt" default="/存储设备/SAN" /></field>
				</obj>
				<obj name="cdevtype-storage_NAS" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage/NAS</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage.nas" bundle="edt" default="/存储设备/NAS" /></field>
				</obj>
				<obj name="cdevtype-storage_RAID" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage/RAID</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage.raid" bundle="edt" default="/存储设备/磁盘阵列" /></field>
				</obj>
				<obj name="cdevtype-storage_HONGSHAN" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/storage/HONGSHAN</str></field>
					<field name="text"><i18n key="edt.data.devtype.storage.hongshan" bundle="edt" default="/存储设备/宏杉存储" /></field>
				</obj>
				<!-- 业务系统 -->
				<obj name="cdevtype-business" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/business</str></field>
					<field name="text"><i18n key="edt.data.devtype.biz" bundle="edt" default="/业务系统" /></field>
				</obj>
				<!-- 应用系统 -->

				<obj name="cdevtype-appserver" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/appserver</str></field>
					<field name="text"><i18n key="edt.data.devtype.app" bundle="edt" default="/应用系统" /></field>
				</obj>
				<obj name="cdevtype-business_EMAIL" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/appserver/EMAIL</str></field>
					<field name="text"><i18n key="edt.data.devtype.app.mail" bundle="edt" default="/应用系统/邮件" /></field>
				</obj>
				<obj name="cdevtype-business_ADC" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/appserver/ADC</str></field>
					<field name="text"><i18n key="edt.data.devtype.app.adc" bundle="edt" default="/应用系统/应用交付系统" /></field>
				</obj>
				<obj name="cdevtype-business_Website" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/appserver/Webserver</str></field>
					<field name="text"><i18n key="edt.data.devtype.app.webserver" bundle="edt" default="/应用系统/WebServer" /></field>
				</obj>
				<obj name="cdevtype-business_ERP" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/appserver/ERP</str></field>
					<field name="text"><i18n key="edt.data.devtype.app.erp" bundle="edt" default="/应用系统/ERP" /></field>
				</obj>
				<obj name="cdevtype-business_OA" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/appserver/OA</str></field>
					<field name="text"><i18n key="edt.data.devtype.app.oa" bundle="edt" default="/应用系统/OA" /></field>
				</obj>

				<!-- 机房设备 -->


				<obj name="cdevtype-phy" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/phy</str></field>
					<field name="text"><i18n key="edt.data.devtype.lab" bundle="edt" default="/机房设备" /></field>
				</obj>
				<obj name="cdevtype-phy_safeguard" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/phy/SafeGuard</str></field>
					<field name="text"><i18n key="edt.data.devtype.lab.security" bundle="edt" default="/机房设备/安防" /></field>
				</obj>
				<obj name="cdevtype-phy_power" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/phy/Power</str></field>
					<field name="text"><i18n key="edt.data.devtype.lab.power" bundle="edt" default="/机房设备/动力" /></field>
				</obj>
				<obj name="cdevtype-phy_env" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/phy/Env</str></field>
					<field name="text"><i18n key="edt.data.devtype.lab.environment" bundle="edt" default="/机房设备/环境" /></field>
				</obj>
				<!-- 工控设备 -->
				<obj name="cdevtype-gongKong" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/gongKong</str></field>
					<field name="text"><i18n key="edt.data.devtype.gongKong" bundle="edt" default="/工控设备" /></field>
				</obj>
				<obj name="cdevtype-gongKong_hirschmann" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/gongKong/Hirschmann</str></field>
					<field name="text"><i18n key="edt.data.devtype.gongKong.hirschmann" bundle="edt" default="/工控设备/赫斯曼交换机" /></field>
				</obj>

				<!-- 大数据 -->
				<obj name="cdevtype-bigdata" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata" bundle="edt" default="/大数据" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Spark" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Spark</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Spark" bundle="edt" default="/大数据/Spark" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Yarn" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Yarn</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Yarn" bundle="edt" default="/大数据/Yarn" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Hdfs" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Hdfs</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Hdfs" bundle="edt" default="/大数据/Hdfs" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Hbase" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Hbase</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Hbase" bundle="edt" default="/大数据/Hbase" /></field>
				</obj>
				<obj name="cdevtype-bigdata_ES" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/ES</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.ES" bundle="edt" default="/大数据/Elasticsearch" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Hive" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Hive</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Hive" bundle="edt" default="/大数据/Hive" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Zookeeper" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Zookeeper</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Zooleeper" bundle="edt" default="/大数据/Zookeeper" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Storm" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Storm</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Storm" bundle="edt" default="/大数据/Storm" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Kafka" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Kafka</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Kafka" bundle="edt" default="/大数据/Kafka" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Flume" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Flume</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Flume" bundle="edt" default="/大数据/Flume" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Impala" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Impala</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Impala" bundle="edt" default="/大数据/Impala" /></field>
				</obj>
				<obj name="cdevtype-bigdata_Hadoop" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/bigdata/Hadoop</str></field>
					<field name="text"><i18n key="edt.data.devtype.bigdata.Hadoop" bundle="edt" default="/大数据/Hadoop" /></field>
				</obj>

				<!-- openstack -->
				<obj name="cdevtype-openstack" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack" bundle="edt" default="/openstack" /></field>
				</obj>
				<obj name="cdevtype-openstack_Manager" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack/Manager</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack.Manager" bundle="edt" default="/openstack/Manager" /></field>
				</obj>
				<obj name="cdevtype-openstack_Nova" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack/Nova</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack.Nova" bundle="edt" default="/openstack/Nova" /></field>
				</obj>
				<obj name="cdevtype-openstack_Cinder" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack/Cinder</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack.Cinder" bundle="edt" default="/openstack/Cinder" /></field>
				</obj>
				<obj name="cdevtype-openstack_Keystone" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack/Keystone</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack.Keystone" bundle="edt" default="/openstack/Keystone" /></field>
				</obj>
				<obj name="cdevtype-openstack_Neutron" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack/Neutron</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack.Neutron" bundle="edt" default="/openstack/Neutron" /></field>
				</obj>
				<obj name="cdevtype-openstack_Dashboard" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/openstack/Dashboard</str></field>
					<field name="text"><i18n key="edt.data.devtype.openstack.Dashboard" bundle="edt" default="/openstack/Dashboard" /></field>
				</obj>

				<!-- 其它设备和系统 -->

				<obj name="cdevtype-other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/otherdev</str></field>
					<field name="text"><i18n key="edt.data.devtype.other" bundle="edt" default="/其它设备和系统" /></field>
				</obj>
				<obj name="cdevtype-other_printer" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/otherdev/Printer</str></field>
					<field name="text"><i18n key="edt.data.devtype.other.netprinter" bundle="edt" default="/其它设备和系统/网络打印机" /></field>
				</obj>


				<!-- 系统自身监控 -->
				<obj name="cdevtype-other_printer" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>/SelfMonitoring</str></field>
					<field name="text"><i18n key="edt.data.devtype.SelfMonitoring" bundle="edt" default="/系统自身" /></field>
				</obj>

			</list>
		</args>
	</obj>

	<obj name="ifunction" type="java.util.ArrayList">
		<args>
			<list>
				<obj name="ceventtype-acc1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>0</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.cup_service" bundle="edt" default="CUP服务" /></field>
				</obj>
				<obj name="ceventtype-acc2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.read_variable" bundle="edt" default="读变量" /></field>
				</obj>
				<obj name="ceventtype-acc3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.write_variable" bundle="edt" default="写变量" /></field>
				</obj>
				<obj name="ceventtype-acc4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>26</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.request_download" bundle="edt" default="请求下载" /></field>
				</obj>
				<obj name="ceventtype-acc5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>27</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.download" bundle="edt" default="下载块" /></field>
				</obj>
				<obj name="ceventtype-acc6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>28</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.download_end" bundle="edt" default="下载结束" /></field>
				</obj>
				<obj name="ceventtype-acc7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>29</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.download_start" bundle="edt" default="开始上传" /></field>
				</obj>
				<obj name="ceventtype-acc8" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.upload" bundle="edt" default="上传" /></field>
				</obj>
				<obj name="ceventtype-acc9" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.upload_end" bundle="edt" default="结束上传" /></field>
				</obj>
				<obj name="ceventtype-acc10" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>40</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.plc_control" bundle="edt" default="PLC控制" /></field>
				</obj>
				<obj name="ceventtype-acc11" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>41</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.plc_stop" bundle="edt" default="PLC停止" /></field>
				</obj>
				<obj name="ceventtype-acc12" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>240</str></field>
					<field name="text"><i18n key="edt.data.collecttype.s7.set_communication" bundle="edt" default="设置通信" /></field>
				</obj>
				<obj name="ceventtype-acc13" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>0</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.handled" bundle="edt" default="确认" /></field>
				</obj>
				<obj name="ceventtype-acc14" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.read" bundle="edt" default="读" /></field>
				</obj>
				<obj name="ceventtype-acc15" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.write" bundle="edt" default="写" /></field>
				</obj>
				<obj name="ceventtype-acc16" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.choose" bundle="edt" default="选择" /></field>
				</obj>
				<obj name="ceventtype-acc17" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.operation" bundle="edt" default="操作" /></field>
				</obj>
				<obj name="ceventtype-acc18" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.online_operation" bundle="edt" default="直接操作" /></field>
				</obj>
				<obj name="ceventtype-acc19" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.online_operation_nak1" bundle="edt" default="直接操作（无确认）" /></field>
				</obj>
				<obj name="ceventtype-acc20" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.freeze" bundle="edt" default="立即冻结 " /></field>
				</obj>
				<obj name="ceventtype-acc21" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.freeze_nak" bundle="edt" default="立即冻结（无确认）" /></field>
				</obj>
				<obj name="ceventtype-acc22" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>9</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.freeze_clean" bundle="edt" default="冻结与清除" /></field>
				</obj>
				<obj name="ceventtype-acc23" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.freeze_clean_nak" bundle="edt" default="冻结与清除（无确认）" /></field>
				</obj>
				<obj name="ceventtype-acc24" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.freeze_time" bundle="edt" default="有时间的冻结" /></field>
				</obj>
				<obj name="ceventtype-acc25" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>12</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.freeze_time_nak" bundle="edt" default="有时间的冻结（无确认）" /></field>
				</obj>
				<obj name="ceventtype-acc26" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>13</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.cold_restart" bundle="edt" default="冷再启动" /></field>
				</obj>
				<obj name="ceventtype-acc27" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>14</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.hot_restarte" bundle="edt" default="热再启动" /></field>
				</obj>
				<obj name="ceventtype-acc28" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>15</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.initialize_default" bundle="edt" default="将数据初始化到缺省值" /></field>
				</obj>
				<obj name="ceventtype-acc29" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>16</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.Initialize_application" bundle="edt" default="初始化应用程序" /></field>
				</obj>
				<obj name="ceventtype-acc30" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>17</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.start_application" bundle="edt" default="启动应用程序" /></field>
				</obj>
				<obj name="ceventtype-acc31" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>18</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.stop_application" bundle="edt" default="停止应用程序" /></field>
				</obj>
				<obj name="ceventtype-acc32" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>19</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.save" bundle="edt" default="保存组态" /></field>
				</obj>
				<obj name="ceventtype-acc33" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.allow_nrm" bundle="edt" default="允许非请求报文" /></field>
				</obj>
				<obj name="ceventtype-acc34" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>21</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.stop_nrm" bundle="edt" default="停用非请求报文" /></field>
				</obj>
				<obj name="ceventtype-acc35" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>22</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.allocation_level" bundle="edt" default="分配等级" /></field>
				</obj>
				<obj name="ceventtype-acc36" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>23</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.delay_measurement" bundle="edt" default="延迟测量" /></field>
				</obj>
				<obj name="ceventtype-acc37" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>24</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.record_current_time" bundle="edt" default="记录当前时间" /></field>
				</obj>
				<obj name="ceventtype-acc38" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>25</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.open_file" bundle="edt" default="打开文件" /></field>
				</obj>
				<obj name="ceventtype-acc39" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>26</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.close_file" bundle="edt" default="关闭文件" /></field>
				</obj>
				<obj name="ceventtype-acc40" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>27</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.remove_file" bundle="edt" default="删除文件" /></field>
				</obj>
				<obj name="ceventtype-acc41" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>28</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.get_fle" bundle="edt" default="获取文件信息" /></field>
				</obj>
				<obj name="ceventtype-acc42" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>29</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.DER" bundle="edt" default="认证文件" /></field>
				</obj>
				<obj name="ceventtype-acc43" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.abort_file" bundle="edt" default="中止文件" /></field>
				</obj>
				<obj name="ceventtype-acc44" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.activation_configuration" bundle="edt" default="激活配置" /></field>
				</obj>
				<obj name="ceventtype-acc45" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>32</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.RFC" bundle="edt" default="认证请求" /></field>
				</obj>
				<obj name="ceventtype-acc46" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>33</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.authentication_error" bundle="edt" default="认证错误" /></field>
				</obj>
				<obj name="ceventtype-acc47" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>129</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.respond" bundle="edt" default="响应" /></field>
				</obj>
				<obj name="ceventtype-acc48" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>130</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.unsolicited_response" bundle="edt" default="非请求响应" /></field>
				</obj>
				<obj name="ceventtype-acc49" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>131</str></field>
					<field name="text"><i18n key="edt.data.collecttype.dnp3.authentication_response" bundle="edt" default="认证响应" /></field>
				</obj>
				<obj name="ceventtype-acc50" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.timescale" bundle="edt" default="带时标的报文" /></field>
				</obj>
				<obj name="ceventtype-acc51" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.timescale_relative" bundle="edt" default="具有相对时间的带时标的报文" /></field>
				</obj>
				<obj name="ceventtype-acc52" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.measured_I" bundle="edt" default="被测值I" /></field>
				</obj>
				<obj name="ceventtype-acc53" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.measured_timescal" bundle="edt" default="具有相对时间的带时标的被测值" /></field>
				</obj>
				<obj name="ceventtype-acc54" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.identification" bundle="edt" default="标识" /></field>
				</obj>
				<obj name="ceventtype-acc55" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.synchronization" bundle="edt" default="时间同步" /></field>
				</obj>
				<obj name="ceventtype-acc56" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.total_query" bundle="edt" default="总查询(总召唤)" /></field>
				</obj>
				<obj name="ceventtype-acc57" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.total_query_stop" bundle="edt" default="总查询(总召唤)终止" /></field>
				</obj>
				<obj name="ceventtype-acc58" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>9</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.measured_II" bundle="edt" default="被测值II" /></field>
				</obj>
				<obj name="ceventtype-acc59" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.universal_categorical_data" bundle="edt" default="通用分类数据" /></field>
				</obj>
				<obj name="ceventtype-acc60" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.universal_categorical_identification" bundle="edt" default="通用分类标识" /></field>
				</obj>
				<obj name="ceventtype-acc61" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.order" bundle="edt" default="一般命令" /></field>
				</obj>
				<obj name="ceventtype-acc62" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>21</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.universal_categorical_order" bundle="edt" default="通用分类命令" /></field>
				</obj>
				<obj name="ceventtype-acc63" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>23</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.recorded_disturbance" bundle="edt" default="被记录的扰动表" /></field>
				</obj>
				<obj name="ceventtype-acc64" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>24</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.command" bundle="edt" default="扰动数据传输的命令" /></field>
				</obj>
				<obj name="ceventtype-acc65" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>25</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.recognition" bundle="edt" default="扰动数据传输的认可" /></field>
				</obj>
				<obj name="ceventtype-acc66" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>26</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.ready" bundle="edt" default="扰动数据传输准备就绪" /></field>
				</obj>
				<obj name="ceventtype-acc67" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>27</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.record_ready" bundle="edt" default="被记录的通道传输准备就绪" /></field>
				</obj>
				<obj name="ceventtype-acc68" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>28</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.state_ready" bundle="edt" default="带标志的状态变位传输准备就绪" /></field>
				</obj>
				<obj name="ceventtype-acc69" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>29</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.state_sign" bundle="edt" default="传送带标志的状态变位" /></field>
				</obj>
				<obj name="ceventtype-acc70" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.transmission_disturbance" bundle="edt" default="传送扰动值" /></field>
				</obj>
				<obj name="ceventtype-acc71" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec103.transfer_end" bundle="edt" default="传送结束" /></field>
				</obj>
				<obj name="ceventtype-acc72" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.single" bundle="edt" default="单点信息（总召唤遥信、变位遥信）" /></field>
				</obj>
				<obj name="ceventtype-acc73" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.single_timescale" bundle="edt" default="带时标单点信息（SOE事项）" /></field>
				</obj>
				<obj name="ceventtype-acc74" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.double" bundle="edt" default="双点信息" /></field>
				</obj>
				<obj name="ceventtype-acc75" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.double_timescale" bundle="edt" default="带时标双点信息" /></field>
				</obj>
				<obj name="ceventtype-acc76" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.step" bundle="edt" default="步位置信息" /></field>
				</obj>
				<obj name="ceventtype-acc77" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.step_timescale" bundle="edt" default="带时标步位置信息" /></field>
				</obj>
				<obj name="ceventtype-acc78" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.32bit" bundle="edt" default="32比特串" /></field>
				</obj>
				<obj name="ceventtype-acc79" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.32bit_timescale" bundle="edt" default="带时标32比特串" /></field>
				</obj>
				<obj name="ceventtype-acc80" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>9</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.measured_normalized" bundle="edt" default="测量值，规一化值（越限遥测）" /></field>
				</obj>
				<obj name="ceventtype-acc81" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>10</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.measured_timescale" bundle="edt" default="测量值，带时标规一化值" /></field>
				</obj>
				<obj name="ceventtype-acc82" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.measured_scaling" bundle="edt" default="测量值，标度化值" /></field>
				</obj>
				<obj name="ceventtype-acc83" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>12</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.measured_scaling_timescale" bundle="edt" default="测量值，带时标标度化值" /></field>
				</obj>
				<obj name="ceventtype-acc84" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>13</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.measured_float" bundle="edt" default="测量值，短浮点数" /></field>
				</obj>
				<obj name="ceventtype-acc85" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>14</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.measured_float_timescale" bundle="edt" default="测量值，带时标短浮点数" /></field>
				</obj>
				<obj name="ceventtype-acc86" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>15</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.cumulant" bundle="edt" default="累计量（电度量）" /></field>
				</obj>
				<obj name="ceventtype-acc87" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>16</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.cumulant_timescale" bundle="edt" default="带时标累计量" /></field>
				</obj>
				<obj name="ceventtype-acc88" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>17</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.timescale_relay" bundle="edt" default="带时标继电保护装置事件" /></field>
				</obj>
				<obj name="ceventtype-acc89" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>18</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.timescale_start" bundle="edt" default="带时标继电保护装置成组启动事件" /></field>
				</obj>
				<obj name="ceventtype-acc90" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>19</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.timescale_out" bundle="edt" default="带时标继电保护装置成组输出电路信息" /></field>
				</obj>
				<obj name="ceventtype-acc91" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.group_single" bundle="edt" default="具有状态变位检出的成组单点信息" /></field>
				</obj>
				<obj name="ceventtype-acc92" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>21</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.without_normalized" bundle="edt" default="测量值，不带品质描述的规一化值" /></field>
				</obj>
				<obj name="ceventtype-acc93" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>30</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_single" bundle="edt" default="带时标CP56TimE2A的单点信息" /></field>
				</obj>
				<obj name="ceventtype-acc94" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>31</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_double" bundle="edt" default="带时标CP56TimE2A的双点信息" /></field>
				</obj>
				<obj name="ceventtype-acc95" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>32</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_step" bundle="edt" default="带时标CP56TimE2A的步位信息" /></field>
				</obj>
				<obj name="ceventtype-acc96" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>33</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_32" bundle="edt" default="带时标CP56TimE2A的32位串" /></field>
				</obj>
				<obj name="ceventtype-acc97" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>34</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_normalization" bundle="edt" default="带时标CP56TimE2A的规一化测量值" /></field>
				</obj>
				<obj name="ceventtype-acc98" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>35</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_scaling" bundle="edt" default="测量值，带时标CP56TimE2A的标度化值" /></field>
				</obj>
				<obj name="ceventtype-acc99" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>36</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_float" bundle="edt" default="测量值，带时标CP56TimE2A的短浮点数" /></field>
				</obj>
				<obj name="ceventtype-acc100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>37</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_cumulant" bundle="edt" default="带时标CP56TimE2A的累计值" /></field>
				</obj>
				<obj name="ceventtype-acc101" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>38</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_relay" bundle="edt" default="带时标CP56TimE2A的继电保护装置事件" /></field>
				</obj>
				<obj name="ceventtype-acc102" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>39</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_start" bundle="edt" default="带时标CP56TimE2A的成组继电保护装置成组启动事件" /></field>
				</obj>
				<obj name="ceventtype-acc103" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>40</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_out" bundle="edt" default="带时标CP56TimE2A的继电保护装置成组输出电路信息" /></field>
				</obj>
				<obj name="ceventtype-acc104" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>45</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.single_order" bundle="edt" default="单命令" /></field>
				</obj>
				<obj name="ceventtype-acc105" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>46</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.double_order" bundle="edt" default="双命令" /></field>
				</obj>
				<obj name="ceventtype-acc106" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>47</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.lift_order" bundle="edt" default="升降命令" /></field>
				</obj>
				<obj name="ceventtype-acc107" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>48</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.setting_normalized" bundle="edt" default="设定值命令，规一化值" /></field>
				</obj>
				<obj name="ceventtype-acc108" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>49</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.setting_scaling" bundle="edt" default="设定值命令，标度化值" /></field>
				</obj>
				<obj name="ceventtype-acc109" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>50</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.setting_float" bundle="edt" default="设定值命令，短浮点数" /></field>
				</obj>
				<obj name="ceventtype-acc110" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>51</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.setting_32" bundle="edt" default="32比特串" /></field>
				</obj>
				<obj name="ceventtype-acc111" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>58</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_single_order" bundle="edt" default="带时标CP56TimE2A的单命令" /></field>
				</obj>
				<obj name="ceventtype-acc112" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>59</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_double_order" bundle="edt" default="带时标CP56TimE2A的双命令" /></field>
				</obj>
				<obj name="ceventtype-acc113" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>60</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_lift_order" bundle="edt" default="带时标CP56TimE2A的升降命令" /></field>
				</obj>
				<obj name="ceventtype-acc114" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>61</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_setting_normalized" bundle="edt" default="带时标CP56TimE2A的设定值命令，规一化值" /></field>
				</obj>
				<obj name="ceventtype-acc115" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>62</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_setting_scaling" bundle="edt" default="带时标CP56TimE2A的设定值命令，标度化值" /></field>
				</obj>
				<obj name="ceventtype-acc116" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>63</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_setting_float" bundle="edt" default="带时标CP56TimE2A的设定值命令，短浮点数" /></field>
				</obj>
				<obj name="ceventtype-acc117" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>64</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_setting_32" bundle="edt" default="带时标CP56TimE2A的32比特串" /></field>
				</obj>
				<obj name="ceventtype-acc118" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>70</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.initialization" bundle="edt" default="初始化结束" /></field>
				</obj>
				<obj name="ceventtype-acc119" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>100</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.total_query" bundle="edt" default="总召唤命令" /></field>
				</obj>
				<obj name="ceventtype-acc120" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>101</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.pulse" bundle="edt" default="电能脉冲召唤命令" /></field>
				</obj>
				<obj name="ceventtype-acc121" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>102</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.read" bundle="edt" default="读命令" /></field>
				</obj>
				<obj name="ceventtype-acc122" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>103</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.synchronization" bundle="edt" default="时钟同步命令" /></field>
				</obj>
				<obj name="ceventtype-acc123" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>104</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.test" bundle="edt" default="测试命令" /></field>
				</obj>
				<obj name="ceventtype-acc124" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>105</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.reset" bundle="edt" default="复位进程命令" /></field>
				</obj>
				<obj name="ceventtype-acc125" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>106</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.delayed" bundle="edt" default="延时传输命令" /></field>
				</obj>
				<obj name="ceventtype-acc126" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>107</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.CP56TimE2A_test" bundle="edt" default="带时标CP56TimE2A的测试命令" /></field>
				</obj>
				<obj name="ceventtype-acc127" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>110</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.parameter_normalized" bundle="edt" default="测量值参数，规一化值" /></field>
				</obj>
				<obj name="ceventtype-acc128" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>111</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.parameter_scaling" bundle="edt" default="测量值参数，标度化值" /></field>
				</obj>
				<obj name="ceventtype-acc129" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>112</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.parameter_float" bundle="edt" default="测量值参数，短浮点数" /></field>
				</obj>
				<obj name="ceventtype-acc130" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>113</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.activation" bundle="edt" default="参数激活" /></field>
				</obj>
				<obj name="ceventtype-acc131" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>120</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.document_ready" bundle="edt" default="文件准备好" /></field>
				</obj>
				<obj name="ceventtype-acc132" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>121</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.festival_ready" bundle="edt" default="节已准备好" /></field>
				</obj>
				<obj name="ceventtype-acc133" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>122</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.call" bundle="edt" default="召唤目录，选择文件，召唤文件，召唤节" /></field>
				</obj>
				<obj name="ceventtype-acc134" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>123</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.last" bundle="edt" default="最后的节，最后的度" /></field>
				</obj>
				<obj name="ceventtype-acc135" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>124</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.handled" bundle="edt" default="确认文件，确认节" /></field>
				</obj>
				<obj name="ceventtype-acc136" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>125</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.paragraph" bundle="edt" default="段" /></field>
				</obj>
				<obj name="ceventtype-acc137" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>126</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.catalog" bundle="edt" default="目录｛空白或×，只在监视（标准）方向有效｝" /></field>
				</obj>
				<obj name="ceventtype-acc138" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>127</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec104.archive_file" bundle="edt" default="查询日志 - 需要归档文件" /></field>
				</obj>
				<obj name="ceventtype-acc139" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>1</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_coil" bundle="edt" default="读线圈状态" /></field>
				</obj>
				<obj name="ceventtype-acc140" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>2</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_dispersed" bundle="edt" default="读离散输入量" /></field>
				</obj>
				<obj name="ceventtype-acc141" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>3</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_register" bundle="edt" default="读保持寄存器" /></field>
				</obj>
				<obj name="ceventtype-acc142" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_input" bundle="edt" default="读输入寄存器" /></field>
				</obj>
				<obj name="ceventtype-acc143" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>5</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.write_coil" bundle="edt" default="写单个线圈" /></field>
				</obj>
				<obj name="ceventtype-acc144" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>6</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.write_register" bundle="edt" default="写单个线圈" /></field>
				</obj>
				<obj name="ceventtype-acc145" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>7</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_abnormal" bundle="edt" default="读不正常状态" /></field>
				</obj>
				<obj name="ceventtype-acc146" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>8</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.diagnosis" bundle="edt" default="诊断" /></field>
				</obj>
				<obj name="ceventtype-acc147" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>11</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_counter" bundle="edt" default="读通讯事件计数器" /></field>
				</obj>
				<obj name="ceventtype-acc148" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>12</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.resd_record" bundle="edt" default="读通讯事件记录" /></field>
				</obj>
				<obj name="ceventtype-acc149" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>15</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.write_multiple_coil" bundle="edt" default="写多个线圈" /></field>
				</obj>
				<obj name="ceventtype-acc150" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>16</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.write_multiple_register" bundle="edt" default="写多个寄存器" /></field>
				</obj>
				<obj name="ceventtype-acc151" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>17</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.report" bundle="edt" default="报告从机 ID" /></field>
				</obj>
				<obj name="ceventtype-acc152" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>20</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.reference" bundle="edt" default="读通用参考值" /></field>
				</obj>
				<obj name="ceventtype-acc153" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>22</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.shield" bundle="edt" default="屏蔽写寄存器" /></field>
				</obj>
				<obj name="ceventtype-acc154" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>23</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.multiple_register" bundle="edt" default="读/写多个寄存器" /></field>
				</obj>
				<obj name="ceventtype-acc155" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>24</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.read_FIFO" bundle="edt" default="读FIFO 查询数据" /></field>
				</obj>
				<obj name="ceventtype-acc156" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>43</str></field>
					<field name="text"><i18n key="edt.data.collecttype.modbus.identification_code" bundle="edt" default="读设备识别码" /></field>
				</obj>
				<obj name="ceventtype-acc157" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>160</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.state" bundle="edt" default="状态" /></field>
				</obj>
				<obj name="ceventtype-acc158" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>161</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.list" bundle="edt" default="得到名称列表" /></field>
				</obj>
				<obj name="ceventtype-acc159" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>162</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.handled" bundle="edt" default="确认" /></field>
				</obj>
				<obj name="ceventtype-acc160" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>163</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.rename" bundle="edt" default="重命名" /></field>
				</obj>
				<obj name="ceventtype-acc161" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>164</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.read" bundle="edt" default="读" /></field>
				</obj>
				<obj name="ceventtype-acc162" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>165</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.write" bundle="edt" default="写" /></field>
				</obj>
				<obj name="ceventtype-acc163" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>166</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.access_attributes" bundle="edt" default="得到变量访问属性" /></field>
				</obj>
				<obj name="ceventtype-acc164" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>167</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.variables" bundle="edt" default="定义命名变量" /></field>
				</obj>
				<obj name="ceventtype-acc165" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>168</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.dispersed" bundle="edt" default="定义分散访问" /></field>
				</obj>
				<obj name="ceventtype-acc166" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>169</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.dispersed_attribute" bundle="edt" default="得到分散访问属性" /></field>
				</obj>
				<obj name="ceventtype-acc167" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>170</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_variables" bundle="edt" default="删除访问变量" /></field>
				</obj>
				<obj name="ceventtype-acc168" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>171</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.variables_list" bundle="edt" default="定义命名变量列表" /></field>
				</obj>
				<obj name="ceventtype-acc169" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>172</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.variables_attribute" bundle="edt" default="得到属性命名的变量列表" /></field>
				</obj>
				<obj name="ceventtype-acc170" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>173</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_list" bundle="edt" default="删除指定的变量列表" /></field>
				</obj>
				<obj name="ceventtype-acc171" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>174</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.type" bundle="edt" default="定义命名类型" /></field>
				</obj>
				<obj name="ceventtype-acc172" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>175</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.variables_type" bundle="edt" default="得到命名类型属性" /></field>
				</obj>
				<obj name="ceventtype-acc173" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>176</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_type" bundle="edt" default="删除命名类型" /></field>
				</obj>
				<obj name="ceventtype-acc174" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>177</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.input" bundle="edt" default="输入" /></field>
				</obj>
				<obj name="ceventtype-acc175" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>178</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.output" bundle="edt" default="输出" /></field>
				</obj>
				<obj name="ceventtype-acc176" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>179</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.control" bundle="edt" default="控制" /></field>
				</obj>
				<obj name="ceventtype-acc177" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>180</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.abandon_control" bundle="edt" default="放弃控制" /></field>
				</obj>
				<obj name="ceventtype-acc178" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>181</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.semaphore" bundle="edt" default="定义信号量" /></field>
				</obj>
				<obj name="ceventtype-acc179" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>182</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_emaphore" bundle="edt" default="删除信号量" /></field>
				</obj>
				<obj name="ceventtype-acc180" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>183</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report_semaphore" bundle="edt" default="报告信号量状态" /></field>
				</obj>
				<obj name="ceventtype-acc181" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>184</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report_pool" bundle="edt" default="报告池信号状态" /></field>
				</obj>
				<obj name="ceventtype-acc182" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>185</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.semaphore_output" bundle="edt" default="报告信号量输入状态" /></field>
				</obj>
				<obj name="ceventtype-acc183" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>186</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.download_sequence" bundle="edt" default="启动下载序列" /></field>
				</obj>
				<obj name="ceventtype-acc184" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>187</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.download" bundle="edt" default="下载部分" /></field>
				</obj>
				<obj name="ceventtype-acc185" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>188</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.stop_download" bundle="edt" default="终止下载序列" /></field>
				</obj>
				<obj name="ceventtype-acc186" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>189</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.start_upload" bundle="edt" default="开始上传序列" /></field>
				</obj>
				<obj name="ceventtype-acc187" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>190</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.upload" bundle="edt" default="上传部分" /></field>
				</obj>
				<obj name="ceventtype-acc188" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>191</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.stop_upload" bundle="edt" default="终止上传序列" /></field>
				</obj>
				<obj name="ceventtype-acc189" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>192</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.field_download" bundle="edt" default="请求域下载" /></field>
				</obj>
				<obj name="ceventtype-acc190" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>193</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.field_upload" bundle="edt" default="请求域上传" /></field>
				</obj>
				<obj name="ceventtype-acc191" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>194</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.field_load" bundle="edt" default="加载域内容" /></field>
				</obj>
				<obj name="ceventtype-acc192" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>195</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.field_storage" bundle="edt" default="存储域内容" /></field>
				</obj>
				<obj name="ceventtype-acc193" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>196</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_field" bundle="edt" default="删除域" /></field>
				</obj>
				<obj name="ceventtype-acc194" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>197</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.field_attribute" bundle="edt" default="获取域属性" /></field>
				</obj>
				<obj name="ceventtype-acc195" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>198</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.create_program" bundle="edt" default="创建程序调用" /></field>
				</obj>
				<obj name="ceventtype-acc196" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>199</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_program" bundle="edt" default="删除程序调用" /></field>
				</obj>
				<obj name="ceventtype-acc197" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>200</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.start" bundle="edt" default="开始" /></field>
				</obj>
				<obj name="ceventtype-acc198" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>201</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.stop" bundle="edt" default="停止" /></field>
				</obj>
				<obj name="ceventtype-acc199" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>202</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.continue" bundle="edt" default="继续" /></field>
				</obj>
				<obj name="ceventtype-acc200" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>203</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.reset" bundle="edt" default="重置" /></field>
				</obj>
				<obj name="ceventtype-acc201" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>204</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.kill" bundle="edt" default="杀死" /></field>
				</obj>
				<obj name="ceventtype-acc202" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>205</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.program_attribute" bundle="edt" default="获取程序调用属性" /></field>
				</obj>
				<obj name="ceventtype-acc203" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>206</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.get_file" bundle="edt" default="获取文件" /></field>
				</obj>
				<obj name="ceventtype-acc204" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>207</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.event" bundle="edt" default="定义事件条件" /></field>
				</obj>
				<obj name="ceventtype-acc205" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>208</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_event" bundle="edt" default="删除事件条件" /></field>
				</obj>
				<obj name="ceventtype-acc206" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>209</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.event_attribute" bundle="edt" default="获取事件条件属性" /></field>
				</obj>
				<obj name="ceventtype-acc207" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>210</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report_event" bundle="edt" default="报告事件条件状态" /></field>
				</obj>
				<obj name="ceventtype-acc208" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>211</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.change_event" bundle="edt" default="改变事件状态监测" /></field>
				</obj>
				<obj name="ceventtype-acc209" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>212</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.trigger_event" bundle="edt" default="触发事件" /></field>
				</obj>
				<obj name="ceventtype-acc210" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>213</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.action" bundle="edt" default="定义事件动作" /></field>
				</obj>
				<obj name="ceventtype-acc211" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>214</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_action" bundle="edt" default="删除事件动作" /></field>
				</obj>
				<obj name="ceventtype-acc212" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>215</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.action_attribute" bundle="edt" default="获取事件动作属性" /></field>
				</obj>
				<obj name="ceventtype-acc213" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>216</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report_action" bundle="edt" default="行动报告状态" /></field>
				</obj>
				<obj name="ceventtype-acc214" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>217</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.register" bundle="edt" default="定义事件注册" /></field>
				</obj>
				<obj name="ceventtype-acc215" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>218</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_register" bundle="edt" default="删除事件注册" /></field>
				</obj>
				<obj name="ceventtype-acc216" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>219</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.change_register" bundle="edt" default="改变事件注册" /></field>
				</obj>
				<obj name="ceventtype-acc217" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>220</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report_register" bundle="edt" default="报告事件注册状态" /></field>
				</obj>
				<obj name="ceventtype-acc218" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>221</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.register_attribute" bundle="edt" default="获取事件注册属性" /></field>
				</obj>
				<obj name="ceventtype-acc219" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>222</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.event_notification" bundle="edt" default="承认事件通知" /></field>
				</obj>
				<obj name="ceventtype-acc220" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>223</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.alarm_sum" bundle="edt" default="得到报警汇总" /></field>
				</obj>
				<obj name="ceventtype-acc221" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>224</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.alarm_register" bundle="edt" default="得到报警登记汇总" /></field>
				</obj>
				<obj name="ceventtype-acc222" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>225</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.read_log" bundle="edt" default="读日志" /></field>
				</obj>
				<obj name="ceventtype-acc223" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>226</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.write_log" bundle="edt" default="写日志" /></field>
				</obj>
				<obj name="ceventtype-acc224" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>227</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.initialization_log" bundle="edt" default="初始化日志" /></field>
				</obj>
				<obj name="ceventtype-acc225" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>228</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report_log" bundle="edt" default="报告日志状态" /></field>
				</obj>
				<obj name="ceventtype-acc226" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>229</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.creat_log" bundle="edt" default="创建日志" /></field>
				</obj>
				<obj name="ceventtype-acc227" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>230</str></field>
					<field name="text"><i18n key="dt.data.collecttype.iec61850.remove_log" bundle="edt" default="删除日志" /></field>
				</obj>
				<obj name="ceventtype-acc228" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>231</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.functions" bundle="edt" default="得到功能列表" /></field>
				</obj>
				<obj name="ceventtype-acc229" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>232</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.open_file" bundle="edt" default="打开文件" /></field>
				</obj>
				<obj name="ceventtype-acc230" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>233</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.read_file" bundle="edt" default="读取文件" /></field>
				</obj>
				<obj name="ceventtype-acc231" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>234</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.close_file" bundle="edt" default="关闭文件" /></field>
				</obj>
				<obj name="ceventtype-acc232" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>235</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.rename_file" bundle="edt" default="重命名文件" /></field>
				</obj>
				<obj name="ceventtype-acc233" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>236</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.remove_file" bundle="edt" default="删除文件" /></field>
				</obj>
				<obj name="ceventtype-acc234" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>237</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.directory" bundle="edt" default="文件目录" /></field>
				</obj>
				<obj name="ceventtype-acc235" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>238</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.active" bundle="edt" default="主动状态" /></field>
				</obj>
				<obj name="ceventtype-acc236" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>239</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.report" bundle="edt" default="信息报告" /></field>
				</obj>
				<obj name="ceventtype-acc237" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>240</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.notification" bundle="edt" default="事件通知" /></field>
				</obj>
				<obj name="ceventtype-acc238" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>241</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.appended" bundle="edt" default="附加到事件条件" /></field>
				</obj>
				<obj name="ceventtype-acc239" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>242</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.signal" bundle="edt" default="连接到信号" /></field>
				</obj>
				<obj name="ceventtype-acc240" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>243</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.end" bundle="edt" default="结束" /></field>
				</obj>
				<obj name="ceventtype-acc241" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>244</str></field>
					<field name="text"><i18n key="edt.data.collecttype.iec61850.cancel" bundle="edt" default="取消" /></field>
				</obj>
				<obj name="ceventtype-acc243" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>4</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.service" bundle="edt" default="服务列表" /></field>
				</obj>
				<obj name="ceventtype-acc244" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>99</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.identity" bundle="edt" default="身份列表" /></field>
				</obj>
				<obj name="ceventtype-acc245" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>100</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.interface" bundle="edt" default="接口列表" /></field>
				</obj>
				<obj name="ceventtype-acc246" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>101</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.register" bundle="edt" default="注册会话" /></field>
				</obj>
				<obj name="ceventtype-acc247" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>102</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.cancellation" bundle="edt" default="注销会话" /></field>
				</obj>
				<obj name="ceventtype-acc248" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>111</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.send_request" bundle="edt" default="发送请求或答复数据" /></field>
				</obj>
				<obj name="ceventtype-acc249" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>112</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.send" bundle="edt" default="发送单元数据" /></field>
				</obj>
				<obj name="ceventtype-acc250" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>114</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.state" bundle="edt" default="指示状态" /></field>
				</obj>
				<obj name="iappprotocol-acc251" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
					<field name="value"><str>115</str></field>
					<field name="text"><i18n key="edt.data.collecttype.ethernetip.cancel" bundle="edt" default="取消" /></field>
				</obj>
			</list>
		</args>
	</obj>


	<obj name="eventDataTable" singleton="true" scope="app"
		 type="com.venustech.taihe.pangu.vault.commons.data.EventDataTable">

		<field name="oprNumMap">
			<lmap>
				<item key="EQ">
					<obj name="eq-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>EQ</str></field>
						<field name="text"><str>=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>

				<item key="NE">
					<obj name="ne-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>NE</str></field>
						<field name="text"><str>!=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="LT">
					<obj name="lt-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>LT</str></field>
						<field name="text"><str>&lt;</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>

				<item key="LE">
					<obj name="le-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>LE</str></field>
						<field name="text"><str>&lt;=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="GT">
					<obj name="gt-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>GT</str></field>
						<field name="text"><str>&gt;</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="GE">
					<obj name="ge-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>GE</str></field>
						<field name="text"><str>&gt;=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>

				<item key="BETWEEN">
					<obj name="between-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>BETWEEN</str></field>
						<field name="text">
							<i18n key="edt.opr.between" bundle="edt" default="在两者之间" />
						</field>
						<field name="oprands"><str>couple</str></field>
					</obj>
				</item>
				<item key="IN_NUMBER">
					<obj name="in_number-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IN_NUMBER</str></field>
						<field name="text">
							<i18n key="edt.opr.in" bundle="edt" default="属于" />
						</field>
						<field name="oprands"><str>multiple</str></field>
					</obj>
				</item>

				<item key="IS_NULL">
					<obj name="is_null-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IS_NULL</str></field>
						<field name="text"><i18n key="edt.opr.isnull" bundle="edt" default="是否为空"/></field>
						<field name="oprands"><str>IS_NULL</str></field>
					</obj>
				</item>

			</lmap>
		</field>


		<field name="oprNumMiniMap">
			<lmap>
				<item key="EQ">
					<obj name="eq_mini-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>EQ</str></field>
						<field name="text"><str>=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="NE">
					<obj name="ne_mini-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>NE</str></field>
						<field name="text"><str>!=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="BETWEEN">
					<obj name="between_mini-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>BETWEEN</str></field>
						<field name="text"><i18n key="edt.opr.between" bundle="edt" default="在两者之间"/></field>
						<field name="oprands"><str>couple</str></field>
					</obj>
				</item>
				<item key="IN_NUMBER">
					<obj name="in_number_mini-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IN_NUMBER</str></field>
						<field name="text"><i18n key="edt.opr.in" bundle="edt" default="属于"/></field>
						<field name="oprands"><str>multiple</str></field>
					</obj>
				</item>
				<item key="IS_NULL">
					<obj name="is_null_mini-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IS_NULL</str></field>
						<field name="text"><i18n key="edt.opr.isnull" bundle="edt" default="是否为空"/></field>
						<field name="oprands"><str>IS_NULL</str></field>
					</obj>
				</item>
			</lmap>
		</field>


		<field name="oprNumDistMap">
			<lmap>
				<item key="EQ">
					<obj name="eq_dist-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>EQ</str></field>
						<field name="text"><str>=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="NE">
					<obj name="ne_dist-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>NE</str></field>
						<field name="text"><str>!=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="IN_NUMBER">
					<obj name="in_number_dist-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IN_NUMBER</str></field>
						<field name="text"><i18n key="edt.opr.in" bundle="edt" default="属于"/></field>
						<field name="oprands"><str>multiple</str></field>
					</obj>
				</item>
				<item key="IS_NULL">
					<obj name="is_null_dist-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IS_NULL</str></field>
						<field name="text"><i18n key="edt.opr.isnull" bundle="edt" default="是否为空"/></field>
						<field name="oprands"><str>IS_NULL</str></field>
					</obj>
				</item>
			</lmap>
		</field>

		<field name="oprFullRegMap">
			<lmap>
				<item key="FULL_TEXT">
					<obj name="str-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>FULL_TEXT</str></field>
						<field name="text"><i18n key="edt.opr.full.text" bundle="edt" default="关键字"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="REGEXP_TEXT">
					<obj name="str-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>REGEXP_TEXT</str></field>
						<field name="text"><i18n key="edt.opr.regexp" bundle="edt" default="正则"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="IS_NULL">
					<obj name="is_null-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IS_NULL</str></field>
						<field name="text"><i18n key="edt.opr.isnull" bundle="edt" default="是否为空"/></field>
						<field name="oprands"><str>IS_NULL</str></field>
					</obj>
				</item>
			</lmap>
		</field>


		<field name="oprZhMap">
			<lmap>
				<item key="EQ_STR">
					<obj name="eq_str-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>EQ_STR</str></field>
						<field name="text"><str>=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<!--item key="EQ_STR_IC">
					<obj name="eq_str_ic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>EQ_STR_IC</str></field>
						<field name="text"><i18n key="edt.opr.eq.is" bundle="edt" default="=(忽略大小写)"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<item key="IN_STR">
					<obj name="in_str-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IN_STR</str></field>
						<field name="text"><i18n key="edt.opr.in" bundle="edt" default="属于"/></field>
						<field name="oprands"><str>multiple</str></field>
					</obj>
				</item>
				<!--item key="IN_STR_IC">
					<obj name="in_str_ic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IN_STR_IC</str></field>
						<field name="text"><i18n key="edt.opr.in.is" bundle="edt" default="属于(忽略大小写)"/></field>
						<field name="oprands"><str>multiple</str></field>
					</obj>
				</item-->
				<item key="START_WITH">
					<obj name="start_with-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>START_WITH</str></field>
						<field name="text"><i18n key="edt.opr.startwith" bundle="edt" default="开始于"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<!--item key="START_WITH_IC">
					<obj name="start_with_ic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>START_WITH_IC</str></field>
						<field name="text"><i18n key="edt.opr.startwith.is" bundle="edt" default="开始于(忽略大小写)"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<!--  item key="END_WITH">
					<obj name="end_with-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>END_WITH</str></field>
						<field name="text"><i18n key="edt.opr.endwith" bundle="edt" default="结束于"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<!--item key="END_WITH_IC">
					<obj name="end_with_ic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>END_WITH_IC</str></field>
						<field name="text"><i18n key="edt.opr.endwith.is" bundle="edt" default="结束于(忽略大小写)"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<!--暂时不支持匹配 item key="MATCH">
					<obj name="match-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>MATCH</str></field>
						<field name="text"><i18n key="edt.opr.match" bundle="edt" default="匹配"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="MATCH_IC">
					<obj name="match_ic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>MATCH_IC</str></field>
						<field name="text"><i18n key="edt.opr.match.is" bundle="edt" default="匹配(忽略大小写)"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<item key="CONTAINS">
					<obj name="contains-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>CONTAINS</str></field>
						<field name="text"><i18n key="edt.opr.contain" bundle="edt" default="包含"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<!--item key="CONTAINS_IC">
					<obj name="contains_ic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>CONTAINS_IC</str></field>
						<field name="text"><i18n key="edt.opr.contain.is" bundle="edt" default="包含(忽略大小写)"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<item key="IS_NULL">
					<obj name="is_null_str-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IS_NULL</str></field>
						<field name="text"><i18n key="edt.opr.isnull" bundle="edt" default="是否为空"/></field>
						<field name="oprands"><str>IS_NULL</str></field>
					</obj>
				</item>
			</lmap>
		</field>


		<field name="oprZhDicMap">
			<lmap>
				<item key="EQ_STR">
					<obj name="eq_str_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>EQ_STR</str></field>
						<field name="text"><str>=</str></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="IN_STR">
					<obj name="in_str_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IN_STR</str></field>
						<field name="text"><i18n key="edt.opr.in" bundle="edt" default="属于"/></field>
						<field name="oprands"><str>multiple</str></field>
					</obj>
				</item>
				<item key="START_WITH">
					<obj name="start_with_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>START_WITH</str></field>
						<field name="text"><i18n key="edt.opr.startwith" bundle="edt" default="开始于"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<!-- item key="END_WITH">
					<obj name="end_with_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>END_WITH</str></field>
						<field name="text"><i18n key="edt.opr.endwith" bundle="edt" default="结束于"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<!--item key="MATCH">
					<obj name="match_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>MATCH</str></field>
						<field name="text"><i18n key="edt.opr.match" bundle="edt" default="匹配"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item-->
				<item key="CONTAINS">
					<obj name="contains_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>CONTAINS</str></field>
						<field name="text"><i18n key="edt.opr.contain" bundle="edt" default="包含"/></field>
						<field name="oprands"><str>single</str></field>
					</obj>
				</item>
				<item key="IS_NULL">
					<obj name="is_null_dic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventOpr">
						<field name="value"><str>IS_NULL</str></field>
						<field name="text"><i18n key="edt.opr.isnull" bundle="edt" default="是否为空"/></field>
						<field name="oprands"><str>IS_NULL</str></field>
					</obj>
				</item>
			</lmap>
		</field>


		<field name="eventBaseAttrsMap">
			<lmap>
				<item key="lrecepttime">
					<obj name="lrecepttime-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lrecepttime</str></field>
						<field name="text">
							<i18n key="edt.attr.recepttime" bundle="edt_eventattr" default="事件接收时间" />
						</field>
						<field name="type"><str>datetime</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="cusername">
					<obj name="cusername-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cusername</str></field>
						<field name="text"><i18n key="edt.attr.username" bundle="edt_eventattr" default="用户名称"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrcip">
					<obj name="csrcip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrcip</str></field>
						<field name="text"><i18n key="edt.attr.srcip" bundle="edt_eventattr" default="源地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="isrcport">
					<obj name="isrcport-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>isrcport</str></field>
						<field name="text"><i18n key="edt.attr.srcport" bundle="edt_eventattr" default="源端口"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="coperation">
					<obj name="coperation-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>coperation</str></field>
						<field name="text"><i18n key="edt.attr.operation" bundle="edt_eventattr" default="操作"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="cdstip">
					<obj name="cdstip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstip</str></field>
						<field name="text"><i18n key="edt.attr.dstip" bundle="edt_eventattr" default="目的地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="idstport">
					<obj name="idstport-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>idstport</str></field>
						<field name="text"><i18n key="edt.attr.dstport" bundle="edt_eventattr" default="目的端口"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="cobject">
					<obj name="cobject-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cobject</str></field>
						<field name="text"><i18n key="edt.attr.object" bundle="edt_eventattr" default="对象"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="iresult">
					<obj name="iresult-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>iresult</str></field>
						<field name="text"><i18n key="edt.attr.result" bundle="edt_eventattr" default="结果"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="lduration">
					<obj name="lduration-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lduration</str></field>
						<field name="text"><i18n key="edt.attr.duration" bundle="edt_eventattr" default="持续时间"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="ireponse">
					<obj name="ireponse-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ireponse</str></field>
						<field name="text"><i18n key="edt.attr.response" bundle="edt_eventattr" default="响应"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="lsend">
					<obj name="lsend-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lsend</str></field>
						<field name="text"><i18n key="edt.attr.send" bundle="edt_eventattr" default="发送流量"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lreceive">
					<obj name="lreceive-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lreceive</str></field>
						<field name="text"><i18n key="edt.attr.receive" bundle="edt_eventattr" default="接收流量"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="imergecount">
					<obj name="imergecount-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>imergecount</str></field>
						<field name="text"><i18n key="edt.attr.mergecount" bundle="edt_eventattr" default="归并数目"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="ceventname">
					<obj name="ceventname-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ceventname</str></field>
						<field name="text"><i18n key="edt.attr.eventname" bundle="edt_eventattr" default="事件名称"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ceventdigest">
					<obj name="ceventdigest-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ceventdigest</str></field>
						<field name="text"><i18n key="edt.attr.eventdigest" bundle="edt_eventattr" default="事件摘要"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ceventtype">
					<obj name="ceventtype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ceventtype</str></field>
						<field name="text"><i18n key="edt.attr.eventtype" bundle="edt_eventattr" default="事件分类"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="icollecttype">
					<obj name="icollecttype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>icollecttype</str></field>
						<field name="text"><i18n key="edt.attr.collecttype" bundle="edt_eventattr" default="采集类型"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>num_dist</str></field>
					</obj>
				</item>
				<item key="ieventlevel">
					<obj name="ieventlevel-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ieventlevel</str></field>
						<field name="text"><i18n key="edt.attr.eventlevel" bundle="edt_eventattr" default="等级"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>num_mini</str></field>
					</obj>
				</item>
				<item key="corilevel">
					<obj name="corilevel-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>corilevel</str></field>
						<field name="text"><i18n key="edt.attr.orilevel" bundle="edt_eventattr" default="原始等级"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="coritype">
					<obj name="coritype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>coritype</str></field>
						<field name="text"><i18n key="edt.attr.oritype" bundle="edt_eventattr" default="原始类型"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="loccurtime">
					<obj name="loccurtime-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>loccurtime</str></field>
						<field name="text"><i18n key="edt.attr.occurtime" bundle="edt_eventattr" default="产生时间"/></field>
						<field name="type"><str>datetime</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="dmonitorvalue">
					<obj name="dmonitorvalue-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>dmonitorvalue</str></field>
						<field name="text"><i18n key="edt.attr.monitorvalue" bundle="edt_eventattr" default="监控数值"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="iprotocol">
					<obj name="iprotocol-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>iprotocol</str></field>
						<field name="text"><i18n key="edt.attr.protocol" bundle="edt_eventattr" default="网络协议"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>num_dist</str></field>
					</obj>
				</item>
				<item key="iappprotocol">
					<obj name="iappprotocol-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>iappprotocol</str></field>
						<field name="text"><i18n key="edt.attr.appprotocol" bundle="edt_eventattr" default="网络应用协议"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>num_dist</str></field>
					</obj>
				</item>
				<item key="csrctip">
					<obj name="csrctip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrctip</str></field>
						<field name="text"><i18n key="edt.attr.srctip" bundle="edt_eventattr" default="源转换IP地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrcname">
					<obj name="csrcname-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrcname</str></field>
						<field name="text"><i18n key="edt.attr.srcname" bundle="edt_eventattr" default="源名称"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrcmac">
					<obj name="csrcmac-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrcmac</str></field>
						<field name="text"><i18n key="edt.attr.srcmac" bundle="edt_eventattr" default="源MAC地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="isrctport">
					<obj name="isrctport-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>isrctport</str></field>
						<field name="text"><i18n key="edt.attr.srctport" bundle="edt_eventattr" default="源转换端口"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="cdstname">
					<obj name="cdstname-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstname</str></field>
						<field name="text"><i18n key="edt.attr.dstname" bundle="edt_eventattr" default="目的名称"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cdstmac">
					<obj name="cdstmac-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstmac</str></field>
						<field name="text"><i18n key="edt.attr.dstmac" bundle="edt_eventattr" default="目的MAC地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cdsttip">
					<obj name="cdsttip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdsttip</str></field>
						<field name="text"><i18n key="edt.attr.dsttip" bundle="edt_eventattr" default="目的转换IP地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="idsttport">
					<obj name="idsttport-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>idsttport</str></field>
						<field name="text"><i18n key="edt.attr.dsttport" bundle="edt_eventattr" default="目的转换端口"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="cdevip">
					<obj name="cdevip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdevip</str></field>
						<field name="text"><i18n key="edt.attr.devip" bundle="edt_eventattr" default="设备地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cdevname">
					<obj name="cdevname-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdevname</str></field>
						<field name="text"><i18n key="edt.attr.devname" bundle="edt_eventattr" default="设备名称" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cdevtype">
					<obj name="cdevtype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdevtype</str></field>
						<field name="text"><i18n key="edt.attr.devtype" bundle="edt_eventattr" default="设备类型" /></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="cprogram">
					<obj name="cprogram-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cprogram</str></field>
						<field name="text"><i18n key="edt.attr.program" bundle="edt_eventattr" default="程序名称" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ccollectorip">
					<obj name="ccollectorip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ccollectorip</str></field>
						<field name="text"><i18n key="edt.attr.collectorip" bundle="edt_eventattr" default="采集器IP地址" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="crequestmsg">
					<obj name="crequestmsg-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>crequestmsg</str></field>
						<field name="text"><i18n key="edt.attr.requestmsg" bundle="edt_eventattr" default="请求内容" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ceventmsg">
					<obj name="ceventmsg-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ceventmsg</str></field>
						<field name="text"><i18n key="edt.attr.eventmsg" bundle="edt_eventattr" default="原始消息" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>full_reg</str></field>
					</obj>
				</item>
			</lmap>
		</field>

		<!-- 事件对象自定义字段配置 -->
		<!--
			每一个自定义字段配置一个com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr对象，需要配置如下属性：
			value - str, 字段名
			dbType - str, 字段的实际类型，支持4中枚举值：int, long, double, string
			text - str/i18n, 字段描述，用于界面显示
			type - str, 字段输入类型, 用于在过滤器中配置字段条件时使用，支持枚举值：text(输入框), select(下拉框), date(日期)
			oprs - str, 字段支持的操作符集合，用于在过滤器中配置字段条件时使用，支持枚举值：num, num_mini, num_dist, zh, zh_dic, all
		-->
		<field name="eventCustomAttrsMap">
			<lmap>
				<item key="istandby1">
					<obj name="istandby1-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>istandby1</str></field>
						<field name="text"><i18n key="edt.attr.alternate.istandby1" bundle="edt_eventattr" default="日志类型" /></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>num_mini</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="istandby2">
					<obj name="istandby2-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>istandby2</str></field>
						<field name="text"><i18n key="edt.attr.alternate.istandby2" bundle="edt_eventattr" default="备用整形2" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="lstandby1">
					<obj name="lstandby1-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>lstandby1</str></field>
						<field name="text"><i18n key="edt.attr.alternate.lstandby1" bundle="edt_eventattr" default="备用长整形1" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>long</str></field>
					</obj>
				</item>
				<item key="lstandby2">
					<obj name="lstandby1-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>lstandby2</str></field>
						<field name="text"><i18n key="edt.attr.alternate.lstandby2" bundle="edt_eventattr" default="备用长整形2" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>long</str></field>
					</obj>
				</item>
				<item key="dstandby1">
					<obj name="dstandby1-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>dstandby1</str></field>
						<field name="text"><i18n key="edt.attr.alternate.dstandby1" bundle="edt_eventattr" default="备用双精度1" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>double</str></field>
					</obj>
				</item>
				<item key="dstandby2">
					<obj name="dstandby2-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>dstandby2</str></field>
						<field name="text"><i18n key="edt.attr.alternate.dstandby2" bundle="edt_eventattr" default="备用双精度2" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>double</str></field>
					</obj>
				</item>
				<!-- 厂商 -->
				<item key="cstandby1">
					<obj name="cstandby1-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby1</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby1" bundle="edt_eventattr" default="厂商" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<!-- 产品 -->
				<item key="cstandby2">
					<obj name="cstandby2-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby2</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby2" bundle="edt_eventattr" default="产品" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<!-- 漏洞编号 -->
				<item key="cstandby3">
					<obj name="cstandby3-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby3</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby3" bundle="edt_eventattr" default="备用字符串3" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<!-- 采集器名称 -->
				<item key="cstandby4">
					<obj name="cstandby4-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby4</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby4" bundle="edt_eventattr" default="备用字符串4" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby5">
					<obj name="cstandby5-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby5</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby5" bundle="edt_eventattr" default="备用字符串5" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby6">
					<obj name="cstandby6-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby6</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby6" bundle="edt_eventattr" default="备用字符串6" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="istandby3">
					<obj name="istandby3-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>istandby3</str></field>
						<field name="text"><i18n key="edt.attr.alternate.istandby3" bundle="edt_eventattr" default="备用整形3" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="istandby4">
					<obj name="istandby4-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>istandby4</str></field>
						<field name="text"><i18n key="edt.attr.alternate.istandby4" bundle="edt_eventattr" default="备用整形4" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="istandby5">
					<obj name="istandby5-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>istandby5</str></field>
						<field name="text"><i18n key="edt.attr.alternate.istandby5" bundle="edt_eventattr" default="备用整形5" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="istandby6">
					<obj name="istandby6-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>istandby6</str></field>
						<field name="text"><i18n key="edt.attr.alternate.istandby6" bundle="edt_eventattr" default="备用整形6" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="lstandby3">
					<obj name="lstandby3-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>lstandby3</str></field>
						<field name="text"><i18n key="edt.attr.alternate.lstandby3" bundle="edt_eventattr" default="备用长整形3" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>long</str></field>
					</obj>
				</item>
				<item key="lstandby4">
					<obj name="lstandby4-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>lstandby4</str></field>
						<field name="text"><i18n key="edt.attr.alternate.lstandby4" bundle="edt_eventattr" default="备用长整形4" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>long</str></field>
					</obj>
				</item>
				<item key="lstandby5">
					<obj name="lstandby5-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>lstandby5</str></field>
						<field name="text"><i18n key="edt.attr.alternate.lstandby5" bundle="edt_eventattr" default="备用长整形5" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>long</str></field>
					</obj>
				</item>
				<item key="lstandby6">
					<obj name="lstandby6-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>lstandby6</str></field>
						<field name="text"><i18n key="edt.attr.alternate.lstandby6" bundle="edt_eventattr" default="备用长整形6" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
						<field name="dbType"><str>long</str></field>
					</obj>
				</item>
				<item key="cstandby7">
					<obj name="cstandby7-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby7</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby7" bundle="edt_eventattr" default="备用字符串7" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby8">
					<obj name="cstandby8-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby8</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby8" bundle="edt_eventattr" default="备用字符串8" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby9">
					<obj name="cstandby9-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby9</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby9" bundle="edt_eventattr" default="备用字符串9" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby10">
					<obj name="cstandby10-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby10</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby10" bundle="edt_eventattr" default="域名" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby11">
					<obj name="cstandby11-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby11</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby11" bundle="edt_eventattr" default="业务系统" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>
				<item key="cstandby12">
					<obj name="cstandby12-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>cstandby12</str></field>
						<field name="text"><i18n key="edt.attr.alternate.cstandby12" bundle="edt_eventattr" default="访问内容" /></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>string</str></field>
					</obj>
				</item>

				<!-- 整合流量字段 包括NTA、TAR -->
				<item key="cdstmask">
					<obj name="cdstmask-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstmask</str></field>
						<field name="text"><i18n key="edt.attr.cdstmask" bundle="edt_eventattr" default="目的掩码"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrcmask">
					<obj name="csrcmask-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrcmask</str></field>
						<field name="text"><i18n key="edt.attr.csrcmask" bundle="edt_eventattr" default="源掩码"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="iscenarid">
					<obj name="iscenarid-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>iscenarid</str></field>
						<field name="text"><i18n key="edt.attr.iscenarid" bundle="edt_eventattr" default="业务场景标识"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="itcpflag">
					<obj name="itcpflag-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>itcpflag</str></field>
						<field name="text"><i18n key="edt.attr.itcpflag" bundle="edt_eventattr" default="TCP标志位"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>num_dist</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="itos">
					<obj name="itos-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>itos</str></field>
						<field name="text"><i18n key="edt.attr.itos" bundle="edt_eventattr" default="Tos"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="isubprotocol">
					<obj name="isubprotocol-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventCustomAttr">
						<field name="value"><str>isubprotocol</str></field>
						<field name="text"><i18n key="edt.attr.isubprotocol" bundle="edt_eventattr" default="子协议"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
						<field name="dbType"><str>int</str></field>
					</obj>
				</item>
				<item key="isrcas">
					<obj name="isrcas-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>isrcas</str></field>
						<field name="text"><i18n key="edt.attr.isrcas" bundle="edt_eventattr" default="DSRC_AS"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="idstas">
					<obj name="idstas-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>idstas</str></field>
						<field name="text"><i18n key="edt.attr.idstas" bundle="edt_eventattr" default="DST_AS"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="ifilesize">
					<obj name="ifilesize-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ifilesize</str></field>
						<field name="text"><i18n key="edt.attr.ifilesize" bundle="edt_eventattr" default="文件大小"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="iattackid">
					<obj name="iattackid-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>iattackid</str></field>
						<field name="text"><i18n key="edt.attr.iattackid" bundle="edt_eventattr" default="攻击ID"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="imatchtag">
					<obj name="imatchtag-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>imatchtag</str></field>
						<field name="text"><i18n key="edt.attr.imatchtag" bundle="edt_eventattr" default="命中威胁情报"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="dsrratio">
					<obj name="dsrratio-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>dsrratio</str></field>
						<field name="text"><i18n key="edt.attr.dsrratio" bundle="edt_eventattr" default="发收字节比率"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="dsrpackratio">
					<obj name="dsrpackratio-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>dsrpackratio</str></field>
						<field name="text"><i18n key="edt.attr.dsrpackratio" bundle="edt_eventattr" default="发收数据包比率"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lstartime">
					<obj name="lstartime-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lstartime</str></field>
						<field name="text"><i18n key="edt.attr.lstartime" bundle="edt_eventattr" default="开始时间"/></field>
						<field name="type"><str>datetime</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lendtime">
					<obj name="lendtime-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lendtime</str></field>
						<field name="text"><i18n key="edt.attr.lendtime" bundle="edt_eventattr" default="结束时间"/></field>
						<field name="type"><str>datetime</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lsendpack">
					<obj name="lsendpack-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lsendpack</str></field>
						<field name="text"><i18n key="edt.attr.lsendpack" bundle="edt_eventattr" default="发送包数"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lrecivepack">
					<obj name="lrecivepack-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lrecivepack</str></field>
						<field name="text"><i18n key="edt.attr.lrecivepack" bundle="edt_eventattr" default="接收包数"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="linputflag">
					<obj name="linputflag-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>linputflag</str></field>
						<field name="text"><i18n key="edt.attr.linputflag" bundle="edt_eventattr" default="Input口标识"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="loutputflag">
					<obj name="loutputflag-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>loutputflag</str></field>
						<field name="text"><i18n key="edt.attr.loutputflag" bundle="edt_eventattr" default="Output口标识"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="ltotalpack">
					<obj name="ltotalpack-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ltotalpack</str></field>
						<field name="text"><i18n key="edt.attr.ltotalpack" bundle="edt_eventattr" default="总包数"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lfunctioncode">
					<obj name="lfunctioncode-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lfunctioncode</str></field>
						<field name="text"><i18n key="edt.attr.lfunctioncode" bundle="edt_eventattr" default="功能码"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="lvlanid">
					<obj name="lvlanid-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lvlanid</str></field>
						<field name="text"><i18n key="edt.attr.lvlanid" bundle="edt_eventattr" default="VlanID"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lscrLabel">
					<obj name="lscrLabel-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lscrLabel</str></field>
						<field name="text"><i18n key="edt.attr.lscrLabel" bundle="edt_eventattr" default="源标签"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="ldstLabel">
					<obj name="ldstLabel-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ldstLabel</str></field>
						<field name="text"><i18n key="edt.attr.ldstLabel" bundle="edt_eventattr" default="目的标签"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lpacksize">
					<obj name="lpacksize-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lpacksize</str></field>
						<field name="text"><i18n key="edt.attr.lpacksize" bundle="edt_eventattr" default="包大小"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="lpackavg">
					<obj name="lpackavg-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>lpackavg</str></field>
						<field name="text"><i18n key="edt.attr.lpackavg" bundle="edt_eventattr" default="接收平均包大小"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>num</str></field>
					</obj>
				</item>
				<item key="csendip">
					<obj name="csendip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csendip</str></field>
						<field name="text"><i18n key="edt.attr.csendip" bundle="edt_eventattr" default="发送者地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="creciveip">
					<obj name="creciveip-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>creciveip</str></field>
						<field name="text"><i18n key="edt.attr.creciveip" bundle="edt_eventattr" default="接收地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrclocation">
					<obj name="csrclocation-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrclocation</str></field>
						<field name="text"><i18n key="edt.attr.csrclocation" bundle="edt_eventattr" default="源地理位置"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cdstlocation">
					<obj name="cdstlocation-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstlocation</str></field>
						<field name="text"><i18n key="edt.attr.cdstlocation" bundle="edt_eventattr" default="目的地理位置"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ccategories">
					<obj name="ccategories-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ccategories</str></field>
						<field name="text"><i18n key="edt.attr.ccategories" bundle="edt_eventattr" default="威胁类型"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="corganizations">
					<obj name="corganizations-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>corganizations</str></field>
						<field name="text"><i18n key="edt.attr.corganizations" bundle="edt_eventattr" default="攻击组织"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ceventid">
					<obj name="ceventid-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ceventid</str></field>
						<field name="text"><i18n key="edt.attr.ceventid" bundle="edt_eventattr" default="包下载标识"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cattackphase">
					<obj name="cattackphase-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cattackphase</str></field>
						<field name="text"><i18n key="edt.attr.cattackphase" bundle="edt_eventattr" default="攻击阶段"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cattackresult">
					<obj name="cattackresult-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cattackresult</str></field>
						<field name="text"><i18n key="edt.attr.cattackresult" bundle="edt_eventattr" default="攻击结果"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="crequesthead">
					<obj name="crequesthead-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>crequesthead</str></field>
						<field name="text"><i18n key="edt.attr.crequesthead" bundle="edt_eventattr" default="请求头"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="crequestbody">
					<obj name="crequestbody-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>crequestbody</str></field>
						<field name="text"><i18n key="edt.attr.crequestbody" bundle="edt_eventattr" default="请求体"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cresponsehead">
					<obj name="cresponsehead-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cresponsehead</str></field>
						<field name="text"><i18n key="edt.attr.cresponsehead" bundle="edt_eventattr" default="响应头"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cresponsebody">
					<obj name="cresponsebody-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cresponsebody</str></field>
						<field name="text"><i18n key="edt.attr.cresponsebody" bundle="edt_eventattr" default="响应体"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cresponsecode">
					<obj name="cresponsecode-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cresponsecode</str></field>
						<field name="text"><i18n key="edt.attr.cresponsecode" bundle="edt_eventattr" default="响应码"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cthreatfamily">
					<obj name="cthreatfamily-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cthreatfamily</str></field>
						<field name="text"><i18n key="edt.attr.cthreatfamily" bundle="edt_eventattr" default="家族"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cfilename">
					<obj name="cfilename-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cfilename</str></field>
						<field name="text"><i18n key="edt.attr.cfilename" bundle="edt_eventattr" default="文件名"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cmd5">
					<obj name="cmd5-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cmd5</str></field>
						<field name="text"><i18n key="edt.attr.cmd5" bundle="edt_eventattr" default="文件MD5"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cfiletype">
					<obj name="cfiletype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cfiletype</str></field>
						<field name="text"><i18n key="edt.attr.cfiletype" bundle="edt_eventattr" default="文件类型"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ccheresultstatic">
					<obj name="ccheresultstatic-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ccheresultstatic</str></field>
						<field name="text"><i18n key="edt.attr.ccheresultstatic" bundle="edt_eventattr" default="静态检测结果"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csuspiciousurl">
					<obj name="csuspiciousurl-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csuspiciousurl</str></field>
						<field name="text"><i18n key="edt.attr.csuspiciousurl" bundle="edt_eventattr" default="Callback特征URL"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csuspiciousaddr">
					<obj name="csuspiciousaddr-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csuspiciousaddr</str></field>
						<field name="text"><i18n key="edt.attr.csuspiciousaddr" bundle="edt_eventattr" default="Callback特征可疑IP"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csuspiciousdomain">
					<obj name="csuspiciousdomain-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csuspiciousdomain</str></field>
						<field name="text"><i18n key="edt.attr.csuspiciousdomain" bundle="edt_eventattr" default="Callback特征可疑域名"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cfiledirect">
					<obj name="cfiledirect-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cfiledirect</str></field>
						<field name="text"><i18n key="edt.attr.cfiledirect" bundle="edt_eventattr" default="文件传输方向"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cftpusername">
					<obj name="cftpusername-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cftpusername</str></field>
						<field name="text"><i18n key="edt.attr.cftpusername" bundle="edt_eventattr" default="FTP用户名"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="crequestdomain">
					<obj name="crequestdomain-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>crequestdomain</str></field>
						<field name="text"><i18n key="edt.attr.crequestdomain" bundle="edt_eventattr" default="主机名称"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="crequestresource">
					<obj name="crequestresource-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>crequestresource</str></field>
						<field name="text"><i18n key="edt.attr.crequestresource" bundle="edt_eventattr" default="URL"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csender">
					<obj name="csender-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csender</str></field>
						<field name="text"><i18n key="edt.attr.csender" bundle="edt_eventattr" default="发件人"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="crecipient">
					<obj name="crecipient-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>crecipient</str></field>
						<field name="text"><i18n key="edt.attr.crecipient" bundle="edt_eventattr" default="收件人"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cemailtitle">
					<obj name="cemailtitle-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cemailtitle</str></field>
						<field name="text"><i18n key="edt.attr.cemailtitle" bundle="edt_eventattr" default="邮件主题"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csampletags">
					<obj name="csampletags-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csampletags</str></field>
						<field name="text"><i18n key="edt.attr.csampletags" bundle="edt_eventattr" default="样本标签"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cfileid">
					<obj name="cfileid-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cfileid</str></field>
						<field name="text"><i18n key="edt.attr.cfileid" bundle="edt_eventattr" default="文件ID"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ctaskid">
					<obj name="ctaskid-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ctaskid</str></field>
						<field name="text"><i18n key="edt.attr.ctaskid" bundle="edt_eventattr" default="任务ID"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csubmittype">
					<obj name="csubmittype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csubmittype</str></field>
						<field name="text"><i18n key="edt.attr.csubmittype" bundle="edt_eventattr" default="提交类型"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cpayload">
					<obj name="cpayload-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cpayload</str></field>
						<field name="text"><i18n key="edt.attr.cpayload" bundle="edt_eventattr" default="Payload信息"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cintranet">
					<obj name="cintranet-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cintranet</str></field>
						<field name="text"><i18n key="edt.attr.cintranet" bundle="edt_eventattr" default="内外网标识"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrcregion">
					<obj name="csrcregion-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrcregion</str></field>
						<field name="text"><i18n key="edt.attr.csrcregion" bundle="edt_eventattr" default="源区域"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cdstregion">
					<obj name="cdstregion-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstregion</str></field>
						<field name="text"><i18n key="edt.attr.cdstregion" bundle="edt_eventattr" default="目的区域"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="csrcdevtype">
					<obj name="csrcdevtype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>csrcdevtype</str></field>
						<field name="text"><i18n key="edt.attr.csrcdevtype" bundle="edt_eventattr" default="源设备类型"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="cdstdevtype">
					<obj name="cdstdevtype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cdstdevtype</str></field>
						<field name="text"><i18n key="edt.attr.cdstdevtype" bundle="edt_eventattr" default="目的设备类型"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="cnexthop">
					<obj name="cnexthop-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cnexthop</str></field>
						<field name="text"><i18n key="edt.attr.cnexthop" bundle="edt_eventattr" default="下一跳"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="ctitle">
					<obj name="ctitle-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>ctitle</str></field>
						<field name="text"><i18n key="edt.attr.ctitle" bundle="edt_eventattr" default="Title信息"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cccaddress">
					<obj name="cccaddress-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cccaddress</str></field>
						<field name="text"><i18n key="edt.attr.cccaddress" bundle="edt_eventattr" default="抄送地址"/></field>
						<field name="type"><str>text</str></field>
						<field name="oprs"><str>zh</str></field>
					</obj>
				</item>
				<item key="cattacktype">
					<obj name="cattacktype-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cattacktype</str></field>
						<field name="text"><i18n key="edt.attr.cattacktype" bundle="edt_eventattr" default="攻击类型"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
				<item key="cflowdir">
					<obj name="cflowdir-datatable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttr">
						<field name="value"><str>cflowdir</str></field>
						<field name="text"><i18n key="edt.attr.cflowdir" bundle="edt_eventattr" default="流方向"/></field>
						<field name="type"><str>select</str></field>
						<field name="oprs"><str>zh_dic</str></field>
					</obj>
				</item>
			</lmap>
		</field>
		<field name="attrDataMap">
			<lmap>
				<item key="ceventtype">
					<list>
						<!--   认证授权  -->
						<obj name="ceventtype-auth" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/auth</str></field>
							<field name="text"><i18n key="edt.data.category.auth" bundle="edt" default="/认证授权" /></field>
						</obj>
						<obj name="ceventtype-auth_accmgr" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/auth/accmgr</str></field>
							<field name="text"><i18n key="edt.data.category.auth.account" bundle="edt" default="/认证授权/账号管理" /></field>
						</obj>
						<obj name="ceventtype-auth_authention" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/auth/authention</str></field>
							<field name="text"><i18n key="edt.data.category.auth.authention" bundle="edt" default="/认证授权/安全认证" /></field>
						</obj>
						<obj name="ceventtype-auth_anthority" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/auth/anthority</str></field>
							<field name="text"><i18n key="edt.data.category.auth.anthority" bundle="edt" default="/认证授权/授权管理" /></field>
						</obj>
						<obj name="ceventtype-auth_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/auth/other</str></field>
							<field name="text"><i18n key="edt.data.category.auth.other" bundle="edt" default="/认证授权/其它" /></field>
						</obj>
						<!--   网络访问  -->
						<obj name="ceventtype-acc" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc</str></field>
							<field name="text"><i18n key="edt.data.category.ac" bundle="edt" default="/网络访问" /></field>
						</obj>
						<obj name="ceventtype-acc_session" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/session</str></field>
							<field name="text"><i18n key="edt.data.category.ac.netlink" bundle="edt" default="/网络访问/会话连接" /></field>
						</obj>
						<obj name="ceventtype-acc_normal" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/normal</str></field>
							<field name="text"><i18n key="edt.data.category.ac.legal" bundle="edt" default="/网络访问/正常访问" /></field>
						</obj>
						<obj name="ceventtype-acc_illegal" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/illegal</str></field>
							<field name="text"><i18n key="edt.data.category.ac.illegal" bundle="edt" default="/网络访问/违规访问" /></field>
						</obj>
						<obj name="ceventtype-acc_suspicious" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/suspicious</str></field>
							<field name="text"><i18n key="edt.data.category.ac.suspicious" bundle="edt" default="/网络访问/可疑访问" /></field>
						</obj>
						<!--  流量star-->
						<obj name="ceventtype-acc_audit" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/audit</str></field>
							<field name="text"><i18n key="edt.data.category.ac.audit" bundle="edt" default="/网络访问/安全审计" /></field>
						</obj>
						<obj name="ceventtype-acc_analyze" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/analyze</str></field>
							<field name="text"><i18n key="edt.data.category.ac.analyze" bundle="edt" default="/网络访问/协议分析" /></field>
						</obj>
						<obj name="ceventtype-acc_software" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/software</str></field>
							<field name="text"><i18n key="edt.data.category.ac.software" bundle="edt" default="/网络访问/网络娱乐" /></field>
						</obj>
						<obj name="ceventtype-acc_pwd" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/pwd</str></field>
							<field name="text"><i18n key="edt.data.category.ac.pwd" bundle="edt" default="/网络访问/弱口令" /></field>
						</obj>
						<!-- 常用应用协议日志 -->
						<obj name="ceventtype-acc_comm" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm</str></field>
							<field name="text"><i18n key="edt.data.category.ac.comm" bundle="edt" default="/网络访问/应用协议" /></field>
						</obj>
						<obj name="ceventtype-acc_ftp" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/ftp</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ftp" bundle="edt" default="/网络访问/FTP日志" /></field>
						</obj>
						<obj name="ceventtype-acc_dns" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/dns</str></field>
							<field name="text"><i18n key="edt.data.category.ac.dns" bundle="edt" default="/网络访问/DNS日志" /></field>
						</obj>
						<obj name="ceventtype-acc_http" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/http</str></field>
							<field name="text"><i18n key="edt.data.category.ac.http" bundle="edt" default="/网络访问/HTTP日志" /></field>
						</obj>
						<obj name="ceventtype-acc_https" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/https</str></field>
							<field name="text"><i18n key="edt.data.category.ac.https" bundle="edt" default="/网络访问/应用日志/HTTPS" /></field>
						</obj>
						<obj name="ceventtype-acc_ldap" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/ldap</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ldap" bundle="edt" default="/网络访问/LDAP日志" /></field>
						</obj>
						<obj name="ceventtype-acc_smb" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/smb</str></field>
							<field name="text"><i18n key="edt.data.category.ac.smb" bundle="edt" default="/网络访问/SMB日志" /></field>
						</obj>
						<obj name="ceventtype-acc_smtp" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/smtp</str></field>
							<field name="text"><i18n key="edt.data.category.ac.smtp" bundle="edt" default="/网络访问/应用日志/SMTP" /></field>
						</obj>
						<!-- 数据库日志 -->
						<obj name="ceventtype-acc_mysql" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/mysql</str></field>
							<field name="text"><i18n key="edt.data.category.ac.mysql" bundle="edt" default="/网络访问/MYSQL日志" /></field>
						</obj>
						<!-- telnet日志 -->
						<obj name="ceventtype-acc_telnet" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/telnet</str></field>
							<field name="text"><i18n key="edt.data.category.ac.telnet" bundle="edt" default="/网络访问/TELNET日志" /></field>
						</obj>
						<!-- pop3日志 -->
						<obj name="ceventtype-acc_pop3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/pop3</str></field>
							<field name="text"><i18n key="edt.data.category.ac.pop3" bundle="edt" default="/网络访问/应用日志/POP3日志" /></field>
						</obj>
						<!-- imap日志 -->
						<obj name="ceventtype-acc_imap" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/imap</str></field>
							<field name="text"><i18n key="edt.data.category.ac.imap" bundle="edt" default="/网络访问/应用日志/IMAP日志" /></field>
						</obj>
						<!-- tds日志 -->
						<obj name="ceventtype-acc_tds" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/tds</str></field>
							<field name="text"><i18n key="edt.data.category.ac.tds" bundle="edt" default="/网络访问/应用日志/TDS日志" /></field>
						</obj>
						<!-- bgp日志 -->
						<obj name="ceventtype-acc_bgp" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/bgp</str></field>
							<field name="text"><i18n key="edt.data.category.ac.bgp" bundle="edt" default="/网络访问/应用日志/BGP日志" /></field>
						</obj>
						<obj name="ceventtype-acc_ssl" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/comm/ssl</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ssl" bundle="edt" default="/网络访问/应用日志/SSL日志" /></field>
						</obj>

						<!-- 工控日志 -->
						<obj name="ceventtype-acc_ics" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics" bundle="edt" default="/网络访问/工控日志" /></field>
						</obj>
						<!-- S7 -->
						<obj name="ceventtype-acc_ics_s7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/s7</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.s7" bundle="edt" default="/网络访问/工控日志/S7" /></field>
						</obj>
						<!-- DNP3 -->
						<obj name="ceventtype-acc_ics_dnp3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/dnp3</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.dnp3" bundle="edt" default="/网络访问/工控日志/DNP3" /></field>
						</obj>
						<!-- 103 -->
						<obj name="ceventtype-acc_ics_iec103" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/iec103</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.iec103" bundle="edt" default="/网络访问/工控日志/IEC103" /></field>
						</obj>
						<!-- 104 -->
						<obj name="ceventtype-acc_ics_iec104" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/iec104</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.iec104" bundle="edt" default="/网络访问/工控日志/IEC104" /></field>
						</obj>
						<!-- DECRPC -->
						<obj name="ceventtype-acc_ics_decrpc" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/decrpc</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.decrpc" bundle="edt" default="/网络访问/工控日志/DECRPC" /></field>
						</obj>
						<!-- Modbus -->
						<obj name="ceventtype-acc_ics_modbus" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/modbus</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.modbus" bundle="edt" default="/网络访问/工控日志/Modbus" /></field>
						</obj>
						<!-- IEC61850-MMS -->
						<obj name="ceventtype-acc_ics_iec61850mms" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/iec61850mms</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.iec61850mms" bundle="edt" default="/网络访问/工控日志/IEC61850-MMS" /></field>
						</obj>
						<!-- IEC61850-GOOSE -->
						<obj name="ceventtype-acc_ics_iec61850mms" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/iec61850goose</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.iec61850goose" bundle="edt" default="/网络访问/工控日志/IEC61850-GOOSE" /></field>
						</obj>
						<!-- EtherNet/IP -->
						<obj name="ceventtype-acc_ics_ethernetip" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/ics/ethernetip</str></field>
							<field name="text"><i18n key="edt.data.category.ac.ics.ethernetip" bundle="edt" default="/网络访问/工控日志/EtherNetIP" /></field>
						</obj>
						<!--  流量end-->

						<obj name="ceventtype-acc_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/acc/other</str></field>
							<field name="text"><i18n key="edt.data.category.ac.other" bundle="edt" default="/网络访问/其它" /></field>
						</obj>
						<!--   操作记录  -->
						<obj name="ceventtype-opr" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/opr</str></field>
							<field name="text"><i18n key="edt.data.category.action" bundle="edt" default="/操作记录" /></field>
						</obj>
						<obj name="ceventtype-opr_sys" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/opr/sys</str></field>
							<field name="text"><i18n key="edt.data.category.action.maintenance" bundle="edt" default="/操作记录/系统维护" /></field>
						</obj>
						<obj name="ceventtype-opr_cfg" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/opr/cfg</str></field>
							<field name="text"><i18n key="edt.data.category.action.alter" bundle="edt" default="/操作记录/配置变更" /></field>
						</obj>
						<obj name="ceventtype-opr_db" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/opr/db</str></field>
							<field name="text"><i18n key="edt.data.category.action.dataoper" bundle="edt" default="/操作记录/数据操作" /></field>
						</obj>
						<obj name="ceventtype-opr_soft" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/opr/soft</str></field>
							<field name="text"><i18n key="edt.data.category.action.softsetup" bundle="edt" default="/操作记录/软件维护" /></field>
						</obj>
						<obj name="ceventtype-opr_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/opr/other</str></field>
							<field name="text"><i18n key="edt.data.category.action.other" bundle="edt" default="/操作记录/其它" /></field>
						</obj>
						<!--   安全预警  -->
						<obj name="ceventtype-sec" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/sec</str></field>
							<field name="text"><i18n key="edt.data.category.warning" bundle="edt" default="/安全预警" /></field>
						</obj>
						<obj name="ceventtype-sec_virus" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/sec/virus</str></field>
							<field name="text"><i18n key="edt.data.category.warning.virus" bundle="edt" default="/安全预警/病毒预警" /></field>
						</obj>
						<obj name="ceventtype-sec_leak" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/sec/leak</str></field>
							<field name="text"><i18n key="edt.data.category.warning.vuln" bundle="edt" default="/安全预警/漏洞预警" /></field>
						</obj>
						<obj name="ceventtype-sec_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/sec/other</str></field>
							<field name="text"><i18n key="edt.data.category.warning.other" bundle="edt" default="/安全预警/其它" /></field>
						</obj>
						<!--   信息刺探  -->
						<obj name="ceventtype-spy" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/spy</str></field>
							<field name="text"><i18n key="edt.data.category.spy" bundle="edt" default="/信息刺探" /></field>
						</obj>
						<obj name="ceventtype-spy_net" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/spy/net</str></field>
							<field name="text"><i18n key="edt.data.category.spy.netscan" bundle="edt" default="/信息刺探/网络扫描" /></field>
						</obj>
						<obj name="ceventtype-spy_port" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/spy/port</str></field>
							<field name="text"><i18n key="edt.data.category.spy.netprobe" bundle="edt" default="/信息刺探/服务探测" /></field>
						</obj>
						<obj name="ceventtype-spy_vuln" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/spy/vuln</str></field>
							<field name="text"><i18n key="edt.data.category.spy.vulnscan" bundle="edt" default="/信息刺探/漏洞扫描" /></field>
						</obj>
						<obj name="ceventtype-spy_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/spy/other</str></field>
							<field name="text"><i18n key="edt.data.category.spy.other" bundle="edt" default="/信息刺探/其它" /></field>
						</obj>
						<!--   恶意代码  -->
						<obj name="ceventtype-malware" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware</str></field>
							<field name="text"><i18n key="edt.data.category.malware" bundle="edt" default="/恶意代码" /></field>
						</obj>
						<obj name="ceventtype-malware_worm" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/worm</str></field>
							<field name="text"><i18n key="edt.data.category.malware.worm" bundle="edt" default="/恶意代码/网络蠕虫" /></field>
						</obj>
						<obj name="ceventtype-malware_botnet" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/botnet</str></field>
							<field name="text"><i18n key="edt.data.category.malware.botnet" bundle="edt" default="/恶意代码/僵尸软件" /></field>
						</obj>
						<obj name="ceventtype-malware_virus" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/virus</str></field>
							<field name="text"><i18n key="edt.data.category.malware.virus" bundle="edt" default="/恶意代码/病毒" /></field>
						</obj>
						<obj name="ceventtype-malware_trojan" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/trojan</str></field>
							<field name="text"><i18n key="edt.data.category.malware.trojan" bundle="edt" default="/恶意代码/木马" /></field>
						</obj>
						<obj name="ceventtype-malware_spyware" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/spyware</str></field>
							<field name="text"><i18n key="edt.data.category.malware.spyware" bundle="edt" default="/恶意代码/间谍软件" /></field>
						</obj>
						<obj name="ceventtype-malware_adware" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/adware</str></field>
							<field name="text"><i18n key="edt.data.category.malware.adware" bundle="edt" default="/恶意代码/流氓软件" /></field>
						</obj>
						<obj name="ceventtype-malware_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/malware/other</str></field>
							<field name="text"><i18n key="edt.data.category.malware.other" bundle="edt" default="/恶意代码/其它" /></field>
						</obj>
						<!--   攻击入侵  -->
						<obj name="ceventtype-att" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att</str></field>
							<field name="text"><i18n key="edt.data.category.attack" bundle="edt" default="/攻击入侵" /></field>
						</obj>
						<obj name="ceventtype-att_ddos" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/ddos</str></field>
							<field name="text"><i18n key="edt.data.category.attack.ddos" bundle="edt" default="/攻击入侵/拒绝服务" /></field>
						</obj>
						<obj name="ceventtype-att_vul" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/vul</str></field>
							<field name="text"><i18n key="edt.data.category.attack.vuln" bundle="edt" default="/攻击入侵/漏洞利用" /></field>
						</obj>

						<obj name="ceventtype-att_sql" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/sql</str></field>
							<field name="text"><i18n key="edt.data.category.attack.sql" bundle="edt" default="/攻击入侵/SQL注入" /></field>
						</obj>
						<obj name="ceventtype-att_xss" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/xss</str></field>
							<field name="text"><i18n key="edt.data.category.attack.xss" bundle="edt" default="/攻击入侵/XSS注入" /></field>
						</obj>
						<obj name="ceventtype-att_right" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/right</str></field>
							<field name="text"><i18n key="edt.data.category.attack.right" bundle="edt" default="/攻击入侵/提权攻击" /></field>
						</obj>

						<obj name="ceventtype-att_bot" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/bot</str></field>
							<field name="text"><i18n key="edt.data.category.attack.bot" bundle="edt" default="/攻击入侵/僵尸攻击" /></field>
						</obj>
						<obj name="ceventtype-att_backdoor" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/backdoor</str></field>
							<field name="text"><i18n key="edt.data.category.attack.backdoor" bundle="edt" default="/攻击入侵/后门攻击" /></field>
						</obj>
						<obj name="ceventtype-att_hiject" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/hijack</str></field>
							<field name="text"><i18n key="edt.data.category.attack.hijack" bundle="edt" default="/攻击入侵/域名劫持" /></field>
						</obj>
						<obj name="ceventtype-att_pwd" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/pwd</str></field>
							<field name="text"><i18n key="edt.data.category.attack.pwdguess" bundle="edt" default="/攻击入侵/口令猜测" /></field>
						</obj>
						<!--   流量start-->
						<obj name="ceventtype-att_cmd" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/cmd</str></field>
							<field name="text"><i18n key="edt.data.category.attack.cmd" bundle="edt" default="/攻击入侵/命令注入" /></field>
						</obj>
						<obj name="ceventtype-att_code" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/code</str></field>
							<field name="text"><i18n key="edt.data.category.attack.code" bundle="edt" default="/攻击入侵/代码注入" /></field>
						</obj>
						<obj name="ceventtype-att_xml" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/xml</str></field>
							<field name="text"><i18n key="edt.data.category.attack.xml" bundle="edt" default="/攻击入侵/XML注入" /></field>
						</obj>
						<obj name="ceventtype-att_overflow" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/overflow</str></field>
							<field name="text"><i18n key="edt.data.category.attack.overflow" bundle="edt" default="/攻击入侵/溢出攻击" /></field>
						</obj>
						<obj name="ceventtype-att_cgi" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/cgi</str></field>
							<field name="text"><i18n key="edt.data.category.attack.cgi" bundle="edt" default="/攻击入侵/CGI攻击" /></field>
						</obj>
						<obj name="ceventtype-att_control" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/control</str></field>
							<field name="text"><i18n key="edt.data.category.attack.control" bundle="edt" default="/攻击入侵/远控后门" /></field>
						</obj>
						<!--   流量end-->
						<obj name="ceventtype-att_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/att/other</str></field>
							<field name="text"><i18n key="edt.data.category.attack.other" bundle="edt" default="/攻击入侵/其它" /></field>
						</obj>
						<!--   信息危害  -->
						<obj name="ceventtype-harm" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm</str></field>
							<field name="text"><i18n key="edt.data.category.harm" bundle="edt" default="/信息危害" /></field>
						</obj>
						<obj name="ceventtype-harm_distort" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/distort</str></field>
							<field name="text"><i18n key="edt.data.category.harm.distort" bundle="edt" default="/信息危害/网页篡改" /></field>
						</obj>
						<obj name="ceventtype-harm_counterfeit" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/counterfeit</str></field>
							<field name="text"><i18n key="edt.data.category.harm.counterfeit" bundle="edt" default="/信息危害/网络仿冒" /></field>
						</obj>
						<obj name="ceventtype-harm_spam" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/spam</str></field>
							<field name="text"><i18n key="edt.data.category.harm.spam" bundle="edt" default="/信息危害/垃圾信息" /></field>
						</obj>
						<obj name="ceventtype-harm_steal" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/steal</str></field>
							<field name="text"><i18n key="edt.data.category.harm.steal" bundle="edt" default="/信息危害/信息窃取" /></field>
						</obj>
						<obj name="ceventtype-harm_spread" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/spread</str></field>
							<field name="text"><i18n key="edt.data.category.harm.spread" bundle="edt" default="/信息危害/传播非法信息" /></field>
						</obj>
						<obj name="ceventtype-harm_pub" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/pub</str></field>
							<field name="text"><i18n key="edt.data.category.harm.pub" bundle="edt" default="/信息危害/发表非法言论" /></field>
						</obj>
						<obj name="ceventtype-harm_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/harm/other</str></field>
							<field name="text"><i18n key="edt.data.category.harm.other" bundle="edt" default="/信息危害/其它" /></field>
						</obj>

						<!--   系统状态  -->
						<obj name="ceventtype-stat" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat" bundle="edt" default="/系统状态" /></field>
						</obj>
						<obj name="ceventtype-stat_start" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat/start</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat.start" bundle="edt" default="/系统状态/系统启动或重启" /></field>
						</obj>
						<obj name="ceventtype-stat_stop" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat/stop</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat.stop" bundle="edt" default="/系统状态/系统关闭或宕机" /></field>
						</obj>
						<obj name="ceventtype-stat_info" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat/info</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat.info" bundle="edt" default="/系统状态/运行报告" /></field>
						</obj>
						<obj name="ceventtype-stat_change" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat/change</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat.change" bundle="edt" default="/系统状态/状态改变" /></field>
						</obj>
						<obj name="ceventtype-stat_conflict" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat/conflict</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat.conflict" bundle="edt" default="/系统状态/地址冲突" /></field>
						</obj>
						<obj name="ceventtype-stat_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stat/other</str></field>
							<field name="text"><i18n key="edt.data.category.sysstat.other" bundle="edt" default="/系统状态/其它" /></field>
						</obj>

						<!--   设备故障  -->
						<obj name="ceventtype-fault" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault</str></field>
							<field name="text"><i18n key="edt.data.category.fault" bundle="edt" default="/设备故障" /></field>
						</obj>
						<obj name="ceventtype-fault_hardware" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/hardware</str></field>
							<field name="text"><i18n key="edt.data.category.fault.hardware" bundle="edt" default="/设备故障/硬件故障" /></field>
						</obj>
						<obj name="ceventtype-fault_server" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/server</str></field>
							<field name="text"><i18n key="edt.data.category.fault.server" bundle="edt" default="/设备故障/服务故障" /></field>
						</obj>
						<obj name="ceventtype-fault_link" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/link</str></field>
							<field name="text"><i18n key="edt.data.category.fault.linkage" bundle="edt" default="/设备故障/链路故障" /></field>
						</obj>
						<obj name="ceventtype-fault_interrupt" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/interrupt</str></field>
							<field name="text"><i18n key="edt.data.category.fault.serviceinterrupt" bundle="edt" default="/设备故障/服务中断" /></field>
						</obj>
						<obj name="ceventtype-fault_threshole" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/threshole</str></field>
							<field name="text"><i18n key="edt.data.category.fault.threshold" bundle="edt" default="/设备故障/阈值告警" /></field>
						</obj>
						<obj name="ceventtype-fault_soft" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/soft</str></field>
							<field name="text"><i18n key="edt.data.category.fault.software" bundle="edt" default="/设备故障/软件故障" /></field>
						</obj>
						<obj name="ceventtype-fault_other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/fault/other</str></field>
							<field name="text"><i18n key="edt.data.category.fault.other" bundle="edt" default="/设备故障/其它" /></field>
						</obj>
						<!--   信息监控  -->
						<obj name="ceventtype-monitor" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/monitor</str></field>
							<field name="text"><i18n key="edt.data.category.infomonitor" bundle="edt" default="/信息监控" /></field>
						</obj>


						<obj name="ceventtype-other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/other</str></field>
							<field name="text"><i18n key="edt.data.category.other" bundle="edt" default="/其它分类" /></field>
						</obj>

					</list>
				</item>
				<item key="icollecttype">
					<list>
						<obj name="icollecttype-syslog" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><str>syslog</str></field>
						</obj>
						<obj name="icollecttype-snmp_trap" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><str>snmptrap</str></field>
						</obj>
						<obj name="icollecttype-netflow" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><str>netflow</str></field>
						</obj>
						<obj name="icollecttype-file" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><str>textfile</str></field>
						</obj>
						<obj name="icollecttype-jdbc" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6</str></field>
							<field name="text"><str>jdbc</str></field>
						</obj>
						<obj name="icollecttype-sniffer" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7</str></field>
							<field name="text"><str>sniffer</str></field>
						</obj>
						<obj name="icollecttype-wmi" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8</str></field>
							<field name="text"><str>wmi</str></field>
						</obj>
						<obj name="icollecttype-ftp" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9</str></field>
							<field name="text"><str>ftp</str></field>
						</obj>
						<obj name="icollecttype-monitor" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10</str></field>
							<field name="text"><str>monitor</str></field>
						</obj>
						<obj name="icollecttype-netbios" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11</str></field>
							<field name="text"><str>netbios</str></field>
						</obj>
						<obj name="icollecttype-opsec" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12</str></field>
							<field name="text"><str>opsec</str></field>
						</obj>
						<obj name="icollecttype-other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>100</str></field>
							<field name="text"><str>other</str></field>
						</obj>
					</list>
				</item>

				<item key="ieventlevel">
					<list>
						<obj name="ieventlevel-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><i18n key="edt.data.severity.fatal" bundle="edt" default="严重" /></field>
						</obj>
						<obj name="ieventlevel-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><i18n key="edt.data.severity.major" bundle="edt" default="重要" /></field>
						</obj>
						<obj name="ieventlevel-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.severity.common" bundle="edt" default="一般" /></field>
						</obj>
						<obj name="ieventlevel-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.severity.slight" bundle="edt" default="轻微" /></field>
						</obj>
						<obj name="ieventlevel-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.severity.info" bundle="edt" default="信息" /></field>
						</obj>
					</list>
				</item>

				<item key="ireponse">
					<list>
						<obj name="ireponse-none" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>-1</str></field>
							<field name="text"><i18n key="edt.data.ireponse.none" bundle="edt" default="无响应" /></field>
						</obj>
						<obj name="ireponse-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.ireponse.accept" bundle="edt" default="允许" /></field>
						</obj>
						<obj name="ireponse-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.ireponse.deny" bundle="edt" default="拒绝" /></field>
						</obj>
						<obj name="ireponse-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.ireponse.drop" bundle="edt" default="丢弃" /></field>
						</obj>
						<obj name="ireponse-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.ireponse.discovery" bundle="edt" default="发现" /></field>
						</obj>
						<obj name="ireponse-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><i18n key="edt.data.ireponse.clear" bundle="edt" default="清除" /></field>
						</obj>
						<obj name="ireponse-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><i18n key="edt.data.ireponse.delete" bundle="edt" default="删除" /></field>
						</obj>
						<obj name="ireponse-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6</str></field>
							<field name="text"><i18n key="edt.data.ireponse.isolation" bundle="edt" default="隔离" /></field>
						</obj>
						<obj name="ireponse-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7</str></field>
							<field name="text"><i18n key="edt.data.ireponse.kill" bundle="edt" default="查杀" /></field>
						</obj>
						<obj name="ireponse-8" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8</str></field>
							<field name="text"><i18n key="edt.data.ireponse.replace" bundle="edt" default="修复" /></field>
						</obj>
						<obj name="ireponse-9" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9</str></field>
							<field name="text"><i18n key="edt.data.ireponse.reset" bundle="edt" default="重置" /></field>
						</obj>
						<obj name="ireponse-10" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10</str></field>
							<field name="text"><i18n key="edt.data.ireponse.reboot" bundle="edt" default="重启" /></field>
						</obj>
						<obj name="ireponse-11" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11</str></field>
							<field name="text"><i18n key="edt.data.ireponse.ignore" bundle="edt" default="忽略" /></field>
						</obj>
					</list>
				</item>

				<item key="ialertlevel">
					<list>
						<obj name="ialertlevel-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertlevel.highest" bundle="edt" default="危急" /></field>
						</obj>
						<obj name="ialertlevel-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertlevel.high" bundle="edt" default="高级" /></field>
						</obj>
						<obj name="ialertlevel-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertlevel.mid" bundle="edt" default="中级" /></field>
						</obj>
						<obj name="ialertlevel-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertlevel.low" bundle="edt" default="低级" /></field>
						</obj>
						<obj name="ialertlevel-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertlevel.lowest" bundle="edt" default="信息" /></field>
						</obj>
					</list>
				</item>
				<!-- 告警类别-->
				<item key="calerttype">
					<list>
						<obj name="ialerttype-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>security</str></field>
							<field name="text"><i18n key="edt.data.alert.type.security" bundle="edt" default="安全" /></field>
						</obj>
						<obj name="ialerttype-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>status</str></field>
							<field name="text"><i18n key="edt.data.alert.type.stat" bundle="edt" default="状态" /></field>
						</obj>
						<obj name="ialerttype-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>performance</str></field>
							<field name="text"><i18n key="edt.data.alert.type.performance" bundle="edt" default="性能" /></field>
						</obj>
						<obj name="ialerttype-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>config</str></field>
							<field name="text"><i18n key="edt.data.alert.type.configuration" bundle="edt" default="配置" /></field>
						</obj>
						<obj name="ialerttype-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>breakdown</str></field>
							<field name="text"><i18n key="edt.data.alert.type.fault" bundle="edt" default="故障" /></field>
						</obj>
						<obj name="ialerttype-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>other</str></field>
							<field name="text"><i18n key="edt.data.alert.type.other" bundle="edt" default="其他" /></field>
						</obj>
					</list>
				</item>
				<item key="corilevel">
					<list>
						<obj name="corilevel-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.emerg" bundle="edt" default="紧急：导致系统不可用" /></field>
						</obj>
						<obj name="corilevel-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.alert" bundle="edt" default="警告：应立即采取应对行动" /></field>
						</obj>
						<obj name="corilevel-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.crit" bundle="edt" default="临界：达到临界条件" /></field>
						</obj>
						<obj name="corilevel-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.err" bundle="edt" default="出错：一般出错事件" /></field>
						</obj>
						<obj name="corilevel-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.warning" bundle="edt" default="预警：预警性提示事件" /></field>
						</obj>
						<obj name="corilevel-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.notice" bundle="edt" default="提示：重要的正常事件" /></field>
						</obj>
						<obj name="corilevel-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.info" bundle="edt" default="通知：一般性的正常事件" /></field>
						</obj>
						<obj name="corilevel-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7</str></field>
							<field name="text"><i18n key="edt.data.oriseverity.debug" bundle="edt" default="调试：调试信息" /></field>
						</obj>
					</list>
				</item>
				<item key="cobject">
					<list>

						<obj name="cobject-host_app" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/app</str></field>
							<field name="text"><i18n key="edt.data.object.host.application" bundle="edt" default="/应用程序" /></field>
						</obj>
						<obj name="cobject-host_app_service" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/app/service</str></field>
							<field name="text"><i18n key="edt.data.object.host.application.service" bundle="edt" default="/应用程序/服务" /></field>
						</obj>
						<obj name="cobject-host_app_db" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/app/db</str></field>
							<field name="text"><i18n key="edt.data.object.host.application.db" bundle="edt" default="/应用程序/数据库" /></field>
						</obj>
						<obj name="cobject-os" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/os</str></field>
							<field name="text"><i18n key="edt.data.object.host.os" bundle="edt" default="/操作系统" /></field>
						</obj>
						<obj name="cobject-cpu" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/cpu</str></field>
							<field name="text"><i18n key="edt.data.object.host.resource.cpu" bundle="edt" default="/CPU" /></field>
						</obj>
						<obj name="cobject-memory" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/memory</str></field>
							<field name="text"><i18n key="edt.data.object.host.resource.mem" bundle="edt" default="/内存" /></field>
						</obj>
						<obj name="cobject-disk" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/disk</str></field>
							<field name="text"><i18n key="edt.data.object.host.resource.disk" bundle="edt" default="/磁盘" /></field>
						</obj>
						<obj name="cobject-file" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/file</str></field>
							<field name="text"><i18n key="edt.data.object.host.resource.file" bundle="edt" default="/文件" /></field>
						</obj>
						<obj name="cobject-process" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/process</str></field>
							<field name="text"><i18n key="edt.data.object.host.resource.process" bundle="edt" default="/进程" /></field>
						</obj>
						<obj name="cobject-register" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/register</str></field>
							<field name="text"><i18n key="edt.data.object.host.resource.registry" bundle="edt" default="/注册表" /></field>
						</obj>
						<obj name="cobject-net" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/net</str></field>
							<field name="text"><i18n key="edt.data.object.network" bundle="edt" default="/网络" /></field>
						</obj>

						<obj name="cobject-net_vpn" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/net/vpn</str></field>
							<field name="text"><i18n key="edt.data.object.network.vpn" bundle="edt" default="/网络/vpn" /></field>
						</obj>
						<obj name="cobject-net_interface" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/net/interface</str></field>
							<field name="text"><i18n key="edt.data.object.network.interface" bundle="edt" default="/网络/接口" /></field>
						</obj>
						<obj name="cobject-net_pkt" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/net/pkt</str></field>
							<field name="text"><i18n key="edt.data.object.network.pkt" bundle="edt" default="/网络/报文" /></field>
						</obj>
						<obj name="cobject-net_session" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/net/session</str></field>
							<field name="text"><i18n key="edt.data.object.network.session" bundle="edt" default="/网络/会话" /></field>
						</obj>
						<obj name="cobject-net_link" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/net/link</str></field>
							<field name="text"><i18n key="edt.data.object.network.link" bundle="edt" default="/网络/链路" /></field>
						</obj>
						<obj name="cobject-phy" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/phy</str></field>
							<field name="text"><i18n key="edt.data.object.facility" bundle="edt" default="/物理设施" /></field>
						</obj>
						<obj name="cobject-phy_egs" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/phy/egs</str></field>
							<field name="text"><i18n key="edt.data.object.facility.access" bundle="edt" default="/物理设施/门禁" /></field>
						</obj>
						<obj name="cobject-user" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/user</str></field>
							<field name="text"><i18n key="edt.data.object.user" bundle="edt" default="/用户" /></field>
						</obj>
						<obj name="cobject-policy" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/policy</str></field>
							<field name="text"><i18n key="edt.data.object.policy" bundle="edt" default="/策略" /></field>
						</obj>
					</list>
				</item>
				<item key="coperation">
					<list>
						<obj name="coperation-login" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/login</str></field>
							<field name="text"><i18n key="edt.data.action.authen.logon" bundle="edt" default="/登录" /></field>
						</obj>
						<obj name="coperation-logout" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/logout</str></field>
							<field name="text"><i18n key="edt.data.action.authen.logoff" bundle="edt" default="/注销" /></field>
						</obj>
						<obj name="coperation-grant" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/grant</str></field>
							<field name="text"><i18n key="edt.data.action.author.grant" bundle="edt" default="/授权" /></field>
						</obj>
						<obj name="coperation-revoke" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/revoke</str></field>
							<field name="text"><i18n key="edt.data.action.author.revoke" bundle="edt" default="/撤销" /></field>
						</obj>
						<obj name="coperation-access" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/access</str></field>
							<field name="text"><i18n key="edt.data.action.access" bundle="edt" default="/访问" /></field>
						</obj>
						<obj name="coperation-audit" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/audit</str></field>
							<field name="text"><i18n key="edt.data.action.audit" bundle="edt" default="/审计" /></field>
						</obj>
						<obj name="coperation-check" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/check</str></field>
							<field name="text"><i18n key="edt.data.action.check" bundle="edt" default="/核查" /></field>
						</obj>
						<obj name="coperation-create" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/create</str></field>
							<field name="text"><i18n key="edt.data.action.create" bundle="edt" default="/创建" /></field>
						</obj>
						<obj name="coperation-add" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/add</str></field>
							<field name="text"><i18n key="edt.data.action.add" bundle="edt" default="/增加" /></field>
						</obj>
						<obj name="coperation-modify" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/modify</str></field>
							<field name="text"><i18n key="edt.data.action.modify" bundle="edt" default="/修改" /></field>
						</obj>
						<obj name="coperation-delete" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/delete</str></field>
							<field name="text"><i18n key="edt.data.action.del" bundle="edt" default="/删除" /></field>
						</obj>
						<obj name="coperation-lost" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/lost</str></field>
							<field name="text"><i18n key="edt.data.action.lost" bundle="edt" default="/丢失" /></field>
						</obj>
						<obj name="coperation-enable" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/enable</str></field>
							<field name="text"><i18n key="edt.data.action.enable" bundle="edt" default="/启用" /></field>
						</obj>
						<obj name="coperation-forbid" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/forbid</str></field>
							<field name="text"><i18n key="edt.data.action.disable" bundle="edt" default="/禁用" /></field>
						</obj>
						<obj name="coperation-lock" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/lock</str></field>
							<field name="text"><i18n key="edt.data.action.lock" bundle="edt" default="/锁定" /></field>
						</obj>
						<obj name="coperation-unlock" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/unlock</str></field>
							<field name="text"><i18n key="edt.data.action.unlock" bundle="edt" default="/解锁" /></field>
						</obj>
						<obj name="coperation-exec" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/exec</str></field>
							<field name="text"><i18n key="edt.data.action.execute" bundle="edt" default="/执行" /></field>
						</obj>
						<obj name="coperation-find" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/find</str></field>
							<field name="text"><i18n key="edt.data.action.find" bundle="edt" default="/搜索" /></field>
						</obj>
						<obj name="coperation-substitute" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/substitute</str></field>
							<field name="text"><i18n key="edt.data.action.substitute" bundle="edt" default="/替代" /></field>
						</obj>
						<obj name="coperation-relay" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/relay</str></field>
							<field name="text"><i18n key="edt.data.action.relay" bundle="edt" default="/转发" /></field>
						</obj>
						<obj name="coperation-open" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/open</str></field>
							<field name="text"><i18n key="edt.data.action.open" bundle="edt" default="/打开" /></field>
						</obj>
						<obj name="coperation-close" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/close</str></field>
							<field name="text"><i18n key="edt.data.action.close" bundle="edt" default="/关闭" /></field>
						</obj>
						<obj name="coperation-setup" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/install</str></field>
							<field name="text"><i18n key="edt.data.action.setup" bundle="edt" default="/安装" /></field>
						</obj>
						<obj name="coperation-unload" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/uninstall</str></field>
							<field name="text"><i18n key="edt.data.action.unload" bundle="edt" default="/卸载" /></field>
						</obj>
						<obj name="coperation-upgrad" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/upgrad</str></field>
							<field name="text"><i18n key="edt.data.action.update" bundle="edt" default="/升级" /></field>
						</obj>
						<obj name="coperation-clear" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/clear</str></field>
							<field name="text"><i18n key="edt.data.action.clear" bundle="edt" default="/清除" /></field>
						</obj>
						<obj name="coperation-backup" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/backup</str></field>
							<field name="text"><i18n key="edt.data.action.backup" bundle="edt" default="/备份" /></field>
						</obj>
						<obj name="coperation-restore" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/restore</str></field>
							<field name="text"><i18n key="edt.data.action.restore" bundle="edt" default="/恢复" /></field>
						</obj>
						<obj name="coperation-copy" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/copy</str></field>
							<field name="text"><i18n key="edt.data.action.copy" bundle="edt" default="/复制" /></field>
						</obj>
						<obj name="coperation-move" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/move</str></field>
							<field name="text"><i18n key="edt.data.action.move" bundle="edt" default="/移动" /></field>
						</obj>
						<obj name="coperation-print" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/print</str></field>
							<field name="text"><i18n key="edt.data.action.print" bundle="edt" default="/打印" /></field>
						</obj>
						<obj name="coperation-start" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/start</str></field>
							<field name="text"><i18n key="edt.data.action.start" bundle="edt" default="/启动" /></field>
						</obj>
						<obj name="coperation-stop" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/stop</str></field>
							<field name="text"><i18n key="edt.data.action.stop" bundle="edt" default="/停止" /></field>
						</obj>
						<obj name="coperation-reboot" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/reboot</str></field>
							<field name="text"><i18n key="edt.data.action.reboot" bundle="edt" default="/重启" /></field>
						</obj>
						<obj name="coperation-register" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/register</str></field>
							<field name="text"><i18n key="edt.data.action.register" bundle="edt" default="/注册" /></field>
						</obj>
						<obj name="coperation-conf" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/conf</str></field>
							<field name="text"><i18n key="edt.data.action.conf" bundle="edt" default="/配置" /></field>
						</obj>
						<obj name="coperation-receive" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/receive</str></field>
							<field name="text"><i18n key="edt.data.action.receive" bundle="edt" default="/接收" /></field>
						</obj>
						<obj name="coperation-view" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/view</str></field>
							<field name="text"><i18n key="edt.data.action.view" bundle="edt" default="/查看" /></field>
						</obj>
						<obj name="coperation-drop" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/drop</str></field>
							<field name="text"><i18n key="edt.data.action.drop" bundle="edt" default="/丢弃" /></field>
						</obj>
						<obj name="coperation-scan" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/scan</str></field>
							<field name="text"><i18n key="edt.data.action.scan" bundle="edt" default="/扫描" /></field>
						</obj>
						<obj name="coperation-save" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/save</str></field>
							<field name="text"><i18n key="edt.data.action.save" bundle="edt" default="/保存" /></field>
						</obj>
						<obj name="coperation-monitor" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/monitor</str></field>
							<field name="text"><i18n key="edt.data.action.monitor" bundle="edt" default="/监视" /></field>
						</obj>
						<obj name="coperation-other" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>/other</str></field>
							<field name="text"><i18n key="edt.data.action.other" bundle="edt" default="/其它" /></field>
						</obj>
					</list>
				</item>
				<item key="iresult">
					<list>
						<obj name="iresult-none" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>-1</str></field>
							<field name="text"><i18n key="edt.data.result.none" bundle="edt" default="无结果" /></field>
						</obj>
						<obj name="iresult-success" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.result.success" bundle="edt" default="成功" /></field>
						</obj>
						<obj name="iresult-failure" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.result.fail" bundle="edt" default="失败" /></field>
						</obj>
						<obj name="iresult-timeout" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.result.timeout" bundle="edt" default="超时" /></field>
						</obj>
						<obj name="iresult-unknown" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><i18n key="edt.data.result.unknown" bundle="edt" default="未知" /></field>
						</obj>
					</list>
				</item>
				<item key="cdevtype">
					<refer>devtypeList</refer>
				</item>g
				<item key="csrcdevtype">
					<refer>devtypeList</refer>
				</item>
				<item key="cdstdevtype">
					<refer>devtypeList</refer>
				</item>

				<item key="iappprotocol">
					<refer>iappProtocolList</refer>
				</item>
				<item key="iprotocol">
					<refer>iprotocolList</refer>
				</item>

				<item key="itcpflag">
					<refer>itcp_flags</refer>
				</item>
				<item key="lfunctioncode">
					<refer>ifunction</refer>
				</item>

				<item key="cflowdir">
					<refer>directionList</refer>
				</item>
				<!-- 告警来源 -->
				<item key="ialertsrc">
					<!-- 告警保留来源 不要乱修改,可在其后面扩展 -->
					<list>
						<!-- [if hasRack monitor] -->
						<obj name="ialertsrc-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertsrc.monitor" bundle="edt" default="性能" /></field>
						</obj>
						<!-- [endif] -->
						<!-- [if hasRack rule] -->
						<obj name="ialertsrc-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertsrc.event" bundle="edt" default="关联分析" /></field>
						</obj>
						<!-- [endif] -->
						<!-- [if hasRack risk] -->
						<obj name="ialertsrclevel-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertsrc.risk" bundle="edt" default="风险" /></field>
						</obj>
						<!-- [endif] -->
						<!-- [if hasRack risk] -->
						<obj name="ialertsrc-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><i18n key="edt.data.alert.ialertsrc.hole" bundle="edt" default="漏洞" /></field>
						</obj>
						<!-- 						<obj name="ialertsrc-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>6</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.ialertsrc.vuln" bundle="edt" default="脆弱性" /></field> -->
						<!-- 						</obj> -->
						<!-- [endif] -->
						<obj name="ialertsrc-12" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12</str></field>
							<field name="text"><i18n key="edt.data.alert.src.selfmon" bundle="edt" default="系统自身监控" /></field>
						</obj>
						<obj name="ialertsrc-13" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>13</str></field>
							<field name="text"><i18n key="edt.data.alert.src.logging" bundle="edt" default="日志源监控" /></field>
						</obj>
						<obj name="ialertsrc-1000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1000</str></field>
							<field name="text"><i18n key="edt.data.alert.src.other" bundle="edt" default="其他" /></field>
						</obj>
						<!-- [if hasRack audit] -->
						<obj name="ialertsrc-20" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>20</str></field>
							<field name="text"><i18n key="edt.data.alert.src.audit" bundle="edt" default="日志分析" /></field>
						</obj>
						<!--  [endif] -->

					</list>
				</item>
				<item key="ialertstatus">
					<!-- 告警保留状态 不要乱修改,可在其后面扩展 -->
					<list>
						<!-- 						<obj name="ialertstatus-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>0</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.status.not_handled" bundle="edt" default="未确认" /></field> -->
						<!-- 						</obj> -->
						<obj name="ialertstatus-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.alert.status.wait" bundle="edt" default="待处置" /></field>
						</obj>
						<obj name="ialertstatus-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.status.handled" bundle="edt" default="处置中" /></field>
						</obj>
						<!-- 						<obj name="ialertstatus-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>2</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.status.handled_send" bundle="edt" default="已确认已派单" /></field> -->
						<!-- 						</obj> -->
						<!-- 						<obj name="ialertstatus-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>3</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.status.eliminated" bundle="edt" default="已消除" /></field> -->
						<!-- 						</obj> -->
						<!-- 						<obj name="ialertstatus-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>4</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.status.white_unaudit" bundle="edt" default="已添加白名单,未审批" /></field> -->
						<!-- 						</obj> -->
						<!-- 						<obj name="ialertstatus-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>5</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.status.white_audited" bundle="edt" default="已添加白名单,已审批通过" /></field> -->
						<!-- 						</obj> -->
						<!-- 						<obj name="ialertstatus-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption"> -->
						<!-- 							<field name="value"><str>6</str></field> -->
						<!-- 							<field name="text"><i18n key="edt.data.alert.status.white_unaudited" bundle="edt" default="已添加白名单,未审批通过" /></field> -->
						<!-- 						</obj> -->
						<obj name="ialertstatus-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.status.handle_ok" bundle="edt" default="已处置" /></field>
						</obj>
						<obj name="ialertstatus-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.alert.status.handle_ignore" bundle="edt" default="误报" /></field>
						</obj>
					</list>
				</item>
				<item key="ialertkillchain">
					<list>
						<obj name="ialertkillchain-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.killchain.investigation" bundle="edt" default="侦查" /></field>
						</obj>
						<obj name="ialertkillchain-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.killchain.permeation" bundle="edt" default="渗透" /></field>
						</obj>
						<obj name="ialertkillchain-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3</str></field>
							<field name="text"><i18n key="edt.data.alert.killchain.storm" bundle="edt" default="攻陷" /></field>
						</obj>
						<obj name="ialertkillchain-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4</str></field>
							<field name="text"><i18n key="edt.data.alert.killchain.control" bundle="edt" default="控制" /></field>
						</obj>
						<obj name="ialertkillchain-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5</str></field>
							<field name="text"><i18n key="edt.data.alert.killchain.destroy" bundle="edt" default="破坏" /></field>
						</obj>
					</list>
				</item>
				<item key="ialertdirection">
					<list>
						<obj name="ialertdirection--1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>-1</str></field>
							<field name="text"><i18n key="edt.data.alert.direction.unknown" bundle="edt" default="未知" /></field>
						</obj>
						<obj name="ialertdirection-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.alert.direction.inner" bundle="edt" default="横向移动" /></field>
						</obj>
						<obj name="ialertdirection-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.direction.out" bundle="edt" default="资产外连" /></field>
						</obj>
						<obj name="ialertdirection-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.direction.attacker" bundle="edt" default="外部威胁" /></field>
						</obj>
					</list>
				</item>
				<item key="ialertconfidence">
					<list>
						<obj name="ialertconfidence-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.alert.confidence.low" bundle="edt" default="低" /></field>
						</obj>
						<obj name="ialertconfidence-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.confidence.middle" bundle="edt" default="中" /></field>
						</obj>
						<obj name="ialertconfidence-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.confidence.high" bundle="edt" default="高" /></field>
						</obj>
					</list>
				</item>
				<item key="ialertcompromise">
					<list>
						<obj name="ialertcompromise-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.alert.compromise.confirm" bundle="edt" default="待确认" /></field>
						</obj>
						<obj name="ialertcompromise-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.compromise.notfall" bundle="edt" default="未失陷" /></field>
						</obj>
						<obj name="ialertcompromise-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.compromise.fall" bundle="edt" default="已失陷" /></field>
						</obj>
					</list>
				</item>
				<item key="ialertattackResult">
					<list>
						<obj name="ialertattackResult-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.alert.attackResult.confirm" bundle="edt" default="待确认" /></field>
						</obj>
						<obj name="ialertattackResult-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.alert.attackResult.notfall" bundle="edt" default="失败" /></field>
						</obj>
						<obj name="ialertattackResult-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.alert.attackResult.fall" bundle="edt" default="成功" /></field>
						</obj>
					</list>
				</item>
				<item key="ialertpro">
					<list>
						<obj name="ialertpro-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>title</str></field>
							<field name="text"><i18n key="alert.title" bundle="alert" default="告警名称" /></field>
						</obj>
						<obj name="ialertpro-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>devIp</str></field>
							<field name="text"><i18n key="alert.devIp" bundle="alert" default="设备地址" /></field>
						</obj>
						<obj name="ialertpro-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>alertType</str></field>
							<field name="text"><i18n key="alert.alertType" bundle="alert" default="告警类别" /></field>
						</obj>
						<obj name="ialertpro-3" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>priority</str></field>
							<field name="text"><i18n key="alert.priority" bundle="alert" default="告警级别" /></field>
						</obj>
						<obj name="ialertpro-4" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>devType</str></field>
							<field name="text"><i18n key="alert.devType" bundle="alert" default="设备类型" /></field>
						</obj>
						<obj name="ialertpro-5" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>ruleId</str></field>
							<field name="text"><i18n key="alert.ruleId" bundle="alert" default="规则ID" /></field>
						</obj>
						<obj name="ialertpro-6" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>csrcip</str></field>
							<field name="text"><i18n key="alert.csrcip" bundle="alert" default="源地址" /></field>
						</obj>
						<obj name="ialertpro-7" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>cdstip</str></field>
							<field name="text"><i18n key="alert.cdstip" bundle="alert" default="目的地址" /></field>
						</obj>
					</list>
				</item>
				<item key="istandby1">
					<!-- istandby1类型扩展 -->
					<list>
						<obj name="istandby1-0" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.logType.0" bundle="alert" default="原始日志" /></field>
						</obj>
						<obj name="istandby1-1" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.logType.1" bundle="alert" default="关联日志" /></field>
						</obj>
						<obj name="istandby1-2" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2</str></field>
							<field name="text"><i18n key="edt.data.logType.2" bundle="alert" default="聚合日志" /></field>
						</obj>
					</list>
				</item>
				<!--  攻击类型-->
				<item key="cattacktype">
					<list>
						<!--   初始访问 -->
						<obj name="cattacktype-1000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1000" bundle="edt" default="/初始访问" /></field>
						</obj>
						<obj name="cattacktype-1001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1001" bundle="edt" default="/初始访问/水坑攻击" /></field>
						</obj>
						<obj name="cattacktype-1002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1002" bundle="edt" default="/初始访问/硬件攻击" /></field>
						</obj>
						<obj name="cattacktype-1003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1003" bundle="edt" default="/初始访问/附件捆马鱼叉攻击" /></field>
						</obj>
						<obj name="cattacktype-1004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1004" bundle="edt" default="/初始访问/通过链接鱼叉攻击" /></field>
						</obj>
						<obj name="cattacktype-1005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1005" bundle="edt" default="/初始访问/利用社会工程学" /></field>
						</obj>
						<obj name="cattacktype-1006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1006" bundle="edt" default="/初始访问/主机扫描" /></field>
						</obj>
						<obj name="cattacktype-1007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1007" bundle="edt" default="/初始访问/端口扫描" /></field>
						</obj>
						<obj name="cattacktype-1008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1008" bundle="edt" default="/初始访问/漏洞扫描" /></field>
						</obj>
						<obj name="cstacattacktypendby12-1009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1009" bundle="edt" default="/初始访问/系统识别" /></field>
						</obj>
						<obj name="cattacktype-1010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1010" bundle="edt" default="/初始访问/网页爬虫" /></field>
						</obj>
						<obj name="cattacktype-1011" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1011</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1011" bundle="edt" default="/初始访问/钓鱼网站" /></field>
						</obj>
						<obj name="cattacktype-1012" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1012</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1012" bundle="edt" default="/初始访问/赌博网站" /></field>
						</obj>
						<obj name="cattacktype-1013" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1013</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1013" bundle="edt" default="/初始访问/色情网站" /></field>
						</obj>
						<obj name="cattacktype-1014" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1014</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1014" bundle="edt" default="/初始访问/暴恐网站" /></field>
						</obj>
						<obj name="cattacktype-1100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.1100" bundle="edt" default="/初始访问/其他" /></field>
						</obj>

						<!--  执行攻击 -->
						<obj name="cattacktype-2000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2000" bundle="edt" default="/执行攻击" /></field>
						</obj>
						<obj name="cattacktype-2001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2001" bundle="edt" default="/执行攻击/执行服务" /></field>
						</obj>
						<obj name="cattacktype-2002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2002" bundle="edt" default="/执行攻击/计划任务" /></field>
						</obj>
						<obj name="cattacktype-2003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2003" bundle="edt" default="/执行攻击/脚本编程" /></field>
						</obj>
						<obj name="cattacktype-2004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2004" bundle="edt" default="/执行攻击/PowerShell" /></field>
						</obj>
						<obj name="cattacktype-2005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2005" bundle="edt" default="/执行攻击/Rundll32命令" /></field>
						</obj>
						<obj name="cattacktype-2006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2006" bundle="edt" default="/执行攻击/命令行界面" /></field>
						</obj>
						<obj name="cattacktype-2007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2007" bundle="edt" default="/执行攻击/Windows远程管理" /></field>
						</obj>
						<obj name="cattacktype-2008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2008" bundle="edt" default="/执行攻击/SQL注入" /></field>
						</obj>
						<obj name="cattacktype-2009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2009" bundle="edt" default="/执行攻击/XSS攻击" /></field>
						</obj>
						<obj name="cattacktype-2010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2010" bundle="edt" default="/执行攻击/文件包含" /></field>
						</obj>
						<obj name="cattacktype-2011" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2011</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2011" bundle="edt" default="/执行攻击/文件上传" /></field>
						</obj>
						<obj name="cattacktype-2012" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2012</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2012" bundle="edt" default="/执行攻击/实体注入" /></field>
						</obj>
						<obj name="cattacktype-2013" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2013</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2013" bundle="edt" default="/执行攻击/目录遍历" /></field>
						</obj>
						<obj name="cattacktype-2014" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2014</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2014" bundle="edt" default="/执行攻击/命令执行" /></field>
						</obj>
						<obj name="cattacktype-2015" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2015</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2015" bundle="edt" default="/执行攻击/拒绝服务" /></field>
						</obj>
						<obj name="cattacktype-2016" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2016</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2016" bundle="edt" default="/执行攻击/缓冲溢出" /></field>
						</obj>
						<obj name="cattacktype-2017" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2017</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2017" bundle="edt" default="/执行攻击/漏洞利用" /></field>
						</obj>
						<obj name="cattacktype-2100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>2100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.2100" bundle="edt" default="/执行攻击/其他" /></field>
						</obj>
						<!--  持续控制-->
						<obj name="cattacktype-3000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3000" bundle="edt" default="/持续控制" /></field>
						</obj>
						<obj name="cattacktype-3001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3001" bundle="edt" default="/持续控制/账号篡改" /></field>
						</obj>
						<obj name="cattacktype-3002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3002" bundle="edt" default="/持续控制/创建账号" /></field>
						</obj>
						<obj name="cattacktype-3003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3003" bundle="edt" default="/持续控制/外部远程服务" /></field>
						</obj>
						<obj name="cattacktype-3004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3004" bundle="edt" default="/持续控制/新服务" /></field>
						</obj>
						<obj name="cattacktype-3005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3005" bundle="edt" default="/持续控制/现有服务修改" /></field>
						</obj>
						<obj name="cattacktype-3006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3006" bundle="edt" default="/持续控制/启动程序注册表" /></field>
						</obj>
						<obj name="cattacktype-3007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3007" bundle="edt" default="/持续控制/启动键注册表" /></field>
						</obj>
						<obj name="cattacktype-3008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3008" bundle="edt" default="/持续控制/僵尸主机" /></field>
						</obj>
						<obj name="cattacktype-3009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3009" bundle="edt" default="/持续控制/远程控制连接" /></field>
						</obj>
						<obj name="cattacktype-3010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3010" bundle="edt" default="/持续控制/木马后门" /></field>
						</obj>
						<obj name="cattacktype-3011" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3011</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3011" bundle="edt" default="/持续控制/可疑的p2p连接" /></field>
						</obj>
						<obj name="cattacktype-3012" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3012</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3012" bundle="edt" default="/持续控制/PC木马控制" /></field>
						</obj>
						<obj name="cattacktype-3013" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3013</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3013" bundle="edt" default="/持续控制/webshell控制" /></field>
						</obj>
						<obj name="cattacktype-3014" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3014</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3014" bundle="edt" default="/持续控制/服务器后门" /></field>
						</obj>
						<obj name="cattacktype-3015" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3015</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3015" bundle="edt" default="/持续控制/服务器木马控制" /></field>
						</obj>
						<obj name="cattacktype-3100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>3100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.3100" bundle="edt" default="/持续控制/其他" /></field>
						</obj>
						<!--  权限提升-->
						<obj name="cattacktype-4000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4000" bundle="edt" default="/权限提升" /></field>
						</obj>
						<obj name="cattacktype-4001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4001" bundle="edt" default="/权限提升/篡改访问令牌" /></field>
						</obj>
						<obj name="cattacktype-4002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4002" bundle="edt" default="/权限提升/应用程序篡改" /></field>
						</obj>
						<obj name="cattacktype-4003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4003" bundle="edt" default="/权限提升/绕过账号控制" /></field>
						</obj>
						<obj name="cattacktype-4004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4004" bundle="edt" default="/权限提升/新服务" /></field>
						</obj>
						<obj name="cattacktype-4005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4005" bundle="edt" default="/权限提升/进程注入" /></field>
						</obj>
						<obj name="cattacktype-4006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4006" bundle="edt" default="/权限提升/上传WebShell" /></field>
						</obj>
						<obj name="cattacktype-4007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4007" bundle="edt" default="/权限提升/上传漏洞" /></field>
						</obj>
						<obj name="cattacktype-4008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4008" bundle="edt" default="/权限提升/远程溢出" /></field>
						</obj>
						<obj name="cattacktype-4009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4009" bundle="edt" default="/权限提升/非授权访问" /></field>
						</obj>
						<obj name="cattacktype-4010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4010" bundle="edt" default="/权限提升/提权攻击" /></field>
						</obj>
						<obj name="cattacktype-4100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>4100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.4100" bundle="edt" default="/权限提升/其他" /></field>
						</obj>
						<!--  防御逃避-->
						<obj name="cattacktype-5000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5000" bundle="edt" default="/防御逃避" /></field>
						</obj>
						<obj name="cattacktype-5001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5001" bundle="edt" default="/防御逃避/二进制数据填充" /></field>
						</obj>
						<obj name="cattacktype-5002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5002" bundle="edt" default="/防御逃避/清空命令记录" /></field>
						</obj>
						<obj name="cattacktype-5003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5003" bundle="edt" default="/防御逃避/关闭安全工具" /></field>
						</obj>
						<obj name="cattacktype-5004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5004" bundle="edt" default="/防御逃避/文件删除" /></field>
						</obj>
						<obj name="cattacktype-5005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5005" bundle="edt" default="/防御逃避/进程替换" /></field>
						</obj>
						<obj name="cattacktype-5006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5006" bundle="edt" default="/防御逃避/堡垒机绕行" /></field>
						</obj>
						<obj name="cattacktype-5007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5007" bundle="edt" default="/防御逃避/非指定客户端登陆系统" /></field>
						</obj>
						<obj name="cattacktype-5008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5008" bundle="edt" default="/防御逃避/删除入侵日志" /></field>
						</obj>
						<obj name="cattacktype-5009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5009" bundle="edt" default="/防御逃避/删除rootkit" /></field>
						</obj>
						<obj name="cattacktype-5010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5010" bundle="edt" default="/防御逃避/隐藏攻击痕迹" /></field>
						</obj>
						<obj name="cattacktype-5100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>5100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.5100" bundle="edt" default="/防御逃避/其他" /></field>
						</obj>
						<!--  获取合法凭证访问-->
						<obj name="cattacktype-6000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6000" bundle="edt" default="/获取合法凭证访问" /></field>
						</obj>
						<obj name="cattacktype-6001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6001" bundle="edt" default="/获取合法凭证访问/arp欺骗" /></field>
						</obj>
						<obj name="cattacktype-6002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6002" bundle="edt" default="/获取合法凭证访问/暴力破解" /></field>
						</obj>
						<obj name="cattacktype-6003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6003" bundle="edt" default="/获取合法凭证访问/凭证窃取" /></field>
						</obj>
						<obj name="cattacktype-6004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6004" bundle="edt" default="/获取合法凭证访问/强制认证" /></field>
						</obj>
						<obj name="cattacktype-6005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6005" bundle="edt" default="/获取合法凭证访问/输入截取" /></field>
						</obj>
						<obj name="cattacktype-6006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6006" bundle="edt" default="/获取合法凭证访问/3389口令破解" /></field>
						</obj>
						<obj name="cattacktype-6007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6007" bundle="edt" default="/获取合法凭证访问/FTP口令破解" /></field>
						</obj>
						<obj name="cattacktype-6008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6008" bundle="edt" default="/获取合法凭证访问/SSH口令破解" /></field>
						</obj>
						<obj name="cattacktype-6009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6009" bundle="edt" default="/获取合法凭证访问/数据库口令破解" /></field>
						</obj>
						<obj name="cattacktype-6010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6010" bundle="edt" default="/获取合法凭证访问/邮件服务口令破解" /></field>
						</obj>
						<obj name="cattacktype-6011" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6011</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6011" bundle="edt" default="/获取合法凭证访问/web口令破解" /></field>
						</obj>
						<obj name="cattacktype-6100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>6100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.6100" bundle="edt" default="/获取合法凭证访问/其他" /></field>
						</obj>
						<!--  查看信息-->
						<obj name="cattacktype-7000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7000" bundle="edt" default="/查看信息" /></field>
						</obj>
						<obj name="cattacktype-7001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7001" bundle="edt" default="/查看信息/网络嗅探" /></field>
						</obj>
						<obj name="cattacktype-7002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7002" bundle="edt" default="/查看信息/账号查看" /></field>
						</obj>
						<obj name="cattacktype-7003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7003" bundle="edt" default="/查看信息/文件与路径查看" /></field>
						</obj>
						<obj name="cattacktype-7004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7004" bundle="edt" default="/查看信息/权限查看" /></field>
						</obj>
						<obj name="cattacktype-7005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7005" bundle="edt" default="/查看信息/进程查看" /></field>
						</obj>
						<obj name="cattacktype-7006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7006" bundle="edt" default="/查看信息/网络共享查看" /></field>
						</obj>
						<obj name="cattacktype-7007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7007" bundle="edt" default="/查看信息/系统信息查看" /></field>
						</obj>
						<obj name="cattacktype-7008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7008" bundle="edt" default="/查看信息/敏感命令执行" /></field>
						</obj>
						<obj name="cattacktype-7100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>7100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.7100" bundle="edt" default="/查看信息/其他" /></field>
						</obj>
						<!--  横向权限扩展-->
						<obj name="cattacktype-8000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8000" bundle="edt" default="/横向权限扩展" /></field>
						</obj>
						<obj name="cattacktype-8001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8001" bundle="edt" default="/横向权限扩展/远程桌面协议" /></field>
						</obj>
						<obj name="cattacktype-8002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8002" bundle="edt" default="/横向权限扩展/票据传递攻击" /></field>
						</obj>
						<obj name="cattacktype-8003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8003" bundle="edt" default="/横向权限扩展/远程文件复制" /></field>
						</obj>
						<obj name="cattacktype-8004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8004" bundle="edt" default="/横向权限扩展/远程服务利用" /></field>
						</obj>
						<obj name="cattacktype-8005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8005" bundle="edt" default="/横向权限扩展/第三方软件" /></field>
						</obj>
						<obj name="cattacktype-8006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8006" bundle="edt" default="/横向权限扩展/SSH劫持" /></field>
						</obj>
						<obj name="cattacktype-8007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8007" bundle="edt" default="/横向权限扩展/蠕虫传播" /></field>
						</obj>
						<obj name="cattacktype-8008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8008" bundle="edt" default="/横向权限扩展/僵尸网络" /></field>
						</obj>
						<obj name="cattacktype-8100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>8100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.8100" bundle="edt" default="/横向权限扩展/其他" /></field>
						</obj>
						<!--  收集信息-->
						<obj name="cattacktype-9000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9000" bundle="edt" default="/收集信息" /></field>
						</obj>
						<obj name="cattacktype-9001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9001" bundle="edt" default="/收集信息/音频截取" /></field>
						</obj>
						<obj name="cattacktype-9002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9002" bundle="edt" default="/收集信息/输入捕获" /></field>
						</obj>
						<obj name="cattacktype-9003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9003" bundle="edt" default="/收集信息/邮件收集" /></field>
						</obj>
						<obj name="cattacktype-9004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9004" bundle="edt" default="/收集信息/本地系统数据" /></field>
						</obj>
						<obj name="cattacktype-9005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9005" bundle="edt" default="/收集信息/共享网络数据" /></field>
						</obj>
						<obj name="cattacktype-9006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9006" bundle="edt" default="/收集信息/自动化收集" /></field>
						</obj>
						<obj name="cattacktype-9100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>9100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.9100" bundle="edt" default="/收集信息/其他" /></field>
						</obj>
						<!--  命令控制-->
						<obj name="cattacktype-10000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10000" bundle="edt" default="/命令控制" /></field>
						</obj>
						<obj name="cattacktype-10001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10001" bundle="edt" default="/命令控制/数据混淆" /></field>
						</obj>
						<obj name="cattacktype-10002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10002" bundle="edt" default="/命令控制/连接代理" /></field>
						</obj>
						<obj name="cattacktype-10003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10003" bundle="edt" default="/命令控制/多跳转代理连接" /></field>
						</obj>
						<obj name="cattacktype-10004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10004" bundle="edt" default="/命令控制/远程访问工具" /></field>
						</obj>
						<obj name="cattacktype-10005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10005" bundle="edt" default="/命令控制/远程文件复制" /></field>
						</obj>
						<obj name="cattacktype-10006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10006" bundle="edt" default="/命令控制/非常用端口" /></field>
						</obj>
						<obj name="cattacktype-10007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10007" bundle="edt" default="/命令控制/远程控制" /></field>
						</obj>
						<obj name="cattacktype-10008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10008" bundle="edt" default="/命令控制/P2P连接" /></field>
						</obj>
						<obj name="cattacktype-10009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10009" bundle="edt" default="/命令控制/连接矿池" /></field>
						</obj>
						<obj name="cattacktype-10010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10010" bundle="edt" default="/命令控制/混合攻击程序" /></field>
						</obj>
						<obj name="cattacktype-10011" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10011</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10011" bundle="edt" default="/命令控制/计算机病毒" /></field>
						</obj>
						<obj name="cattacktype-10012" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10012</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10012" bundle="edt" default="/命令控制/计算机木马" /></field>
						</obj>
						<obj name="cattacktype-10013" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10013</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10013" bundle="edt" default="/命令控制/蠕虫" /></field>
						</obj>
						<obj name="cattacktype-10014" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10014</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10014" bundle="edt" default="/命令控制/网页内嵌恶意代码" /></field>
						</obj>
						<obj name="cattacktype-10100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>10100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.10100" bundle="edt" default="/命令控制/其他" /></field>
						</obj>
						<!--  数据渗漏-->
						<obj name="cattacktype-11000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11000" bundle="edt" default="/数据渗漏" /></field>
						</obj>
						<obj name="cattacktype-11001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11001" bundle="edt" default="/数据渗漏/CC数据渗漏" /></field>
						</obj>
						<obj name="cattacktype-11002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11002" bundle="edt" default="/数据渗漏/DNS隐蔽隧道数据渗漏" /></field>
						</obj>
						<obj name="cattacktype-11003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11003" bundle="edt" default="/数据渗漏/其他协议的数据渗漏" /></field>
						</obj>
						<obj name="cattacktype-11004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11004" bundle="edt" default="/数据渗漏/非法接入存储介质拷贝" /></field>
						</obj>
						<obj name="cattacktype-11005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11005" bundle="edt" default="/数据渗漏/点滴式数据渗漏" /></field>
						</obj>
						<obj name="cattacktype-11006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11006" bundle="edt" default="/数据渗漏/流量敏感字传输" /></field>
						</obj>
						<obj name="cattacktype-11007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11007" bundle="edt" default="/数据渗漏/外传检测" /></field>
						</obj>
						<obj name="cattacktype-11008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11008" bundle="edt" default="/数据渗漏/违规外联" /></field>
						</obj>
						<obj name="cattacktype-11009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11009" bundle="edt" default="/数据渗漏/远程拷贝" /></field>
						</obj>
						<obj name="cattacktype-11100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>11100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.11100" bundle="edt" default="/数据渗漏/其他" /></field>
						</obj>
						<!--  恶劣影响-->
						<obj name="cattacktype-12000" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12000</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12000" bundle="edt" default="/恶劣影响" /></field>
						</obj>
						<obj name="cattacktype-12001" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12001</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12001" bundle="edt" default="/恶劣影响/垃圾邮件" /></field>
						</obj>
						<obj name="cattacktype-12002" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12002</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12002" bundle="edt" default="/恶劣影响/资源被劫持" /></field>
						</obj>
						<obj name="cattacktype-12003" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12003</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12003" bundle="edt" default="/恶劣影响/格式化磁盘" /></field>
						</obj>
						<obj name="cattacktype-12004" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12004</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12004" bundle="edt" default="/恶劣影响/数据破环" /></field>
						</obj>
						<obj name="cattacktype-12005" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12005</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12005" bundle="edt" default="/恶劣影响/篡改信息" /></field>
						</obj>
						<obj name="cattacktype-12006" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12006</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12006" bundle="edt" default="/恶劣影响/服务停止" /></field>
						</obj>
						<obj name="cattacktype-12007" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12007</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12007" bundle="edt" default="/恶劣影响/业务应用停止" /></field>
						</obj>
						<obj name="cattacktype-12008" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12008</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12008" bundle="edt" default="/恶劣影响/网页篡改" /></field>
						</obj>
						<obj name="cattacktype-12009" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12009</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12009" bundle="edt" default="/恶劣影响/网络拒绝服务" /></field>
						</obj>
						<obj name="cattacktype-12010" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12010</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12010" bundle="edt" default="/恶劣影响/终端拒绝服务" /></field>
						</obj>
						<obj name="cattacktype-12011" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12011</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12011" default="/恶劣影响/切断目标主机网络" /></field>
						</obj>
						<obj name="cattacktype-12012" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12012</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12012" bundle="edt" default="/恶劣影响/更改目标主机配置" /></field>
						</obj>
						<obj name="cattacktype-12100" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>12100</str></field>
							<field name="text"><i18n key="edt.data.collecttype.cattacktype.12100" bundle="edt" default="/恶劣影响/其他" /></field>
						</obj>
					</list>
				</item>
				<!-- 文件传输方向-->
				<item key="cfiledirect">
					<list>
						<obj name="cfiledirect-up" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>0</str></field>
							<field name="text"><i18n key="edt.data.action.authen.up" bundle="edt" default="上传" /></field>
						</obj>
						<obj name="cfiledirect-down" type="com.venustech.taihe.pangu.vault.commons.data.EventAttrOption">
							<field name="value"><str>1</str></field>
							<field name="text"><i18n key="edt.data.action.logsource.down" bundle="edt" default="下载" /></field>
						</obj>

					</list>
				</item>

			</lmap>
		</field>


		<field name="expLogicalMap">
			<lmap>
				<item key="and">
					<obj name="exp_logical-and" type="com.venustech.taihe.pangu.vault.commons.data.FilterLogical">
						<field name="value"><str>and</str></field>
						<field name="text"><str>AND</str></field>
					</obj>
				</item>
				<item key="or">
					<obj name="exp_logical-or" type="com.venustech.taihe.pangu.vault.commons.data.FilterLogical">
						<field name="value"><str>or</str></field>
						<field name="text"><str>OR</str></field>
					</obj>
				</item>
				<item key="not">
					<obj name="exp_logical-not" type="com.venustech.taihe.pangu.vault.commons.data.FilterLogical">
						<field name="value"><str>not</str></field>
						<field name="text"><str>NOT</str></field>
					</obj>
				</item>
			</lmap>
		</field>

	</obj>
</ioc>
