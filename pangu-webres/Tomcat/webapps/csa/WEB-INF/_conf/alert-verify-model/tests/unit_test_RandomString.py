import pytest
from aimodel.utils.Logger import Logger
from aimodel.constants import GlobalConsts, ModelConsts
from aimodel.configfetch.ConfigFetch import fetch_model_config, fetch_task_config_json
from aimodel.models.RandomString import RandomString


@pytest.fixture(scope="module")
def random_string():
    model_config_file = './configs.yaml'
    model_config = fetch_model_config(model_config_file)
    GlobalConsts.set_value('log_level', model_config.log_level)
    GlobalConsts.set_value('log_path', model_config.log_path)

    return RandomString()


@pytest.fixture(scope="module")
def test_data():
    test_case_path = "RandomString_tc.json"
    return fetch_task_config_json(test_case_path)


class TestRandomString:
    def test_predict_result_type(self, random_string, test_data):
        result = random_string.predict(test_data)
        assert isinstance(result, list)

    def test_predict_result_content(self, random_string, test_data):
        result = random_string.predict(test_data)
        assert all(isinstance(item, dict) for item in result)
        assert all('name' in item and 'score' for item in result)

    def test_predict_score_range(self, random_string, test_data):
        result = random_string.predict(test_data)
        assert all(0 <= item['score'] <= 10 for item in result)

    def test_predict(self, random_string, test_data):
        result = random_string.predict(test_data)
        assert all(item['name'] == item['name'] for item in result)
        # print result
        print(test_data)
        print(result)