import pytest
from aimodel.utils.Logger import Logger
from aimodel.constants import GlobalConsts, ModelConsts
from aimodel.configfetch.ConfigFetch import fetch_model_config, fetch_task_config_json
from aimodel.models.DataLeakageValidator import DataLeakageValidator


@pytest.fixture(scope="module")
def data_leakage():
    model_config_file = './configs.yaml'
    model_config = fetch_model_config(model_config_file)
    GlobalConsts.set_value('log_level', model_config.log_level)
    GlobalConsts.set_value('log_path', model_config.log_path)
    return DataLeakageValidator()


@pytest.fixture(scope="module")
def test_data():
    test_case_path = "DataLeakage_tc.json"
    return fetch_task_config_json(test_case_path)


class TestDataLeakage:
    def test_predict_result_type(self, data_leakage, test_data):
        result = data_leakage.predict(test_data)
        assert isinstance(result, list)

    def test_predict_result_content(self, data_leakage, test_data):
        result = data_leakage.predict(test_data)
        assert all(isinstance(item, dict) for item in result)
        assert all('id' in item and 'score' for item in result)

    def test_predict_score_range(self, data_leakage, test_data):
        result = data_leakage.predict(test_data)
        assert all(0 <= item['score'] <= 10 for item in result)

    def test_predict(self, data_leakage, test_data):
        result = data_leakage.predict(test_data)
        assert all(item['id'] == item['id'] for item in result)
        # print result
        print(test_data)
        print(result)