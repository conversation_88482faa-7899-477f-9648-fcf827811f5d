import pytest
from aimodel.utils.Logger import Logger
from aimodel.constants import GlobalConsts, ModelConsts
from aimodel.configfetch.ConfigFetch import fetch_model_config, fetch_task_config_json
from aimodel.models.Mimic import Mimic
from aimodel.dbhandler.MySqlHandler import MySqlHandler


@pytest.fixture(scope="module")
def mimic():
    model_config_file = './configs.yaml'
    model_config = fetch_model_config(model_config_file)
    GlobalConsts.set_value('log_level', model_config.log_level)
    GlobalConsts.set_value('log_path', model_config.log_path)
    mysql_database = model_config.mysql_database
    mysql_host = model_config.mysql_host
    mysql_port = model_config.mysql_port
    mysql_user = model_config.mysql_user
    mysql_password = model_config.mysql_password
    mysql_handler = MySqlHandler(mysql_database, mysql_host, mysql_port, mysql_user, mysql_password)
    return Mimic(mysql_handler)


@pytest.fixture(scope="module")
def test_data():
    test_case_path = "Mimic_tc.json"
    return fetch_task_config_json(test_case_path)


class TestMimic:
    def test_predict_result_type(self, mimic, test_data):
        result = mimic.predict(test_data)
        assert isinstance(result, list)

    def test_predict_result_content(self, mimic, test_data):
        result = mimic.predict(test_data)
        assert all(isinstance(item, dict) for item in result)
        assert all('name' in item and 'score' for item in result)

    def test_predict_score_range(self, mimic, test_data):
        result = mimic.predict(test_data)
        assert all(0 <= item['score'] <= 10 for item in result)

    def test_predict(self, mimic, test_data):
        result = mimic.predict(test_data)
        print(test_data)
        print(result)