import pytest
from aimodel.utils.Logger import Logger
from aimodel.constants import GlobalConsts, ModelConsts
from aimodel.configfetch.ConfigFetch import fetch_model_config, fetch_task_config_json
from aimodel.models.SystemCommandExec import SystemCmdExec


@pytest.fixture(scope="module")
def system_cmd_exec():
    model_config_file = './configs.yaml'
    model_config = fetch_model_config(model_config_file)
    GlobalConsts.set_value('log_level', model_config.log_level)
    GlobalConsts.set_value('log_path', model_config.log_path)
    return SystemCmdExec()


@pytest.fixture(scope="module")
def test_data():
    test_case_path = "SystemCmdExec_tc.json"
    return fetch_task_config_json(test_case_path)


class TestSystemCmdExec:
    def test_predict_result_type(self, system_cmd_exec, test_data):
        result = system_cmd_exec.predict(test_data)
        assert isinstance(result, list)

    def test_predict_result_content(self, system_cmd_exec, test_data):
        result = system_cmd_exec.predict(test_data)
        assert all(isinstance(item, dict) for item in result)
        assert all('id' in item and 'score' for item in result)

    def test_predict_score_range(self, system_cmd_exec, test_data):
        result = system_cmd_exec.predict(test_data)
        assert all(0 <= item['score'] <= 10 for item in result)

    def test_predict(self, system_cmd_exec, test_data):
        result = system_cmd_exec.predict(test_data)
        assert all(item['id'] == item['id'] for item in result)
        # print result
        print(test_data)
        print(result)