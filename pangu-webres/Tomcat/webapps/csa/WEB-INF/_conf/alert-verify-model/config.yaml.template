configs:
  - name: log_level
    description: 打印的日志级别(debug, info, error, warning)
    value: info
  - name: pangu_api_address
    description: api地址
    value: ${pangu.rest.url}
  - name: api_username
    description: api用户名
    value: ${pangu.rest.username}
  - name: api_secret
    description: api密钥
    value: ${pangu.rest.apisecret}
  - name: log_path
    description: 日志路径
    value: ${pangu.log.dir}/alert-verify-model/
  - name: mysql_database
    description: mysql数据库
    value: ${pangu.mysql.database}
  - name: mysql_host
    description: mysql host
    value: ${pangu.mysql.host}
  - name: mysql_port
    description: mysql port
    value: ${pangu.mysql.port}
  - name: mysql_user
    description: mysql user
    value: ${pangu.mysql.username}
  - name: mysql_password
    description: mysql password
    value: ${pangu.mysql.password}
