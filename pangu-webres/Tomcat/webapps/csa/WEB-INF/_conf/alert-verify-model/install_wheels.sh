HW_ARCH=$(uname -m)
CURRENT_DIR=$(pwd)
WHEELS_DIR=${CURRENT_DIR}/wheels/linux_${HW_ARCH}
DEPENDENCIES=${CURRENT_DIR}/dependencies
mkdir -p ${WHEELS_DIR}
mkdir -p ${DEPENDENCIES}

INSTALL_DIR=$(syscmd --path | awk '{ print $4 }')
PYTHON_HOME=${INSTALL_DIR}/py-standalone
PYTHON_EXEC=${PYTHON_HOME}/bin/python3

count=`ls -l ${CURRENT_DIR}/wheels |grep "^-"|wc -l`
if [ $count -gt 0 ]; # Judge exists whl file
then
  mv -f ${CURRENT_DIR}/wheels/*.whl ${WHEELS_DIR}
  sync
  $PYTHON_EXEC -m pip install ${WHEELS_DIR}/*.whl --target=${DEPENDENCIES} --upgrade --force-reinstall
  sync
fi

chmod -R 755 ${DEPENDENCIES}
