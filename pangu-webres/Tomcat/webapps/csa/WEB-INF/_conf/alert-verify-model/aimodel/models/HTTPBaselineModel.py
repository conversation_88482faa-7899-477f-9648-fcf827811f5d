"""http日志基线模型"""
import os
import site
import joblib
from aimodel.models.BaseValidator import BaseValidator


BASE_DIR = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
    "algorithms"
)
site.addsitedir(BASE_DIR)


class HTTPBaselineModel(BaseValidator):

    def __init__(self, ck_handler, sample):
        """
        构建http日志基线，
        :param ck_handler: 用于查询ck数据
        :param sample: 待检测样本，传入的json的keys为 [srcIp, dstIp, dstPort, httpHost, httpUrl, extInfo]
        """
        super().__init__()
        self._ck_handler = ck_handler

        # 异常分数
        self.anomaly_score = 0

        self.sample = sample
        # 告警日志中crequestbody和cresponsebody进行base64加密了
        self.csrcip = sample["srcIp"]
        self.cdstip = sample["dstIp"]
        self.idstport = sample["dstPort"]
        self.http_host = sample["httpHost"]
        self.http_url = sample["httpUrl"]
        self.extInfo = sample["extInfo"]

        self.request_head_data = []
        self.request_body_data = {"http_url": self.http_url, "crequestbody": []}
        self.response_head_data = []
        self.response_body_data = {"http_url": self.http_url, "cresponsebody": []}

        for message in self.extInfo:
            self.request_head_data.append(message["requestHeader"])
            self.response_head_data.append(message["responseHeader"])

            self.request_body_data["crequestbody"].append(message["requestBody"])
            self.response_body_data["cresponsebody"].append(message["responseBody"])

        self.data_map = {
            "http_url": self.http_url,
            "crequesthead": self.request_head_data,
            "crequestbody": self.request_body_data,
            "cresponsehead": self.response_head_data,
            "cresponsebody": self.response_body_data
        }

        # 模型保存路径
        self.model_store_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                            'model-lib', 'http_baseline_model', f"{self.http_host}_{self.idstport}")

        http_baseline = __import__("http_baseline")
        self.model_map = {
            "http_url": http_baseline.URLAnomaly,
            "crequesthead": http_baseline.RequestHead,
            "crequestbody": http_baseline.RequestBody,
            "cresponsehead": http_baseline.ResponseHead,
            "cresponsebody": http_baseline.ResponseBody
        }
        self.fields = ["http_url", "crequesthead", "crequestbody", "cresponsehead", "cresponsebody"]

    def predict(self):
        """keys为 srcIp, dstIp, dstPort, httpHost, httpUrl, requestMsg
        requestMsg为List，每项为dist的keys为 requestHeader, requestBody, responseHeader, responseBody
        """
        self._logger.get_log().info(f"输入的待验证日志：\n{self.sample}")

        for field in self.fields:
            # 如果不存在基线则返回 -1
            if not os.path.exists(os.path.join(self.model_store_dir, f"{field}.model")):
                self._logger.get_log().info(f"{self.http_host}-{self.idstport}的{field}基线目前没有被拟合。")
                return -1

            model = joblib.load(os.path.join(self.model_store_dir, f"{field}.model"))
            self._logger.get_log().info(f"读取{self.http_host}-{self.idstport}的{field}基线成功。")

            data = self.data_map.get(field)
            s = model.predict(data, field)
            self._logger.get_log().info(f"偏离{self.http_host}-{self.idstport}的{field}基线分数为{s}。")
            if s > 0:
                self.anomaly_score += s

        return self.anomaly_score

    def fit(self, *args):
        pass
