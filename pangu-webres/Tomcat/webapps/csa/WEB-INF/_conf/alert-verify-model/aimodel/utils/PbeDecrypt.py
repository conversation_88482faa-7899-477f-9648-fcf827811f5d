# -*- coding:utf-8 -*-
import pyDes
import base64
from Crypto.Hash import MD5  # Linux


def PbeDecrypt(params, salt):
    pwd = 'Y7$gGvFt>a!Q=T{9wSt;78i_'
    count = 1000

    decrypt_data = base64.b64decode(params.encode())

    hasher = MD5.new()
    hasher.update(pwd.encode())
    hasher.update(salt)
    result = hasher.digest()

    for i in range(1, count):
        hasher = MD5.new()
        hasher.update(result)
        result = hasher.digest()

    des = pyDes.des(result[:8], pyDes.CBC, padmode=pyDes.PAD_PKCS5, IV=result[8:16])
    return str(des.decrypt(decrypt_data).decode('utf-8'))


def JaspytDecrypt(encrypt_message):
    if not encrypt_message:
        return ''
    raw_encrypt_bytes = base64.b64decode(encrypt_message)
    encrypt_bytes_arr = list(raw_encrypt_bytes)

    salt_arr = encrypt_bytes_arr[0:8]
    message_arr = encrypt_bytes_arr[8:]

    salt = bytes(salt_arr)
    message = bytes(message_arr)

    return PbeDecrypt(str(base64.b64encode(message), encoding='utf-8'),  salt=salt)
