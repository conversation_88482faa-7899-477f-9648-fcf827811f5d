import numpy as np
from furl import furl
from .base_anomaly import HTTPBaseline
from .base_function import modify_sigmoid
from .value_baseline import ValueBaseline


class URLAnomaly(HTTPBaseline):

    def __init__(self, http_host, idstport):
        """
        用于检测url的异常程度，抽取出参数值和参数名然后再分别打分。
        :param http_host: 以host划分基线
        :param idstport:  对应的端口
        """
        super().__init__(http_host, idstport)

        self.url_keys = None
        self.all_keys = set()
        self.value_detector = {}

        self.baseline_http_url_path = None

    def fit(self, data, field):
        """http_url: [scheme:]//[user[:password]@]host[:port][/path][?query][#fragment]
            以上为url标准格式，需要对url的格式做解析，解析出参数名和参数值。
        :param field: http_url
        :param data: 基线数据，包含http_url
        """
        url_data = data.loc[:, field]
        furl_data = url_data.apply(lambda x: self.fix_furl(x))
        self.url_keys = furl_data.apply(lambda x: set(x.args.keys()))

        # 基线http日志的path
        self.baseline_http_url_path = furl_data.apply(lambda x: x.path.__str__())

        url_keys_uniq = self.url_keys.drop_duplicates()
        for k in url_keys_uniq:
            self.all_keys = self.all_keys.union(k)

        for k in self.all_keys:
            value_data = furl_data.apply(lambda x: x.args.get(k, np.nan)).dropna()
            if value_data.empty:
                continue

            model = ValueBaseline(field=field, key=k).fit(value_data)
            self.value_detector[k] = model

        return self

    def predict(self, http_url, field):
        """
        预测url是否是异常
        :param http_url: 待检测的url
        :param field: 字段名
        :return: 异常分数
        """
        assert field == "http_url", "待检测的url字段名应该为http_url"

        f = furl(http_url)
        http_url_args = f.args
        args_keys = http_url_args.keys()

        # 没有参数值基线模型
        if len(self.value_detector) == 0:
            return 0

        baseline_args_keys = self.url_keys.apply(
            lambda x: len(set(args_keys) - x)
        )
        args_keys_anomaly_score = modify_sigmoid((baseline_args_keys == 0).sum())

        args_values_anomaly_score = 0
        for k in args_keys:
            v = http_url_args[k]
            m = self.value_detector.get(k, None)

            if m is not None:
                args_values_anomaly_score += m.predict(v)
            else:
                pass  # 没有此参数的基线

        return args_keys_anomaly_score + args_values_anomaly_score

    @staticmethod
    def fix_furl(x):
        """有些url解析报错"""
        try:
            return furl(x)
        except ValueError:
            return type("furl", (object,), {"args": {}, "path": ""})
