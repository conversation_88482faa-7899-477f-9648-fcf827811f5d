from html.parser import HTMLParser
import xml.etree.ElementTree as ET
from aimodel.utils.Logger import Logger

logger = Logger(__name__)


def walk_data(root_node, level, result_dict):

    # temp_list = (level, root_node.tag, root_node.text, root_node.attrib)
    if root_node.text:
        result_dict[root_node.tag] = root_node.text

    # 遍历每个子节点
    children_node = root_node.getchildren()
    if len(children_node) == 0:
        return
    for child in children_node:
        walk_data(child, level + 1, result_dict)


def parse_xml(text):
    try:
        level = 1  # 节点的深度从1开始
        result_dict = {}

        root = ET.fromstring(text)
        walk_data(root, level, result_dict)

        return result_dict
    except Exception as e:
        logger.get_log().debug(f"xml格式解析错误：{e}")
        return {}


class MyHTMLParser(HTMLParser):

    def __init__(self):
        super().__init__()
        self.tag_attrs = {}
        self.content = {}
        self.tag = None

    def handle_starttag(self, tag, attrs):
        self.tag = tag
        self.tag_attrs[tag] = attrs

    def handle_data(self, data):
        text = data.strip()
        if text:
            self.content[self.tag] = text

    def get_content(self):
        return self.content


def parse_html(text):
    try:
        if "html" not in str(text).lower():
            logger.get_log().debug(f"不属于html格式：{text}")
            return {}

        parser = MyHTMLParser()
        parser.feed(text)
        return parser.get_content()

    except (TypeError, ValueError) as e:
        logger.get_log().debug(f"html格式解析错误：{e}")
        return {}
