import re
from aimodel.models.BaseValidator import BaseValidator

# windows 常用系统命令
windows_command = [
    'cd', 'dir', 'copy', 'del', 'erase', 'move', 'ren', 'type', 'more',
    'find', 'findstr', 'fc', 'sort', 'start', 'taskkill', 'tasklist',
    'ping', 'ipconfig', 'netstat', 'tracert', 'nslookup', 'net', 'netsh',
    'sc', 'reg', 'schtasks', 'shutdown', 'systeminfo', 'ver', 'vol'
]

# linux 常用系统命令
linux_command = [
    'ls', 'cd', 'pwd', 'rm', 'rmdir', 'mv', 'cp', 'cat', 'more', 'less',
    'head', 'tail', 'grep', 'find', 'touch', 'mkdir', 'chmod', 'chown',
    'which', 'ps', 'kill', 'killall', 'top', 'df', 'du', 'free', 'uname',
    'locate', 'find', 'tar', 'gzip', 'gunzip', 'zip', 'unzip', 'ifconfig'
]


class SystemCommandDetector(BaseValidator):
    def __int__(self, model_name='系统命令匹配'):
        self.model_name = "系统命令匹配"

    def fit(self):
        pass

    def predict(self, text):
        """
        match text with windows_command or linux_command using regular expression
        """
        self._logger.get_log().info(f"待检测文本: {text}")
        for command in windows_command:
            if re.match(command, text):
                self._logger.get_log().info(f"检测到系统命令: {command}")
                return 1
        for command in linux_command:
            if re.match(command, text):
                self._logger.get_log().info(f"检测到系统命令: {command}")
                return 1
        self._logger.get_log().info(f"{text} 未检测到系统命令")
        return 0


if __name__ == "__main__":
    d = SystemCommandDetector()
    print(d.predict("cd test"))
    print(d.predict("ping www"))
