from .base_body import BodyBase
from .base_function import parse_json, parse_k_v, parse_xml, parse_html


class ResponseBody(BodyBase):

    def __init__(self, http_host, idstport):
        super().__init__(http_host, idstport)

        # 响应体目前可解析的文件类型
        self.parse_fun = {
            "json": parse_json,
            "k_v": parse_k_v,
            "xml": parse_xml,
        }
        # 响应体需要解析html文件
        self.parse_html = parse_html
