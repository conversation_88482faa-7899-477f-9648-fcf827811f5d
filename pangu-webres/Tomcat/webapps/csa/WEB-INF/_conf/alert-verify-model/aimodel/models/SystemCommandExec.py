import os
import base64
import numpy as np
from html.parser import HTMLParser
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from aimodel.models.BaseValidator import BaseValidator
from aimodel.algorithms.http_baseline.base_function import parse_json


class HTMLChecker(HTMLParser):
    def __init__(self):
        super().__init__()
        self.is_html = False

    def handle_starttag(self, tag, attrs):
        self.is_html = True


def is_html_parser(text):
    """
    判断给定的文本是否为 HTML 格式。

    Args:
        text (str): 要检查的文本。

    Returns:
        bool: 如果文本是 HTML 格式则返回 True,否则返回 False。
    """
    try:
        parser = HTMLChecker()
        parser.feed(text)
        return parser.is_html
    except (ValueError, TypeError):
        return False


class TextExtractor(HTMLParser):
    def __init__(self):
        super().__init__()
        self.text_list = []
        self.current_text = ""

    def handle_starttag(self, tag, attrs):
        self.text_list.append(self.current_text.strip())
        self.current_text = ""

    def handle_data(self, data):
        self.current_text += data

    def extract_text(self, html_text):
        self.feed(html_text)
        self.text_list.append(self.current_text.strip())
        return self.text_list


class SystemCmdExec(BaseValidator):
    """
    检测文本是否是系统命令执行结果
    检测主体：
    http_url
    http请求体
    http请求响应体

    http请求响应体类型：
    1. html文件 2. json文件 3. xml文件 4. 二进制文件 5. 文本文件 6. 其他文件
    """

    def __init__(self, threshold=0.5):
        super().__init__()
        p1 = self.path_encryptor.decrypt(self.base_path_ep)
        self.model_path = os.path.join(
            p1, self.base64_encryptor.decode("bW9kZWwtbGli"),
            self.base64_encryptor.decode("YWxsLU1pbmlMTS1MNi12Mg==")
        )

        self._logger.get_log().info(f"加载模型")
        self.model = SentenceTransformer(self.model_path)
        self._logger.get_log().info("加载模型完成")

        self.system_cmd_exec_embeddings_path = os.path.join(
            p1, self.base64_encryptor.decode("ZGF0YQ=="),
            self.base64_encryptor.decode("dmVjdG9yX2xpYg=="),
            self.base64_encryptor.decode("c3lzdGVtX2NtZF9leGVjX2VtYmVkZGluZ3MubnB5")
        )
        self._logger.get_log().info("加载系统命令执行结果embedding")
        self.system_cmd_exec_embeddings = np.load(self.system_cmd_exec_embeddings_path)
        self._logger.get_log().info("加载系统命令执行结果embedding完成")

        self.threshold = threshold

    def fit(self, *args):
        pass

    def get_embeddings_batch(self, texts):
        processed_texts = []

        for t in texts:
            if is_html_parser(t):
                parser = TextExtractor()
                extracted_text = parser.extract_text(t)
                processed_texts.append(' '.join(extracted_text))
                continue

            text_values = parse_json(t)  # 如果不为json则输出{}
            if text_values:
                processed_texts.append(' '.join(map(str, text_values.values())))
                continue

            processed_texts.append(t)  # 不为以上两种类型

        # 批量编码
        embeddings = self.model.encode(processed_texts)
        return embeddings

    def predict(self, data):
        data = [i for i in data if "data" in i]

        if len(data) == 0:
            self._logger.get_log().info("data全为空")
            return []

        results = []
        # 提取所有文本内容
        texts = []
        for item in data:
            decode_data = base64.b64decode(item['data']).decode('utf-8', errors='ignore')
            texts.append(decode_data)

        # 批量获取嵌入
        embeddings = self.get_embeddings_batch(texts)

        # 逐个计算相似度和分数
        for i, embedding in enumerate(embeddings):
            similarity = cosine_similarity(embedding.reshape(1, -1), self.system_cmd_exec_embeddings)
            max_similarity = np.max(similarity)

            score = int(((max_similarity - 0.7) / 0.3) * 10)  # 相似度 -> 异常分数

            if score < 0:
                score = 0

            if score > 0:
                self._logger.get_log().info(f"该文本{data[i]['data']}，疑是命令执行返回文本，异常分数为{score}")
                results.append({
                    "id": data[i]["id"],
                    "score": score
                })

        return results
