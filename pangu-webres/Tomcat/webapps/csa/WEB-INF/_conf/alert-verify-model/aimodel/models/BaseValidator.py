import os
import secrets
import base64
from abc import ABC, abstractmethod
from aimodel.utils.Logger import Logger
from aimodel.utils.PathEncryptor import PathEncryptor, Base64Encryptor


class BaseValidator(ABC):

    def __init__(self):
        self._logger = Logger(__name__)
        self.base_path = eval(base64.b64decode('b3MucGF0aC5kaXJuYW1lKG9zLnBhdGguZGlybmFtZShfX2ZpbGVfXykp'))
        self.base64_encryptor = Base64Encryptor()
        self.random_key = secrets.token_urlsafe(32)
        self.path_encryptor = PathEncryptor(self.random_key)
        self.base_path_ep = self.path_encryptor.encrypt(self.base_path)

    @abstractmethod
    def fit(self, *args):
        pass

    @abstractmethod
    def predict(self, *args):
        pass
