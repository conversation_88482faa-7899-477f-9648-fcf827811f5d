import json
from aimodel.utils.Logger import Logger
from aimodel.dbhandler.CkhHandler import CkhHandlerPangu
from aimodel.dbhandler.MySqlHandler import MySqlHandler


class VerifyCore:

    def __init__(self, model_name, input_json, data, model_config):
        self._logger = Logger(__name__)
        self.model_name = model_name
        self.batch_id = input_json.split(".")[0]
        self.data = data

        # ClickHouse Configuration
        self.username = model_config.api_username
        self.api_secret = model_config.api_secret
        self.address = model_config.pangu_api_address
        self.mysql_database = model_config.mysql_database
        self.mysql_host = model_config.mysql_host
        self.mysql_port = model_config.mysql_port
        self.mysql_user = model_config.mysql_user
        self.mysql_password = model_config.mysql_password
        self._ck_handler = None
        self._mysql_handler = None

    def get_ck_handler(self):
        ck_handler = CkhHandlerPangu(username=self.username, apiSecret=self.api_secret, address=self.address)
        return ck_handler

    def get_mysql_handler(self):
        mysql_handler = MySqlHandler(
            self.mysql_database, self.mysql_host, self.mysql_port, self.mysql_user, self.mysql_password
        )
        return mysql_handler

    def run(self):
        result = []

        if self.model_name == "dns_random_string":
            self._logger.get_log().info("调用随机字符串模型")
            from aimodel.models.RandomString import RandomString
            model = RandomString()
            result = model.predict(self.data)

        elif self.model_name == "system_command_exec_result":
            self._logger.get_log().info("调用系统命令执行结果检测模型")
            from aimodel.models.SystemCommandExec import SystemCmdExec
            model = SystemCmdExec()
            result = model.predict(self.data)

        elif self.model_name == "dns_mimic":
            self._logger.get_log().info("调用仿冒域名检测模型")
            from aimodel.models.Mimic import Mimic
            self._mysql_handler = self.get_mysql_handler()
            model = Mimic(self._mysql_handler)
            result = model.predict(self.data)

        elif self.model_name == "http_baseline_model":
            self._logger.get_log().info("调用http日志基线模型")
            from aimodel.models.HTTPBaselineModel import HTTPBaselineModel
            result = []
            for sample in self.data:
                model = HTTPBaselineModel(None, sample)
                score = model.predict()
                if 0 <= score < 3:
                    continue
                else:
                    score = 10 if score > 10 else score
                    result.append({"id": sample["id"], "score": int(score)})

        elif self.model_name == "data_leakage":
            self._logger.get_log().info("调用信息泄露模型")
            from aimodel.models.DataLeakageValidator import DataLeakageValidator
            model = DataLeakageValidator()
            result = model.predict(self.data)

        elif self.model_name == "web_attack_payload":
            self._logger.get_log().info("识别web攻击的payload")
            from aimodel.models.AttackPayload import AttackPayload
            model = AttackPayload()
            result = model.predict(self.data)

        elif self.model_name == "alert_payload_baseline_model":
            self._logger.get_log().info("告警日志payload基线模型")
            from aimodel.models.AlertPayloadBaselineModel import AlertPayloadBaselineModel
            self._ck_handler = self.get_ck_handler()
            model = AlertPayloadBaselineModel(self._ck_handler)
            result = model.predict(self.data)

        elif self.model_name == "http_baseline_alert_payload_model":
            self._logger.get_log().info("http日志基线对告警日志payload检测模型")
            from aimodel.models.HTTPBaselineAlertPayloadModel import HTTPBaselineAlertPayloadModel
            self._ck_handler = self.get_ck_handler()
            model = HTTPBaselineAlertPayloadModel(self._ck_handler)
            result = model.predict(self.data)

        elif self.model_name == "http_sample_and_detect_model":
            self._logger.get_log().info("http采样日志调用攻击载荷、命令执行、日志基线模型格式")
            from aimodel.models.HTTPSampleAndDetectModel import HTTPSampleAndDetectModel
            model = HTTPSampleAndDetectModel()
            result = model.predict(self.data)

        result = json.dumps(result, ensure_ascii=False)
        self._logger.get_log().info(f"检测结果：{result}")
        return result
