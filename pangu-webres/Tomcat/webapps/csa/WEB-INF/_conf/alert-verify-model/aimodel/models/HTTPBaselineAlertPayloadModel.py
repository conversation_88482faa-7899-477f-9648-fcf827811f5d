import os
import time
import datetime
import numpy as np
from sentence_transformers import SentenceTransformer, util
from aimodel.models.BaseValidator import BaseValidator


class HTTPBaselineAlertPayloadModel(BaseValidator):

    def __init__(self, ck_handler):
        super().__init__()
        self._ck_handler = ck_handler
        self.event_table_1 = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime('%y%m%d')

        self._logger.get_log().info("读取预训练模型。")
        p1 = self.path_encryptor.decrypt(self.base_path_ep)
        self.LM_model_path = os.path.join(p1, 'model-lib', 'all-MiniLM-L6-v2')
        self.model = SentenceTransformer(self.LM_model_path)

        # 模型保持路径
        self.store_dir = os.path.join(p1, 'model-lib', 'http_baseline_alert_payload_model')
        if not os.path.exists(self.store_dir):
            os.makedirs(self.store_dir)

    def get_baseline_data(self, cdstip, idstport, field):
        """获取需要建模的host-port"""
        # 筛选满足http日志数量的host
        sql = """
        select {field}
        from {event_table_1}
        where csrclogtype = '/security/IDS/HTTP'
        and cdstip = '{cdstip}' and idstport = {idstport} and notEmpty({field})
        group by {field}
        limit 300
        """.format(
            event_table_1="event20" + self.event_table_1,
            cdstip=cdstip,
            idstport=idstport,
            field=field
        )
        self._logger.get_log().info(f"执行查询{cdstip}:{idstport}的基线数据，SQL语句：\n{sql}")
        data = self._ck_handler.get_data_func(sql)

        return data

    def fit(self, field, data, cdstip_store_dir):
        embeddings = self.model.encode(data.values.reshape(1, -1))
        np.save(f"{cdstip_store_dir}/{field}.npy", embeddings)

    def predict(self, data):
        """输出告警payload与该目的IP+目的端口上的HTTP日志中的一致字段（url、requestheader、requestbody、responseheader、responsebody）"""
        result = []

        filed_map = {
            "url": "http_url",
            "requestheader": "crequesthead",
            "requestbody": "crequestbody",
            "responseheader": "cresponsehead",
            "responsebody": "cresponsebody",
        }
        field_list = ["url", "requestbody", "responsebody"]

        self._logger.get_log().info(f"输入的待验证日志：\n{data}")
        for sample in data:
            payload_list = []
            id_ = sample.get("id")
            csrcip = sample.get("srcIp")
            cdstip = sample.get("dstIp")
            idstport = sample.get("dstPort")
            ceventname = sample.get("eventName")
            ext_info = sample.get("extInfo")
            for i in ext_info:
                if i.get("payload"):
                    payload_list.append(i.get("payload"))

            if len(payload_list) == 0:
                self._logger.get_log().info(f"输入的待验证日志{ceventname}的payload为空")
                continue

            cdstip_store_dir = os.path.join(self.store_dir, f"{cdstip}_{idstport}")
            if not os.path.exists(cdstip_store_dir):
                os.makedirs(cdstip_store_dir)

            queries_embeddings = self.model.encode(payload_list)
            for field in field_list:
                data = self.get_baseline_data(cdstip, idstport, filed_map[field])

                if len(data) <= 20:
                    self._logger.get_log().info(f"{cdstip}:{idstport}基线{field}非空数据少于20条，不拟合基线。")
                    continue

                # 判断是否需要重新拟合模型
                if not os.path.exists(f"{cdstip_store_dir}/{field}.npy"):
                    self.fit(field, data, cdstip_store_dir)
                else:
                    create_time = os.path.getctime(f"{cdstip_store_dir}/{field}.npy")
                    days_between = (time.time() - create_time) / (24 * 60 * 60)
                    if days_between > 3:
                        self._logger.get_log().info(f"{cdstip}_{idstport}的{field}模型过期，重新拟合。")
                        os.remove(f"{cdstip_store_dir}/{field}.npy")
                        self.fit(field, data, cdstip_store_dir)
                    else:
                        self._logger.get_log().info(f"{cdstip}_{idstport}的{field}模型已存在")

                embeddings = np.load(f"{cdstip_store_dir}/{field}.npy")
                hits = util.semantic_search(queries_embeddings, embeddings, top_k=1)
                similarity = hits[0][0]["score"]

                if similarity > 0.7:
                    result.append(
                        {"id": id_, "similar_field": field, "similar_score": similarity * 100}
                    )

        self._logger.get_log().info(f"http日志基线对告警日志payload检测模型结果{result}")

        return result
