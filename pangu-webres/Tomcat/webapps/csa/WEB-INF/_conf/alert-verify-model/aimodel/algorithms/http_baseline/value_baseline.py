"""对参数值进行检测，区分不同值的类型。
"""
import pandas as pd
from . import value_detector


class ValueBaseline:

    def __init__(self, field, key):
        """
        用于对参数的参数值进行拟合基线
        Parameters
        ----------
        field : str 参数来源的字段
        key : str 参数
        """
        self.field = field
        self.key = key
        self.key_type = None
        self.model = None

    def fit(self, data):
        """
        基于self.field字段中self.key参数的参数值拟合基线，需要判断值的类型
        Parameters
        ----------
        data : pd.Series 参数值

        Returns
        -------
        self : 拟合好的实例
        """
        n_sample = data.shape[0]

        value_counts = data.value_counts(ascending=False, normalize=False)
        df = pd.DataFrame({"values": value_counts.index, "counts": value_counts.values})

        # 判断布尔值所占的比例
        v_b = df["values"].map(lambda x: value_detector.is_boolean(x))
        r_b = sum(df["counts"][v_b]) / n_sample
        if r_b > 0.95:
            self.key_type = "boolean"
            self.model = value_detector.BooleanDetector().fit(value_counts)
            return self

        # 判断数字值所占的比例
        v_n = df["values"].map(lambda x: value_detector.is_numeric(x))
        r_n = sum(df["counts"][v_n]) / n_sample
        if r_n > 0.95:
            self.key_type = "numeric"
            self.model = value_detector.NumericDetector().fit(data, value_counts)
            return self

        # 判断是否是时间
        v_t = df["values"].map(lambda x: value_detector.is_time(x))
        r_t = sum(df["counts"][v_t]) / n_sample
        if r_t > 0.95:
            self.key_type = "time"
            self.model = value_detector.TimeDetector()
            return self

        # 判断是否可能是枚举类型
        is_e = value_detector.is_enum(value_counts, n_sample)
        if is_e:
            self.key_type = "enum"
            self.model = value_detector.EnumDetector().fit(value_counts)
            return self

        # 判断是否是字符串（当普通字符串处理）
        v_s = df["values"].map(lambda x: value_detector.is_string(x))
        r_s = sum(df["counts"][v_s]) / n_sample
        if r_s > 0.95:
            self.key_type = "string"
            self.model = value_detector.StringDetector().fit(data)
            return self

        return self

    def predict(self, data):
        if self.model is None:
            # 该基线模型没有拟合
            return 0
        else:
            s = self.model.predict(data)
            return s
