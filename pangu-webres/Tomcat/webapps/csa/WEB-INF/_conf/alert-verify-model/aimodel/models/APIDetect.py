"""code +++ bug ---
"""
import os
import warnings
import re
import datetime
import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import CountVectorizer
import lightgbm as lgb

warnings.filterwarnings("ignore")


class APIDetection:

    def __init__(self, ck_handler):
        self.ck_handler = ck_handler

        self.event_table = datetime.datetime.now().strftime('%y%m%d')

        self.path = os.path.abspath(__file__)
        self.data_path = os.path.join(
            os.path.dirname(os.path.dirname(self.path)), 'data', 'APIDetectionData', "training_set.csv")

        self.result_api = set()
        self.result_notapi = set()
        self.result_unkonw = set()

        training_set = pd.read_csv(self.data_path)
        X = training_set.drop(["labels"], axis=1)
        Y = training_set["labels"]
        self.clf = lgb.LGBMClassifier()
        self.clf.fit(X, Y)

    def get_data(self, http_host):
        sql = """
        select distinct http_url, method, countSubstrings(http_url, '/') as n_s, extract(http_url, '^/(.+?)/') as f_s_c,
            lower(arrayElement(splitByRegexp('[,;]', response_content_type), 1)) as r_c_t
        from {event_table}
        where csrclogtype = '/security/IDS/HTTP'
        and method in ('POST', 'GET', 'PUT', 'DELETE')
        and notEmpty(http_host) and cdstipinner = 1 and startsWith(http_url, '/')
        and match(http_url, '\?') = 0 and match(crequestbody, '\w+=\w+') = 0 
        and multiSearchAny(http_url, ['svn']) = 0
        and n_s > 1
        and http_host = '{http_host}'
        """.format(
            event_table="event20" + self.event_table,
            http_host=http_host
        )

        data = self.ck_handler.get_data_func(sql)

        return data

    def get_features(self, data, length):
        max_features = int(length / 10)
        max_features = 30 if max_features > 30 else max_features

        # (bag-of-words)
        vc = CountVectorizer(
            lowercase=False,
            token_pattern=r"(?u)\b\w\w+\b",  # r"(?u)\b[a-zA-Z_]+\b"
            ngram_range=(1, 3),
            analyzer="word",
            # min_df=0.01,
            max_features=max_features,
            binary=True
        )

        matrix = vc.fit_transform(data)
        df = pd.DataFrame(matrix.toarray(), columns=vc.get_feature_names_out())

        return df

    def cluster(self, df, length):
        n_clusters = 1 if int(length / 20) == 0 else int(length / 20)
        n_clusters = 10 if n_clusters > 10 else n_clusters

        self.model = KMeans(
            n_clusters=n_clusters,
            init='k-means++',
            random_state=0
        ).fit(df)

        return self.model.labels_, n_clusters

    def judge_by_model(self, data, item_list, index):
        item_len = len(item_list)
        d_examine = data[item_list[index]]
        length = len(d_examine)
        # f1
        f1 = len(d_examine.unique()) # 本层宽度

        if f1 == 1:
            return "continue"

        d_examine.dropna(inplace=True)
        # f2
        value_frequency = pd.value_counts(d_examine) / length
        f2 = sum((-1) * np.log2(value_frequency) * value_frequency)  # 熵值
        # f3
        f3 = np.unique(d_examine.map(lambda x: len(x)).values).size  # 长度的唯一值个数
        # f4
        f4 = np.sum(d_examine.map(lambda x: bool(re.search(r'\d', x))).values == True) / length  # 含有数字的比例
        # f5
        f5 = index  # 层级深度
        # f6
        f6 = 1 if len(item_list) == (index + 1) else 0  # 是否是结尾项
        # f7
        f7 = 0 if f6 == 1 else len(data[item_list[(index+1)]].unique())  # 下层宽度
        # f8
        f8 = np.sum(d_examine.map(lambda x: bool(re.search(r'[a-zA-Z]', x))).values == True) / length


        feature = pd.DataFrame({"f1": [f1], "f2": [f2], "f3": [f3], "f4": [f4],
                                "f5": [f5], "f6": [f6], "f7": [f7], "f8": [f8]}
                               )

        r = self.clf.predict(feature)

        if r == 1:
            return True
        elif r == 0:
            return False

    def run(self, http_host):
        # result
        self.data_unknow = pd.DataFrame(columns=["http_url", "method", "n_s", "r_c_t"])
        self.data_noapi = pd.DataFrame(columns=["http_url", "method", "n_s", "r_c_t"])
        self.data_api = pd.DataFrame(columns=["http_url", "method", "n_s", "r_c_t"])

        data_all = self.get_data(http_host)

        for i, data in data_all.groupby(["f_s_c", "method", "r_c_t"]):

            length = len(data)
            length_f_s_c = len(i[0])

            if length < 5 or length_f_s_c >= 30:
                self.data_unknow = pd.concat([self.data_unknow, data.drop("f_s_c", axis=1)])
                self.data_unknow = pd.concat([self.data_unknow, pd.DataFrame({"http_url": ["噪声数据"]})])
                continue

            data_n_s = data["n_s"].values.ravel()
            count_f_t = sum(
                    data["http_url"].str.contains(
                        pat=r'xml|zip|jar|pom|model',
                        flags=re.IGNORECASE,
                        case=False,
                        regex=True
                    )
            )

            if (np.max(data_n_s) > 9 and np.min(data_n_s) < 5) or count_f_t > length * 0.5:
                print(data.head(100))
                # self.get_static_resource_server()
                pass

            else:
                self.get_api(data)

        return self.data_unknow, self.data_noapi, self.data_api, self.result_api

    def get_api(self, input_data):

        for _, data_n_s in input_data.groupby(["n_s"]):

            length = len(data_n_s)

            if length < 3: # 缺乏对照
                continue

            if length > 100:
                df = self.get_features(data_n_s.http_url, length)
                labels, n_clusters = self.cluster(df, length)
                data_n_s["labels"] = labels
            else:
                n_clusters = 1
                data_n_s["labels"] = np.zeros(length, dtype=int)

            for index in range(n_clusters):
                r_invest = set()
                # 特征
                cluster_ = data_n_s[data_n_s["labels"] == index].drop("labels", axis=1)

                if len(cluster_) == 0:
                    continue

                count_pjh = sum(
                    cluster_["http_url"].str.contains(
                        pat=r'js|xml|html|htm|woff|ttf|png|css|svg|jpg|jpeg|gif|mp3|tgz|php|fh5',
                        flags=re.IGNORECASE,
                        case=False,
                        regex=True
                    )
                )

                if count_pjh > cluster_.shape[0] * 0.5:
                    self.result_notapi = self.result_notapi | set(cluster_["http_url"])
                    self.data_noapi = pd.concat([self.data_noapi, cluster_.drop("f_s_c", axis=1)])
                    self.data_noapi = pd.concat(
                        [self.data_noapi, pd.DataFrame({"http_url": ["此类中大概率为静态资源"]})]
                   )
                    continue

                # split
                cluster_split = cluster_["http_url"].str.split("/", n=-1, expand=True)

                item_list = ["item" + str(i) for i in range(cluster_split.shape[-1])]
                cluster_split.columns = item_list
                item_len = len(item_list)

                for v, data in cluster_split.groupby(["item0", "item1"]):

                    if len(data) < 3:  # 缺乏对照无法判断
                        continue

                    index = 2
                    while index < item_len:

                        j = self.judge_by_model(data, item_list, index)

                        if j == "continue":
                            index += 1
                            continue

                        elif j is True:
                            data[item_list[index]] = "{v}"
                            index += 1

                        elif j is False:
                            break

                    if index == item_len:
                        r_set = set(data["item0"].str.cat(data[item_list[1:]], sep="/"))
                        self.result_api = self.result_api | r_set
                        r_invest = r_invest | r_set
                        print(r_set)
                        continue
                    else:
                        for i in self.group_api(item_list, index, data):
                            if i:
                                print(i)
                                r_invest = r_invest | i
                        self.result_api = self.result_api | r_invest

                if r_invest:
                    self.data_api = pd.concat([self.data_api, cluster_.drop("f_s_c", axis=1).head(500)], ignore_index=False)
                    self.data_api = pd.concat(
                        [self.data_api, pd.DataFrame({"http_url": [";".join(list(r_invest))]})],
                        ignore_index=False
                    )

                else:
                    self.data_unknow = pd.concat([self.data_unknow, cluster_.drop("f_s_c", axis=1)])
                    self.data_unknow = pd.concat(
                        [self.data_unknow, pd.DataFrame({"http_url": ["此类中没有识别出路径中有参数的api"]})]
                    )

    def group_api(self, item_list, index, data):
        item_len = len(item_list)

        for _, data_ in data.groupby(item_list[index]):
            index_ = index + 1
            if len(data_) < 2:
                if "api" in "".join(str(i) for i in data_.values.ravel()):
                    r_set = set(data_["item0"].str.cat(data_[item_list[1:]], sep="/"))
                    yield r_set

                else:
                    yield None

                continue

            while index_ < item_len:

                j = self.judge_by_model(data_, item_list, index_)

                if j == "continue":
                    index_ += 1
                    continue

                elif j is True:
                    data_[item_list[index_]] = "{v}"
                    index_ += 1

                elif j is False:
                    break

            if index_ == item_len:
                r_set = set(data_["item0"].str.cat(data_[item_list[1:]], sep="/"))
                yield r_set

            else:
                for i in self.group_api(item_list, index_, data_):
                    yield i

    def get_static_resource_server(self):
        pass
