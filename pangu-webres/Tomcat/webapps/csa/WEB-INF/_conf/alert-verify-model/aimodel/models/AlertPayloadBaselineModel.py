import jieba
import datetime
import functools
from aimodel.models.BaseValidator import BaseValidator


def jaccard_similarity(set1, set2):
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return intersection / union


class AlertPayloadBaselineModel(BaseValidator):

    def __init__(self, ck_handler):
        super(AlertPayloadBaselineModel, self).__init__()
        self.ck_handler = ck_handler
        self.event_table = datetime.datetime.now().strftime('%y%m%d')

    def get_data_via_ceventname(self, cdstip, idstport, ceventname):
        sql = """
        select csrcip, cdstip, idstport, ceventname, payload
        from {event_table}
        where cdstip = '{cdstip}' and idstport = {idstport}
        and ceventname = '{ceventname}'
        """.format(
            event_table="event20" + self.event_table,
            cdstip=cdstip,
            idstport=idstport,
            ceventname=ceventname
        )
        self._logger.get_log().info(f"{sql}")
        df =  self.ck_handler.get_data_func(sql)

        return df

    def fit(self, df):
        pass

    @staticmethod
    def cut(p):
        test_puc = ' ~`!@#$%^&*()-_+={}[]|\\:;\'",.<>?/，。《》？：；“‘、'

        r = []
        for i in p:
            if isinstance(i, str):
                words = jieba.cut(i, cut_all=False)
                r.append({i for i in words if i not in test_puc})

        return r

    def predict(self, data_input):
        """根据ceventname查询历史告警数据
        基线1：csrcip、cdstip、idstport、ceventname相同的payload
        基线2：cdstip、idstport、ceventname相同的payload
        """
        result = []
        ip_set = set()
        threshold = data_input.get("threshold")
        data = data_input.get("data")

        for d in data:
            payload_list = []

            # 传入的信息
            score1 = 0
            score2 = 0
            id_ = d.get("id")
            csrcip = d.get("srcIp")
            cdstip = d.get("dstIp")
            idstport = d.get("dstPort")
            ceventname = d.get("eventName")
            ext_info = d.get("extInfo")
            for i in ext_info:
                payload_list.append(i.get("payload"))

            df = self.get_data_via_ceventname(cdstip, idstport, ceventname)

            if len(df) == 0:
                continue

            input_ = self.cut(payload_list)
            baseline_ = self.cut(df["payload"].to_list())

            for i_p in input_:
                f = functools.partial(jaccard_similarity, set2=i_p)
                r = map(f, baseline_)
                for score, s_ip in zip(r, df["csrcip"]):
                    if score > threshold / 100:
                        if s_ip == csrcip:
                            score1 += 1
                        score2 += 1
                        ip_set.add(s_ip)
            if score1 > 0 or score2 > 0:
                result.append({"id": id_, "score1": score1, "score2": score2, "score3": len(ip_set)})
        self._logger.get_log().info(f"告警日志payload基线模型结果：{result}")

        return result
