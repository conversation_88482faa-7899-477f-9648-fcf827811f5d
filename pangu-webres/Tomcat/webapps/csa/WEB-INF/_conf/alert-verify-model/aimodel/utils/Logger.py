import os
import sys
import logging
import logging.handlers
from aimodel.constants import GlobalConsts


class Logger:

    def __init__(self, logger_name):
        level = GlobalConsts.get_value("log_level")

        log_path = GlobalConsts.get_value("log_path")
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        filename = os.path.join(log_path, "model.log")

        logger_level_map = {
            'debug': logging.DEBUG, 'info': logging.INFO, 'warning': logging.WARNING, 'error': logging.ERROR
        }

        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(logger_level_map.get(level))

        if not self.logger.handlers:
            filehandler = logging.handlers.RotatingFileHandler(
                filename=filename,
                maxBytes=10 * 1024 * 1024,  # 每个日志文件的最大字节数
                backupCount=0, # 保存日志备份个数
                encoding='utf-8'
            )

            filehandler.suffix = '%Y-%m-%d.log'
            filehandler.setLevel(logger_level_map.get(level))

            ch = logging.StreamHandler(sys.stdout)
            ch.setLevel(logger_level_map.get(level))

            formatter = logging.Formatter(
                '%(asctime)s-%(module)s-%(funcName)s-%(levelname)s-line[%(lineno)s]-[%(thread)s]: %(message)s'
            )

            filehandler.setFormatter(formatter)
            ch.setFormatter(formatter)

            self.logger.addHandler(filehandler)
            # self.logger.addHandler(ch)

    def get_log(self):
        """定义一个函数，回调logger实例"""
        return self.logger
