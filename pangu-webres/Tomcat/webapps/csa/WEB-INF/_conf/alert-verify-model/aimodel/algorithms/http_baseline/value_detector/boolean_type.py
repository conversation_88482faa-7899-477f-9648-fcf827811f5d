from .base import BaseDetector
from .type_detector import is_boolean
from ..base_function import modify_sigmoid


class BooleanDetector(BaseDetector):

    def __init__(self):
        self.values_counts = None

    def fit(self, data):
        self.values_counts = data
        return self

    def predict(self, data):
        if is_boolean(data):
            if data not in self.values_counts.index:
                return 0.7
            else:
                s = modify_sigmoid(self.values_counts[data], alpha=0.7)
                return s
        else:
            return 1
