"""code +++ bug ---
"""
import os
import math
import pandas as pd
import numpy as np
import tldextract
from collections import Counter
from sklearn.feature_extraction.text import CountVectorizer
# from sklearn.preprocessing import LabelEncoder
import lightgbm as lgb


def get_second_level_domain_name(domain):
    res = tldextract.extract(domain)
    return res.domain


def get_three_level_domain_name(domain):
    res = tldextract.extract(domain)
    return res.subdomain.split(".")[-1]


def get_four_level_domain_name(domain):
    res = tldextract.extract(domain)
    res_list = res.subdomain.split(".")
    return res_list[-2] if len(res_list) > 1 else ""


def entropy(domain_name):
    """ Function which computes the entropy of a given domain name based on it's chars """
    elements, length = Counter(domain_name), len(domain_name)

    return -sum(element / length * math.log(element / length, 2) for element in elements.values())


def get_domain_name(domain):
    """ Function which extracts domain name from subdomain name """
    res = tldextract.extract(domain)
    return res.domain if len(res.domain) > len(res.subdomain) or entropy(res.domain) > entropy(
        res.subdomain) else res.subdomain


class DGAModel:

    def __init__(self):
        self.alexa_vc = None
        self.dict_vc = None
        self.alexa_counts = None
        self.dict_counts = None

        self.lgb_clf = None

    @staticmethod
    def read_training_data():
        """读取训练数据"""

        dga_domains = pd.read_csv(
            os.path.join(os.path.dirname(__file__), "dga_domain.txt"), sep="\s+",
            names=["source", "domain_name", "date", "second", "first", "time"]
        )
        dga_domains.drop(["source", "date", "second", "first", "time"], axis=1, inplace=True)
        dga_domains["label"] = "dga"

        legit_domains = pd.read_csv(
            os.path.join(os.path.dirname(__file__), "normal_domain.csv"),
            names=["domain_name"]
        )
        legit_domains["label"] = "legit"

        dga_domains = dga_domains.loc[:legit_domains.shape[0] - 1, :]
        data = pd.concat([dga_domains, legit_domains])

        data["domain"] = data["domain_name"].apply(lambda domain: get_domain_name(domain))

        split_condition = data["label"] == "legit"
        legit = data[split_condition]
        dga = data[~split_condition]

        words_df = pd.read_csv(
            os.path.join(os.path.dirname(__file__), "words.txt"),
            names=["word"],
            encoding="utf-8",
            header=None,
            dtype={"word": np.str_},
            sep="\t"
        )

        return data, legit, dga, words_df

    def preprocess(self, data):
        """特征抽取"""
        data["length"] = data["domain"].apply(lambda domain: len(domain))
        data["entropy"] = data["domain"].apply(lambda domain: entropy(domain))
        data["alexa_grams"] = self.alexa_counts * self.alexa_vc.transform(data["domain"]).T
        data["words_grams"] = self.dict_counts * self.dict_vc.transform(data["domain"]).T

        return data

    def fit(self):
        """拟合模型"""
        data, legit, dga, words_df = self.read_training_data()

        # alexa n-gram feature
        self.alexa_vc = CountVectorizer(analyzer="char", ngram_range=(3, 5), min_df=0.00001, max_df=1.0)
        counts_matrix = self.alexa_vc.fit_transform(legit["domain"])
        self.alexa_counts = np.log10(np.asarray(counts_matrix.sum(axis=0)).flatten())

        words_df = words_df[words_df["word"].map(lambda word: str(word).isalpha())]
        words_df = words_df.applymap(lambda word: str(word).strip().lower())
        words_df = words_df.dropna().drop_duplicates()

        # words n-gram feature
        self.dict_vc = CountVectorizer(analyzer="char", ngram_range=(3, 5), min_df=0.00001, max_df=1.0)
        words_counts_matrix = self.dict_vc.fit_transform(words_df["word"])
        self.dict_counts = np.log(np.asarray(words_counts_matrix.sum(axis=0)).flatten())

        data = self.preprocess(data)
        X = data.drop(["label", "domain_name", "domain"], axis=1)
        Y = data["label"]

        self.lgb_clf = lgb.LGBMClassifier()
        self.lgb_clf.fit(X, Y)

        return self

    def predict(self, data):
        """

        Parameters
        ----------
        data: pd.DataFrame
            data = pd.DataFrame({"domain": ["baidu", "google", ...]})
        Returns
        -------
        labels: dga or legit domain name
        """
        data = self.preprocess(data)
        test_X = data[["length", "entropy", "alexa_grams", "words_grams"]]
        labels = self.lgb_clf.predict_proba(test_X)

        return labels
