from aimodel.models.BaseValidator import BaseValidator
from aimodel.models.AttackPayload import AttackPayload
from aimodel.models.DataLeakageValidator import DataLeakageValidator
from aimodel.models.HTTPBaselineModel import HTTPBaselineModel


class HTTPSampleAndDetectModel(BaseValidator):

    def __init__(self):
        super().__init__()


    def fit(self, *args):
        pass

    def predict(self, data):
        """调用三个模型进行检测
        1. url、请求头是否具备攻击载荷
        2. 响应头是否是系统命令执行结果、信息泄露。
        3. 调用日志基线模型。
        """
        result = []
        ap_model = AttackPayload()
        dl_model = DataLeakageValidator()

        for sample in data:
            s_r = dict()

            ext_info = sample.get("extInfo")
            match = 0
            mismatch = 0
            ap_r = dict()  # AttackPayload模型返回结果
            dl_r = dict()  # DataLeakageValidator模型返回结果

            self._logger.get_log().info("执行攻击载荷")
            ap_r_ = ap_model.predict_(sample)
            ap_r.update(ap_r_)

            self._logger.get_log().info("判断是否是系统命令执行结果和数据泄露")
            dl_r_ = dl_model.predict_(sample)
            dl_r.update(dl_r_)

            self._logger.get_log().info("判断是否偏离日志基线")
            for e in ext_info:

                d = {
                    "srcIp": sample.get("srcIp"),
                    "dstIp": sample.get("dstIp"),
                    "dstPort": sample.get("dstPort"),
                    "httpHost": sample.get("httpHost"),
                    "httpUrl": e.get("httpUrl"),
                    "extInfo": [e],
                }

                hb_model = HTTPBaselineModel(None, d)
                score = hb_model.predict()
                if 0 < score < 3:
                    match += 1
                elif score > 3:
                    mismatch += 1

            s_r["sign"] = sample.get('sign')
            s_r["attack"] = ap_r
            s_r["command"] = dl_r
            s_r["match"] = match
            s_r["mismatch"] = mismatch

            result.append(s_r)

        self._logger.get_log().info(f"http采样日志调用攻击载荷、命令执行结果、信息泄露、日志基线模型结果：{result}")
        return result
