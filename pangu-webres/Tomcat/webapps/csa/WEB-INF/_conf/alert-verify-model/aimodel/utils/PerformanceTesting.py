import time
import functools
from aimodel.utils.Logger import Logger


logger = Logger(__name__)


def timeit_class(cls):
    def _timeit(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            logger.get_log().info(f"Execution time of {func.__name__}: {execution_time:.6f} seconds")
            return result

        return wrapper

    class Wrapper:
        def __init__(self, *args, **kwargs):
            self.wrapped = cls(*args, **kwargs)

        def __getattr__(self, name):
            attr = getattr(self.wrapped, name)
            if callable(attr):
                return _timeit(attr)
            return attr

    return Wrapper
