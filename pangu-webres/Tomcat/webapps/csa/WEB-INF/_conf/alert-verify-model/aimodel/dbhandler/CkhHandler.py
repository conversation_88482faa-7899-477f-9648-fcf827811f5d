import sys
import time
import uuid
import json
import _sha256
import requests
import pandas as pd
from aimodel.utils.Logger import Logger
from aimodel.utils.Base64Shift2 import Base64Shift2
from aimodel.exceptions.InterFaceException import RestfulException


class Ckh<PERSON>andlerPangu(object):
    """用于restful请求盘古ck数据"""
    def __init__(self, username, apiSecret, address):
        self._logger = Logger(__name__)
        try:
            # http restful 请求参数
            self.request_dic = {
                "username": username,
                "apiSecret": apiSecret,
                "address": address,
                "tokenApi": address + "/api/v1/token/get"
            }
            token_api_address = self.request_dic['tokenApi']
            self._token = self.getToken(token_api_address).replace('"', '')
            self.db_api = self.request_dic['address'] + "/api/v2/ckh/_executePS"
        except Exception as e:
            self._logger.get_log().error('Clickhouse 连接失败')
            self._logger.get_log().error(e)
            sys.exit(-1)

    @property
    def token(self):
        return self._token

    def getSha256(self, params):
        """
        # 将参数按照values排序
        # 将参数按照字符串拼接
        # 返回sha256加密后的字符串
        """

        values = sorted(params.values())
        params_str = "".join(values)
        return _sha256.sha256(params_str.encode('utf-8')).hexdigest()

    def getRequestBody(self):
        """
        description: 获取请求body
        构造四个请求参数：username：配置项生成，timestamp：由time生成，nonce：uuid生成，signature：sha256生成
        Returns
        -------
        返回构造好的请求参数字典

        """
        timestamp = str(round(time.time() * 1000))
        nonce = str(uuid.uuid4())
        params = {
            "apiSecret": self.request_dic['apiSecret'],
            "timestamp": timestamp,
            "nonce": nonce
        }
        signature = self.getSha256(params)
        self._logger.get_log().info("username: %s" % self.request_dic['username'])
        self._logger.get_log().info("timestamp: %s" % timestamp)
        self._logger.get_log().info("nonce: %s" % nonce)
        self._logger.get_log().info("signature: %s" % signature)
        form = {
            "username": self.request_dic['username'],
            "timestamp": timestamp,
            "nonce": nonce,
            "signature": signature
        }
        return form

    def getToken(self, token_api_address):
        """
        description: 获取token
        Parameters
        ----------
        token_api_address: str 盘古token_api地址
        Returns
        -------
        返回token
        """
        request_body = self.getRequestBody()
        headers = {
            "accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        response = requests.request("POST", token_api_address, data=request_body, headers=headers, verify=False)
        return response.text

    # 传入token，sql，返回ckh json数据
    def get_data_func(self, sql):
        headers = {
            "accept": "application/json",
            "Authorization": "Bearer " + self._token,
            "Content-Type": ""
        }
        # sql = sql.encode('utf-8')
        encrypted_sql = Base64Shift2(sql)  # sql字符串加密
        sql_data = json.dumps({"sql": encrypted_sql, "params": []}, ensure_ascii=False).encode('utf-8')

        res = requests.post(url=self.db_api, data=sql_data, headers=headers, verify=False)
        self._logger.get_log().info(f'请求数据sql的api状态码：{res.status_code}')
        json_data = json.loads(res.text)
        self._logger.get_log().info(f'请求数据api返回的msg：{json_data["data"]["msg"]}')
        if 'data' not in list(json_data.keys()):
            raise RestfulException("请求数据api没有返回data")
        data = json_data['data']['jsonData']
        df_data = pd.DataFrame(json.loads(data))
        return df_data
