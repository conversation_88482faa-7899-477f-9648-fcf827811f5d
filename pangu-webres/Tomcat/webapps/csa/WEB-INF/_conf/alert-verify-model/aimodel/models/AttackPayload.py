import os
import numpy as np
import pandas as pd
import tensorflow as tf
from aimodel.models.BaseValidator import BaseValidator


class AttackPayload(BaseValidator):

    def __init__(self):
        super().__init__()
        self.model = None
        self.path = os.path.abspath(__file__)
        self.model_path = os.path.join(os.path.dirname(os.path.dirname(self.path)), 'model-lib', 'fusion_model')

        self.label_map = {
            1: "SQLInjection",
            2: "DirectoryTraversal",
            3: "CodeExecution",
            4: "CommandExecution",
            5: "XSSInjection"
        }

    def fit(self):
        pass

    def predict(self, data):
        """只检测payload字段"""
        self.model = tf.keras.models.load_model(self.model_path)
        result = []

        for sample in data:
            value = []

            id_ = sample["id"]
            request_msg = sample["extInfo"]

            for r_M in request_msg:
                if r_M == {}:
                    continue

                payload = r_M.get("payload", None)

                if payload is None or "�" in payload:
                    continue
                else:
                    value.append(payload)

            if len(value) == 0:
                continue

            test_set = pd.DataFrame({"payload": value})
            pred = self.model.predict(test_set["payload"], batch_size=512)

            s = np.max(pred[:, 1:])
            score = int(((s - 0.7) / 0.3) * 10)

            if score > 0:
                result.append({"id": id_, "score": score})

        return result

    def predict_(self, sample):
        """检测url和请求体是否具有攻击payload
        """
        result = dict()

        if self.model is None:
            self.model = tf.keras.models.load_model(self.model_path)

        value = []

        for e in sample["extInfo"]:
            value.append(e.get("httpUrl", ""))
            value.append(e.get("requestBody", ""))

        test_set = pd.DataFrame({"payload": value})
        pred = self.model.predict(test_set["payload"], batch_size=512)

        b = np.argmax(pred, axis=1)
        for i in range(len(b)):
            label = b[i]
            if label > 0:
                l = self.label_map.get(label)
                if l in result:
                    result[l] = result[l] + 1
                else:
                    result[l] = 1

        return result
