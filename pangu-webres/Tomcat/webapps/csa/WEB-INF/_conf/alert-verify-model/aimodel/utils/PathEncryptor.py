from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes
import base64


class PathEncryptor:
    def __init__(self, master_password):
        self.salt = get_random_bytes(16)
        self.key = PBKDF2(master_password.encode('utf-8'), self.salt, dkLen=32, count=100000, hmac_hash_module=SHA256)

    def encrypt(self, path):
        # 创建加密器
        cipher = AES.new(self.key, AES.MODE_CBC)
        iv = cipher.iv
        # 加密路径
        encrypted_path = cipher.encrypt(pad(path.encode('utf-8'), AES.block_size))
        # 返回iv和加密后的路径，使用base64编码
        return base64.b64encode(iv + encrypted_path).decode('utf-8')

    def decrypt(self, encrypted_path):
        # 解码base64
        encrypted_data = base64.b64decode(encrypted_path)
        iv = encrypted_data[:AES.block_size]
        encrypted_path = encrypted_data[AES.block_size:]
        # 创建解密器
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        # 解密路径
        decrypted_path = unpad(cipher.decrypt(encrypted_path), AES.block_size)
        return decrypted_path.decode('utf-8')


class Base64Encryptor:
    def __init__(self):
        pass

    @staticmethod
    def encode(data):
        """
        对字符串进行Base64编码

        参数:
        data (str): 要编码的字符串

        返回:
        str: 编码后的Base64字符串
        """
        # 将字符串转换为字节
        byte_data = data.encode('utf-8')
        # 执行Base64编码
        base64_bytes = base64.b64encode(byte_data)
        # 将编码后的字节转换回字符串
        base64_string = base64_bytes.decode('utf-8')
        return base64_string

    @staticmethod
    def decode(base64_data):
        """
        对Base64字符串解码

        参数:
        base64_data (str): 要解码的Base64字符串

        返回:
        str: 解码后的字符串
        """
        # 将Base64字符串转换为字节
        base64_bytes = base64_data.encode('utf-8')
        # 执行Base64解码
        byte_data = base64.b64decode(base64_bytes)
        # 将解码后的字节转换回字符串
        data = byte_data.decode('utf-8')
        return data
