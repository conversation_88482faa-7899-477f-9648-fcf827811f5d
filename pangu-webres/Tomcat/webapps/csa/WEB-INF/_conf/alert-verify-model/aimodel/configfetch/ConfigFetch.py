# -*- coding:utf-8 -*-
import codecs
import yaml
import json
from aimodel.configfetch.ModelConfig import ModelConfig
from aimodel.exceptions.ParamException import ParamException
from aimodel.utils.ParamCheck import file_path_valid


def fetch_model_config(filepath):
    if not file_path_valid(filepath):
        raise FileNotFoundError(f'程序相关配置文件不存在，文件路径：{filepath}')

    func_map = {
        'log_level': 'model_config_obj.log_level',
        'model_store_dir': 'model_config_obj.model_store_dir',
        'pangu_api_address': 'model_config_obj.pangu_api_address',
        'api_username': 'model_config_obj.api_username',
        'api_secret': 'model_config_obj.api_secret',
        'log_path': 'model_config_obj.log_path',
        'mysql_database': 'model_config_obj.mysql_database',
        'mysql_host': 'model_config_obj.mysql_host',
        'mysql_port': 'model_config_obj.mysql_port',
        'mysql_user': 'model_config_obj.mysql_user',
        'mysql_password': 'model_config_obj.mysql_password'
    }
    model_config_obj = ModelConfig()
    with codecs.open(filepath, 'r', encoding='utf-8') as f:
        content = yaml.safe_load(f)
        if 'configs' not in content:
            raise ParamException(f'模型配置文件格式错误，必须包含模块：configs，文件路径：{filepath}')
        for item in content['configs']:
            name = item['name']
            value = item['value']
            if name not in func_map:
                raise ParamException(f'模型配置文件格式错误，无效参数：{name}，文件路径：{filepath}')
            exec(f'{func_map[name]} = \'{value}\'')
    return model_config_obj


def fetch_task_config_json(json_file_path):
    """
    :param json_file_path:
    :return: dict类型的参数
    """
    if not file_path_valid(json_file_path):
        raise FileNotFoundError(f'程序入口传入的json文件不存在，文件路径：{json_file_path}')

    with codecs.open(json_file_path, 'r', encoding='utf-8') as f:
        result = json.load(f)
        return result
