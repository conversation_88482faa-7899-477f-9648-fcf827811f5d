# -*- coding:utf-8 -*-
class ModelConfig(object):

    def __init__(
            self, log_level=None, model_store_dir=None, pangu_api_address=None, api_username=None,
            api_secret=None, log_path=None
    ):
        self._log_level = log_level
        self._model_store_dir = model_store_dir
        self._pangu_api_address = pangu_api_address
        self._api_username = api_username
        self._api_secret = api_secret
        self._log_path = log_path

    @property
    def log_level(self):
        return self._log_level

    @property
    def model_store_dir(self):
        return self._model_store_dir

    @property
    def pangu_api_address(self):
        return self._pangu_api_address

    @property
    def api_username(self):
        return self._api_username

    @property
    def api_secret(self):
        return self._api_secret

    @property
    def log_path(self):
        return self._log_path

    @log_level.setter
    def log_level(self, log_level):
        if not log_level:
            raise ValueError('log_level不能为空')
        log_level = log_level.lower()
        if log_level not in {'debug', 'info', 'error', 'warning'}:
            raise ValueError(f'log_level的取值只能是[debug, info, error, warning], 然而log_level={log_level}')
        self._log_level = log_level

    @model_store_dir.setter
    def model_store_dir(self, model_store_dir):
        self._model_store_dir = model_store_dir

    @pangu_api_address.setter
    def pangu_api_address(self, pangu_api_address):
        self._pangu_api_address = pangu_api_address

    @api_username.setter
    def api_username(self, api_username):
        self._api_username = api_username

    @api_secret.setter
    def api_secret(self, api_secret):
        self._api_secret = api_secret

    @log_path.setter
    def log_path(self, log_path):
        self._log_path = log_path

    def __str__(self):
        return f'log_level: {self.log_level}, model_store_dir: {self.model_store_dir}'
