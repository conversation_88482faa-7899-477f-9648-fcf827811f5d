# -*- coding:utf-8 -*-
# import tldextract
import numpy as np
import Levenshtein
from functools import lru_cache
from Levenshtein import distance
from aimodel.models.BaseValidator import BaseValidator


@lru_cache(maxsize=10000)
def similarity(text1: str, text2: str, round_n=3) -> float:
    return np.round(Levenshtein.ratio(text1, text2), round_n)


class Mimic(BaseValidator):

    def __init__(self, mysql_handler):
        super().__init__()

        self._mysql_handler = mysql_handler
        self.target_list = self.get_domain_data()
        self.target_array = np.array(self.target_list)

    def fit(self, *args):
        pass

    def get_domain_data(self):
        sql = """
        select content 
        from sys_resource sr 
        where typename = '自定义资源' and name = '常见业务域名'
        """

        domain_tuple = self._mysql_handler.select_all(sql)
        domain_list = domain_tuple[0][0].split(';')
        self._logger.get_log().info(f"提取平台中内置的常见业务域名:{domain_list}")

        if len(domain_list) == 0:
            self._logger.get_log().info("平台中内置的常见业务域名为空")
            return []

        return domain_list

    def predict(self, data):
        result = []

        for domain_name in data:

            self._logger.get_log().info(f"计算{domain_name}和系统内置常见业务域名相近似度")
            # domain = tldextract.extract(domain_name).domain
            # 计算最大相似读和最相似的业务域名
            max_score, similar_text = self.vectorized_compute_score(domain_name)

            if max_score == 1.0:
                self._logger.get_log().info(f"{domain_name}是正常域名")
                max_score = 0

            elif max_score > 0.7:
                self._logger.get_log().info(f"{domain_name}是{similar_text}的仿冒域名")

            else:
                self._logger.get_log().info(f"{domain_name}和{similar_text}最相近，相似度为{max_score}较低。")

            score = int(((max_score - 0.7) / 0.3) * 10)

            if score > 0:
                result.append({"name": domain_name, "score": score})

        return result

    def vectorized_compute_score(self, text):
        distances = self.vectorized_levenshtein(text, self.target_array)

        max_len = max(len(text), np.max(np.frompyfunc(len, 1, 1)(self.target_array)))
        similarities = 1 - distances / max_len

        max_index = np.argmax(similarities)
        max_score = similarities[max_index]
        similar_text = self.target_array[max_index]

        return max_score, similar_text

    @staticmethod
    def vectorized_levenshtein(s1, s2):
        return np.frompyfunc(lambda x: distance(s1, x), 1, 1)(s2).astype(np.float64)
