"""对html文件内容进行检测。
"""
import jieba
import numpy as np
import pandas as pd
from nltk.util import bigrams, trigrams
from nltk.lm import KneserNeyInterpolated
from nltk.lm.preprocessing import padded_everygram_pipeline


class JiebaSegNGram:

    def __init__(self, contamination, n):
        self.contamination = contamination
        self.n = n

        self.detector_ = None
        self.decision_scores_ = None
        self.threshold = None

    def fit(self, X):
        token_list = self.tokenization(X)
        train_data, padded_tokens = padded_everygram_pipeline(self.n, token_list)

        self.detector_ = KneserNeyInterpolated(self.n)
        self.detector_.fit(train_data, padded_tokens)
        self.decision_scores_ = self.decision_function(token_list)
        self.threshold = np.percentile(self.decision_scores_, self.contamination * 100)
        return self

    def tokenization(self, X):
        token_list = []
        lines = X.values.tolist()

        for line in lines:
            tokens = self.text_to_word_sequence(input_text=line)

            test_puc = ' ~`!@#$%^&*()-_+={}[]|\\:;\'",.<>?/，。《》？：；“‘、\n'
            table = str.maketrans('', '', test_puc)
            tokens = [w.translate(table) for w in tokens]

            tokens = [w.lower() for w in tokens if w]

            token_list.append(tokens)

        return token_list

    def predict(self, X):
        if isinstance(X, str):
            X = pd.Series([X])

        token_list = self.tokenization(X)
        decision_scores = self.decision_function(token_list)  # 分数越小越异常
        if decision_scores < self.threshold:
            return 1
        else:
            return 0

    def decision_function(self, token_list):
        prob_list = []
        if self.n == 2:
            for l in token_list:
                if len(l) == 1:
                    prob_list.append(self.detector_.score(l[0]))
                else:
                    prob_list.append(self.__get_prob_2(self.__get_bigrams(l)))
        elif self.n == 3:
            for l in token_list:
                if len(l) == 1:
                    prob_list.append(self.detector_.score(l[0]))
                elif len(l) == 2:
                    prob_list.append(self.__get_prob_2(self.__get_bigrams(l)))
                else:
                    prob_list.append(self.__get_prob_3(self.__get_trigrams(l)))

        return np.array(prob_list)

    @staticmethod
    def __get_bigrams(token):
        """
        description:
            获取词二元组
        """
        return list(bigrams(token))

    @staticmethod
    def __get_trigrams(token):
        """
        description:
            获取词三元组
        """
        return list(trigrams(token))

    def __get_prob_2(self, list_bigrams):
        """
        description:
            二元组概率累乘
        """
        p = 0
        for iter1, iter2 in list_bigrams:
            p += np.log(self.detector_.score(iter2, [iter1]))
        return p

    def __get_prob_3(self, list_trigrams):
        """
        description:
            三元组概率累乘
        """
        p = 0
        for iter1, iter2, iter3 in list_trigrams:
            p += np.log(self.detector_.score(iter3, [iter1, iter2]))
        return p

    @staticmethod
    def text_to_word_sequence(input_text):
        """jieba.cut cut_all参数用来控制是否采用全模式；HMM参数用来控制是否使用HMM模型；use_paddle参数用来控制是否使用paddle模式下的
            分词模式
        Parameters
        ----------
        input_text : str
            需要分词的字符串
        Returns
        -------
            可迭代的generator，可以使用for循环来获得分词得到的每一个词语（unicode）
        """
        return jieba.cut(input_text, cut_all=False)


class HTMLBaseline:

    def __init__(self, field, key):
        """
        用于对参数的参数值进行拟合基线
        Parameters
        ----------
        field : str 参数来源的字段
        key : str 参数
        """
        self.field = field
        self.key = key
        self.key_type = None
        self.model = None

    def fit(self, data):
        """
        对html文件中抽取的内容进行拟合基线
        Parameters
        ----------
        data : pd.Series 参数值

        Returns
        -------
        self : 拟合好的实例
        """
        self.key_type = "string"
        if len(data) > 20:
            self.model = JiebaSegNGram(contamination=0.001, n=2).fit(data)
        return self

    def predict(self, data):
        if self.model is None:
            # 该基线模型没有拟合
            return 0
        else:
            s = self.model.predict(data)
            return s
