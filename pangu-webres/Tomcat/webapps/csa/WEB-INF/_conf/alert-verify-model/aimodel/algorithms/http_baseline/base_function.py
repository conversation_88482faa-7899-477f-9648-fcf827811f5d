import re
import json
import numpy as np
from . import parse_trunc_json
from bs4 import BeautifulSoup, element


def parse_k_v(x):
    """用于解析字符串key=value&...格式"""
    try:
        if "=" not in x:
            return {}

        # 大概率为xml或者html
        if ("xml" in x.lower()) or ("html" in x.lower()):
            return {}

        # 多个k_v使用&分割
        pair_strs = x.split('&')

        pairs_dict = {}
        for item in pair_strs:
            d = item.split("=")
            if len(d) != 2:
                continue

            if re.match('^[a-zA-Z0-9_]+$', d[0]):  # 参数值大概率由字母数字下划线组成
                pairs_dict.update({d[0]: d[1]})

        return pairs_dict

    except (TypeError, ValueError, AttributeError) as e:
        return {}


def parse_json(text):
    """json字符串可能被截取"""
    if not isinstance(text, str) or ("{" not in text):  # 目前只解析具有k_v结构的json
        return {}

    try:
        data = json.loads(text)
    except (TypeError, ValueError, AttributeError):
        try:
            data = parse_trunc_json.loads(text)
        except parse_trunc_json.decoder.JSONDecodeError as e:
            return {}

    if isinstance(data, dict):
        return data
    elif isinstance(data, list):  # 数组文件抽取第一个数据进行检测
        d = data[0]
        if isinstance(d, dict):
            return d
        else:
            return {}
    else:
        return {}


def traversal_xml(data):
    r_d = {}
    n = data.name

    for i in data.children:
        r = ""
        if isinstance(i, element.Tag):
            r = traversal_xml(i)
        elif isinstance(i, element.NavigableString):
            r = i.text
            r = r.strip()
        if r:
            if isinstance(r_d.get(n), dict):
                r_d[n].update(r)
            else:
                r_d[n] = r
    return r_d


def parse_xml(text):
    try:
        if "xml" not in str(text).lower():
            return {}
        data = BeautifulSoup(text, "xml")
        if data.is_xml:
            r = {}
            for i in data.children:
                if isinstance(i, element.Tag):
                    r = traversal_xml(i)
                elif isinstance(i, element.NavigableString):
                    if i.name is not None:
                        r[i.name] = i.text
                else:
                    return {}
            return r
        else:
            return {}
    except (TypeError, ValueError) as e:
        return {}


def parse_html(text):
    """
    html格式字符串解析出内容，不是html则为空
    Parameters
    ----------
    text : str 需要解析的字符串

    Returns
    -------
    str 内容
    """
    try:
        if "html" not in str(text).lower():
            return ""
        data = BeautifulSoup(text, "html.parser")  # 使用html.parser修复HTML
        text = data.get_text(separator=' ')
        text = text.strip()
        return text

    except (TypeError, ValueError) as e:
        return ""


def modify_sigmoid(x, alpha=0.25):
    y = 2 * (-1 * (1 / (1 + np.exp(-alpha * x))) + 1)
    return y
