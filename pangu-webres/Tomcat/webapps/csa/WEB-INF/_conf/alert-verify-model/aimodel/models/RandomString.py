# -*- coding:utf-8 -*-
import os
import joblib
import pandas as pd
from aimodel.models.BaseValidator import BaseValidator
from aimodel.algorithms.randomstring.dga_model import DGAModel, get_domain_name


def get_model(base_path_ep, path_encryptor, base64_encryptor):
    # 模型存储的位置
    model_filepath = os.path.join(
        path_encryptor.decrypt(base_path_ep), base64_encryptor.decode("YWxnb3JpdGhtcw=="),
        base64_encryptor.decode("cmFuZG9tc3RyaW5n"), base64_encryptor.decode("bW9kZWwuam9ibGli")
    )
    if not os.path.exists(model_filepath):
        model = DGAModel().fit()
        joblib.dump(model, model_filepath)

    else:
        model = joblib.load(model_filepath)

    return model


class RandomString(BaseValidator):

    def __init__(self):
        super().__init__()
        self._model = get_model(self.base_path_ep, self.path_encryptor, self.base64_encryptor)
        self._logger.get_log().info("已读取训练好的模型")

    def fit(self):
        pass

    def predict(self, text):
        test_data = pd.DataFrame(columns=['qry_name'], data=text)

        test_data["domain"] = test_data["qry_name"].apply(lambda domain: get_domain_name(domain))
        labels = self._model.predict(test_data)

        scores = []
        for label in labels:
            s = label[0]
            score = int(((s - 0.9) / 0.1) * 10)
            if score > 0:
                scores.append(score)
            else:
                scores.append(0)

        result = []
        for i, s in zip(test_data["qry_name"].values.tolist(), scores):
            if s > 0:
                result.append({"name": i, "score": s})

        return result
