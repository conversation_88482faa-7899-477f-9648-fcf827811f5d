"""用于判断参数值类型"""
import datetime


def is_boolean(x):
    """判断输入是否为布尔值"""
    if isinstance(x, str):
        if x.lower() in ["true", "false"]:
            return True
        else:
            return False
    elif isinstance(x, bool):
        return True
    else:
        return False


def is_numeric(x):
    if isinstance(x, str):
        if x.isnumeric():
            return True
        else:
            return False

    elif isinstance(x, int) or isinstance(x, float):
        return True

    else:
        return False


def is_string(x):
    if isinstance(x, str):
        return True
    else:
        return False


def is_enum(value_counts, n_sample):
    """
    参数值出现的数值较少
    Parameters
    ----------
    value_counts : 词频
    n_sample : 总计

    Returns
    -------
    True or False
    """
    n_value = value_counts.shape[0]

    if (n_value == 1 and n_sample >= 3) or (n_value <= 3 and n_sample >= 8) or \
            (n_value <= 5 and n_sample >= 13) or (n_value <= 10 and n_sample >= 60):
        return True
    else:
        return False


def is_time(x):
    """
    需要判断待检测字符串是否是日期格式数据。
    Parameters
    ----------
    x : str 需要校验的字符串

    Returns
    -------
    True or False
    """
    pat = "%a, %d %b %Y %H:%M:%S GMT"
    if isinstance(x, str):
        try:
            datetime.datetime.strptime(x, pat)
            return True
        except ValueError:
            return False
    # TODO: 需要添加更多认证
    else:
        return False
