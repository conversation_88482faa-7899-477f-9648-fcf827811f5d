import os
import base64
import numpy as np
from aimodel.models.BaseValidator import BaseValidator
from sentence_transformers import SentenceTransformer, util


class DataLeakageValidator(BaseValidator):

    def __init__(self):
        super().__init__()
        self.data_path = None
        self.model = None
        self.embedding_path = None

        self.path = os.path.abspath(__file__)

        p1 = self.path_encryptor.decrypt(self.base_path_ep)
        p2 = os.path.join('data', 'vector_lib')

        # 预训练大模型的位置
        self._logger.get_log().info("读取预训练模型。")
        self.LM_model_path = os.path.join(p1, 'model-lib', 'all-MiniLM-L6-v2')
        self.model = SentenceTransformer(self.LM_model_path)

        self._logger.get_log().info("读取embedding向量。")
        # 操作系统文件embedding向量库
        self.embedding_path_OSConfig = os.path.join(p1, p2, 'OSConfig.npy')
        # 数据库配置文件embedding向量库
        self.embedding_path_DBConfig = os.path.join(p1, p2, 'DBConfig.npy')
        # 中间件配置文件embedding向量库
        self.embedding_path_MiddlewareConfig = os.path.join(p1, p2, 'MiddlewareConfig.npy')
        # 系统命令执行返回
        self.embedding_path_SystemCommandExec = os.path.join(p1, p2, 'system_cmd_exec_embeddings.npy')

    def fit(self, *args):
        pass

    def predict(self, data):

        corpus_embeddings_middleware = np.load(self.embedding_path_MiddlewareConfig)
        corpus_embeddings_db = np.load(self.embedding_path_DBConfig)
        corpus_embeddings_os = np.load(self.embedding_path_OSConfig)

        corpus_embeddings = {
            "os": corpus_embeddings_os,
            "db": corpus_embeddings_db,
            "middleware": corpus_embeddings_middleware
        }

        result = []

        for sample in data:
            r = {}

            request_msg = sample.get("extInfo", None)
            if request_msg is None:
                continue

            s = 0  # 此id对应的extInfo最大异常分数

            for r_m in request_msg:
                response_body = r_m.get("responseBody", None)

                if response_body is None:
                    continue

                response_body = base64.b64decode(response_body).decode('utf-8', errors='ignore')
                queries_embeddings = self.model.encode(response_body)

                for k in corpus_embeddings:
                    e = corpus_embeddings[k]
                    hits = util.semantic_search(queries_embeddings, e, top_k=1)
                    similarity = hits[0][0]["score"]

                    score = int(((similarity - 0.7) / 0.3) * 10)  # 相似度 -> 异常分数
                    if score < 0:
                        score = 0
                    else:
                        self._logger.get_log().info(f"响应体为{k}文件：{response_body}")

                    r[k] = score
                    # 更新此id对应的extInfo最大异常分数
                    s = s if s > score else score

            if s > 0:
                r["id"] = sample["id"]
                result.append(r)

        return result

    def predict_(self, data):
        result = dict()

        corpus_embeddings_middleware = np.load(self.embedding_path_MiddlewareConfig)
        corpus_embeddings_db = np.load(self.embedding_path_DBConfig)
        corpus_embeddings_os = np.load(self.embedding_path_OSConfig)
        corpus_embeddings_system_com = np.load(self.embedding_path_SystemCommandExec)

        corpus_embeddings = {
            "SystemCommandExec": corpus_embeddings_system_com,
            "OSConfigurationFile": corpus_embeddings_os,
            "DBConfigurationFile": corpus_embeddings_db,
            "MiddlewareConfigurationFile": corpus_embeddings_middleware
        }

        for sample in data["extInfo"]:
            responseBody = sample.get("responseBody", None)
            queries_embeddings = self.model.encode(responseBody)

            for k in corpus_embeddings:
                e = corpus_embeddings[k]
                hits = util.semantic_search(queries_embeddings, e, top_k=1)
                similarity = hits[0][0]["score"]

                score = int(((similarity - 0.7) / 0.3) * 10)  # 相似度 -> 异常分数
                if score > 0:
                    self._logger.get_log().info(f"响应体为{k}文件：{responseBody}")
                    if k in result:
                        result[k] = result[k] + 1
                    else:
                        result[k] = 1

        return result
