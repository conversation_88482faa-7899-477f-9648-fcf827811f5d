# -*- coding:utf-8 -*-
import pymysql as pymysql
from aimodel.utils.DecryptUtil import decrypt_message
from aimodel.utils.Logger import Logger
import sys


class MySqlHandler(object):

    def __init__(self, database, mysql_host, mysql_port, mysql_username, mysql_password):
        self._logger = Logger(__name__)
        try:

            mysql_info_dict = {
                "host": mysql_host,
                "port": int(mysql_port),
                "user": mysql_username,
                "password": decrypt_message(mysql_password),
                "database": database,
                "charset": "utf8"
            }

            self._mysql_conn = pymysql.connect(**mysql_info_dict)

            mysql_host = mysql_info_dict.get("host", '')
            mysql_port = mysql_info_dict.get("port", 0)

            self._logger.get_log().info(f'mysql:{mysql_host}:{mysql_port} 已连接')

        except Exception as e:
            self._logger.get_log().error('MySQL 连接失败')
            self._logger.get_log().error(e)
            sys.exit(-1)

    def get_mysql_conn(self):
        return self._mysql_conn

    def replace_many(self, replace_sql, params):
        cursor = self._mysql_conn.cursor()
        try:
            cursor.executemany(replace_sql, params)
            self._mysql_conn.commit()
        except Exception as e:
            message = "Execute replace sql failed: %s" % str(e)
            # This logs to the docker logs
            self._logger.get_log().error(message)
            return message
        finally:
            cursor.close()

    def select_all(self, sql):
        cursor = self._mysql_conn.cursor()
        try:
            cursor.execute(sql)
            self._mysql_conn.commit()
            return cursor.fetchall()
        except Exception as e:
            message = "Execute simple get sql failed: %s" % str(e)
            self._logger.get_log().error(message)
            return message
        finally:
            cursor.close()
