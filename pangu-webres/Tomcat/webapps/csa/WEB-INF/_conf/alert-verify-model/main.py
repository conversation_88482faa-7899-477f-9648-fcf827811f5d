import os
import site
import warnings
warnings.filterwarnings("ignore")
BASE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "dependencies")
site.addsitedir(BASE_DIR)

import argparse
import traceback
from aimodel.utils.Logger import Logger
from aimodel.constants import GlobalConsts, ModelConsts
from aimodel.exceptions.ParamException import ParamException
from aimodel.core.VerifyCore import VerifyCore
from aimodel.configfetch.ConfigFetch import fetch_model_config, fetch_task_config_json


# yaml
model_config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
model_config = fetch_model_config(model_config_file)


GlobalConsts.set_value('log_level',  model_config.log_level)
GlobalConsts.set_value('log_path', model_config.log_path)
logger = Logger(__name__)


def main():
    try:
        logger.get_log().info('Start Alert Verify Engine')

        parser = argparse.ArgumentParser(description='Run Alert-Verify')
        parser.add_argument('--model_name', required=True, help='model to be used')
        parser.add_argument('--input_json', required=True, help='text to be verified')
        args = parser.parse_args()

        model_name = args.model_name
        input_json = args.input_json  # 输入的json文件名为这批检测的id
        data = fetch_task_config_json(os.path.join(os.path.dirname(os.path.abspath(__file__)), input_json))

        if model_name not in ModelConsts.MODEL_LIST:
            raise ParamException(f'暂无告警验证模型：{model_name}')

        verify_core = VerifyCore(model_name, input_json, data, model_config)
        result = verify_core.run()

        os.remove(input_json)
        print(result)

    except Exception as e:
        message = "错误信息" + str(e)
        logger.get_log().error(message)
        logger.get_log().error(traceback.format_exc())


if __name__ == "__main__":
    main()
