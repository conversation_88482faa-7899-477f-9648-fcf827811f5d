<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--
        categroy 目录配置
          - id 与beanId定义相同（*使用category-开头）
          - name 目录名称，请使用国际化方式配置对应国际化文件
     -->

    <!-- 可视中心 -->
    <bean id="category-vis" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-vis"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.vis')}"/>
        <property name="order">
            <value>10</value>
        </property>
    </bean>

    <!-- 资产中心 -->
    <bean id="category-asset" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-asset"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.asset')}"/>
        <property name="order">
            <value>20</value>
        </property>
    </bean>

    <!-- 威胁中心 -->
    <bean id="category-threaten" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-threaten"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.threaten')}"/>
        <property name="order">
            <value>30</value>
        </property>
    </bean>

    <!-- 数据中心 -->
    <bean id="category-data" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-data"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.data')}"/>
        <property name="order">
            <value>40</value>
        </property>
    </bean>

    <!-- 情报中心 -->
    <bean id="category-intelligence" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-intelligence"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.intelligence')}"/>
        <property name="order">
            <value>50</value>
        </property>
    </bean>

    <!-- 脆弱性中心 -->
    <bean id="category-weakness" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-weakness"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.weakness')}"/>
        <property name="order">
            <value>60</value>
        </property>
    </bean>

    <!-- 运行中心 -->
    <bean id="category-operation" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-operation"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.operation')}"/>
        <property name="order">
            <value>70</value>
        </property>
    </bean>

    <!-- 模型中心 -->
    <bean id="category-model" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-model"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.model')}"/>
        <property name="order">
            <value>80</value>
        </property>
    </bean>

    <!-- 响应中心 -->
    <bean id="category-response" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-response"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.response')}"/>
        <property name="order">
            <value>90</value>
        </property>
    </bean>

    <!-- 监管中心 -->
    <bean id="category-supervise" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-supervise"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.supervise')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 报告中心 -->
    <bean id="category-report" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-report"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.report')}"/>
        <property name="order">
            <value>110</value>
        </property>
    </bean>

    <!-- 配置中心 -->
    <bean id="category-system" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-system"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.system')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>

    <!-- 其他 -->
    <bean id="category-other" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-other"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.other')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 态势中心 -->
    <bean id="category-cloudcas" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-cloudcas"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.cloudcas')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 云租户门户 -->
    <bean id="category-cloudportal" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-cloudportal"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.cloudportal')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 云运营门户 -->
    <bean id="category-cloudoperation" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-cloudoperation"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.cloudoperation')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 态势中心 -->
    <bean id="category-cloudvis" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-cloudvis"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.vis')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 云网安全-->
    <bean id="category-securityCloud" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-securityCloud"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.cloudSecurity')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 运维安全-->
    <bean id="category-securityOperation" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-securityOperation"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.securityOperation')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 云安全态势-->
    <bean id="category-cloudPosture" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-cloudPosture"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.cloudposture')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 终端安全-->
    <bean id="category-securityTerminal" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-securityTerminal"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.securityterminal')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 展示中心-->
    <bean id="category-exhibitionCentre" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-exhibitionCentre"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.exhibitionCentre')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 安全编排 -->
    <bean id="category-securityArrange" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-securityArrange"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.securityArrange')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 安全管理 -->
    <bean id="category-securityManager" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-securityManager"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.securityManager')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!--行为感知中心-->
    <bean id="category-behaviorPerception" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-behaviorPerception"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.behaviorPerception')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!--分析中心-->
    <bean id="category-analysis" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-analysis"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.analysis')}"/>
        <property name="order">
            <value>40</value>
        </property>
    </bean>

    <!--用户实体管理-->
    <bean id="category-userEntityManagement" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-userEntityManagement"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.userEntityManagement')}"/>
        <property name="order">
            <value>100</value>
        </property>
    </bean>

    <!-- 数据治理中心 -->
    <bean id="category-governance" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-governance"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.governance')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>

    <!-- 授权平台中心 -->
    <bean id="category-licenceOnline" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-licenceOnline"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.licenceOnline')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>

    <!-- 知识中心 -->
    <bean id="category-knowledge" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-knowledge"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.knowledge')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>
    <!-- 需求中心 -->
    <bean id="category-requirement" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-requirement"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.requirement')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>
    <!-- GPT相关中心定义 -->
    <!-- GPT可视化中心 -->
    <bean id="category-gpt-visualization" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-gpt-visualization"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.gpt.visualization')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>
    <!-- GPT分析中心-->
    <bean id="category-gpt-analysis" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-gpt-analysis"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.gpt.analysis')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>
    <!-- GPT知识中心 -->
    <bean id="category-gpt-knowledge" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-gpt-knowledge"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.gpt.knowledge')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>
    <!-- GPT盘小古智能应用 -->
    <bean id="category-gpt-intelligent" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-gpt-intelligent"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.gpt.intelligent')}"/>
        <property name="order">
            <value>120</value>
        </property>
    </bean>
    <!-- GPT模型中心 -->
    <bean id="category-gpt-model" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-gpt-model"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.gpt.model')}"/>
        <property name="order">
            <value>130</value>
        </property>
    </bean>
    <!-- GPT配置中心 -->
    <bean id="category-gpt-config" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="category-gpt-config"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('category', 'category.gpt.config')}"/>
        <property name="order">
            <value>130</value>
        </property>
    </bean>

    <!-- 内部开发者平台-项目管理中心 -->
    <bean id="idp-project" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="idp-project"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('idp', 'idp.project')}"/>
        <property name="order">
            <value>50</value>
        </property>
    </bean>

    <!-- 内部开发者平台-CICD中心 -->
    <bean id="idp-cicd" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="idp-cicd"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('idp', 'idp.cicd')}"/>
        <property name="order">
            <value>50</value>
        </property>
    </bean>

    <!-- 内部开发者平台-审批中心 -->
    <bean id="idp-task" class="com.venustech.taihe.pangu.foundation.definition.Category">
        <property name="id" value="idp-task"/><!-- 与bean id保持一致 -->
        <property name="name" value="#{$i18n.message('idp', 'idp.task')}"/>
        <property name="order">
            <value>50</value>
        </property>
    </bean>
</beans>
