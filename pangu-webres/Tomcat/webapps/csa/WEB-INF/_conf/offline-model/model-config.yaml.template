configs:
  - name: log_level
    description: 打印的日志级别(debug, info, error, warning)
    value: info
  - name: time_offset
    description: 观测时间偏移量(单位：分钟)，以当前时间减去time_offset作为获取数据的时间点
    value: 3
  - name: pangu_api_address
    description: pangu restful api address
    value: ${pangu.rest.url}
  - name: api_username
    description: api用户名
    value: ${pangu.rest.username}
  - name: api_secret
    description: 密钥
    value: ${pangu.rest.apisecret}
  - name: anomaly_beh_topic
    description: 发送ueba异常行为的topic参数
    value: logs-ueba-anomaly
  - name: log_path
    description: 日志路径
    value: ${pangu.log.dir}/model-engine/offline-model
  - name: mysql_database
    description: mysql数据库
    value: ${pangu.mysql.database}
  - name: mysql_host
    description: mysql host
    value: ${pangu.mysql.host}
  - name: mysql_port
    description: mysql port
    value: ${pangu.mysql.port}
  - name: mysql_user
    description: mysql user
    value: ${pangu.mysql.username}
  - name: mysql_password
    description: mysql password
    value: ${pangu.mysql.password}
  - name: kafka_host
    description: kafka host
    value: ${pangu.kafka.server.url}
  - name: kafka_mechanism
    description: sasl kafka mechanism
    value: ${pangu.kafka.server.saslMechanism}
  - name: kafka_username
    description: sasl kafka username
    value: ${pangu.kafka.server.username}
  - name: kafka_password
    description: sasl kafka password
    value: ${pangu.kafka.server.password}
