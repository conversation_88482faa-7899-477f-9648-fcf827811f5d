#!/bin/bash

SOURCE="$0"
SOURCE_DIR="$( dirname "$SOURCE" )"
SOURCE_ABSOLUTE_DIR="$( cd -P "$SOURCE_DIR" >/dev/null 2>&1 && pwd )"

SHELL_HOME="model-offline"

architecture=$(syscmd --arch)

start() {
   cd ${SOURCE_ABSOLUTE_DIR}
   project_install_dir="$( cd -P "../../../../../../" >/dev/null 2>&1 && pwd )"
   if [ "$architecture" = "x86_64" ]; then
      chmod u+x model-offline-listener-server
      nohup ./model-offline-listener-server -path ${project_install_dir} > /dev/null 2>&1 &
     elif [ "$architecture" = "aarch64" ]; then
       chmod u+x model-offline-listener-server-arm64
       nohup ./model-offline-listener-server-arm64 -path ${project_install_dir} > /dev/null 2>&1 &
     else
       echo "请定制当前系统架构的model-offline-listener-server-app：$architecture"
   fi
}

install_wheel() {
   cd ${SOURCE_ABSOLUTE_DIR}
   chmod u+x install_wheels.sh
   ./install_wheels.sh
   echo "安装${SHELL_HOME}依赖成功"
}

stop() {
   pid=`ps -ef | grep "$SHELL_HOME" |grep -v "grep" |awk '{print $2}'`
   kill -9 $pid
}

case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  *)
    echo "param support start/stop"
    exit 1
    ;;
esac
exit $?
