HW_ARCH=$(syscmd --arch)
CURRENT_DIR=$(pwd)
WHEELS_DIR=${CURRENT_DIR}/wheels/${HW_ARCH}
DEPENDENCIES=${CURRENT_DIR}/dependencies
mkdir -p ${DEPENDENCIES}

INSTALL_DIR=$(syscmd --path | awk '{ print $4 }')
PYTHON_HOME=${INSTALL_DIR}/py-standalone
PYTHON_EXEC=${PYTHON_HOME}/bin/python3

sync
$PYTHON_EXEC -m pip install ${WHEELS_DIR}/*.whl --target=${DEPENDENCIES} --upgrade --force-reinstall
sync

cd ${CURRENT_DIR}
chmod -R 755 ${DEPENDENCIES}
