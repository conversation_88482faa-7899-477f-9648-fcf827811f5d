# -*- coding:utf-8 -*-
# 模型必备的参数
MODEL_ID = 'model_id'
ALGORITHM = 'algorithm'
PARAMS = 'params'
# STAT_TIME = 'stat_time'
# INDICATOR_MV_ID = 'indicator_mv_id'
# GROUP_BY = 'group_by'
# HISTORY_DAY = 'history_day'
# WINDOW_TIME = 'window_time'
# TRAIN = 'train'
# SINK = 'sink'

# 模型参数检查时必备的模型参数
MODEL_ARGS = {ALGORITHM, PARAMS}

# 离线模型包含算法
MODEL_ALL = {
    'firstAppearance', 'mimic', 'sensitiveInformation', 'randomString', 'content', 'outlier',
    'accumulation', 'beacon', 'recommendation', 'IPTag', 'SSLSuspiciousDetection',
    'HTTPSuspiciousDetection', 'SessionBaselineModel', 'OperationalBaseline', 'WebAppSuspiciousDetection', 'others'
}


class ModelMap:

    @classmethod
    def get(cls, algorithm):
        if algorithm == 'firstAppearance':
            from olmodel.models.FirstAppearance import FirstAppearance
            return FirstAppearance
        elif algorithm == 'mimic':
            from olmodel.models.Mimic import Mimic
            return Mimic
        elif algorithm == 'sensitiveInformation':
            from olmodel.models.SensitiveInformation import SensitiveInformation
            return SensitiveInformation
        elif algorithm == 'randomString':
            from olmodel.models.RandomString import RandomString
            return RandomString
        elif algorithm == 'content':
            from olmodel.models.Content import Content
            return Content
        elif algorithm == 'outlier':
            from olmodel.models.Outlier import Outlier
            return Outlier
        elif algorithm == 'accumulation':
            from olmodel.models.Accumulation import Accumulation
            return Accumulation
        elif algorithm == 'beacon':
            from olmodel.models.Beacon import Beacon
            return Beacon
        elif algorithm == 'recommendation':
            from olmodel.models.Recommendation import Recommendation
            return Recommendation
        elif algorithm == 'IPTag':
            from olmodel.models.IPTag import IPTagModel
            return IPTagModel
        elif algorithm == 'SSLSuspiciousDetection':
            from olmodel.models.SSLSuspiciousDetection import SSLSuspiciousDetection
            return SSLSuspiciousDetection
        elif algorithm == 'HTTPSuspiciousDetection':
            from olmodel.models.HTTPSuspicousDetection import HTTPSuspiciousDetection
            return HTTPSuspiciousDetection
        elif algorithm == 'SessionBaselineModel':
            from olmodel.models.HTTPBaselineModel import HTTPBaselineModel
            return HTTPBaselineModel
        elif algorithm == 'OperationalBaseline':
            from olmodel.models.OperationalBaseline import OperationalBaseline
            return OperationalBaseline
        elif algorithm == 'WebAppSuspiciousDetection':
            from olmodel.models.WebAppSuspiciousDetection import WebAppSuspiciousDetection
            return WebAppSuspiciousDetection
        elif algorithm == 'others':
            from olmodel.models.Others import Others
            return Others
