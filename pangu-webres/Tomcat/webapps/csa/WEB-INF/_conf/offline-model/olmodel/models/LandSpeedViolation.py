# -*- coding:utf-8 -*-
from olmodel.models.BaseModel import BaseModel
import olmodel.utils.TimeUtil as TimeUtil
from haversine import haversine

SPLIT_CHAR = "-"
AIRPLANE_MAXIMUM_SPEED = 4000  # 陆地最快速度，4000km/h


class LandSpeedViolation(BaseModel):

    def __init__(self, ck_handler, model_config, name="违反陆地速度（疑似盗号）", **kwargs):
        super().__init__(ck_handler)
        self._model_config = model_config
        self._name = name

    def get_data(self):
        # 检查过去一小时内，两次登录间隔是否违反陆地速度
        start_time = TimeUtil.get_now_timestamp() - 60 * 60 * 1000
        login_data_df = self.get_login_data(start_time, "asc")

        if login_data_df.empty:
            return []

        acc_dict = dict()
        earliest_acc_dict = dict()

        # 根据key（账号类型+账号）进行分组，并找到此次检测每组中登录时间最早的记录
        for index, row in login_data_df.iterrows():
            cuseraccountname = row['cuseraccountname']
            cuseraccounttype = row['cuseraccounttype']
            if cuseraccountname is None or len(cuseraccountname) == 0 \
                    or cuseraccounttype is None or len(cuseraccounttype) == 0:
                continue

            key = cuseraccountname + SPLIT_CHAR + cuseraccounttype

            if key not in acc_dict:
                earliest_acc_dict[key] = row

                acc_dict[key] = list()

            acc_dict[key].append(row)

        offline_result = []
        for key, acc_list in acc_dict.items():
            pre_acc_info = None
            for acc_info in acc_list:
                if pre_acc_info is None:
                    pre_acc_info = acc_info
                    continue
                else:
                    csrcgpsOne = acc_info['csrcgps']
                    csrcgpsTwo = pre_acc_info['csrcgps']

                    if csrcgpsOne is None or len(csrcgpsOne) == 0 \
                            or csrcgpsTwo is None or len(csrcgpsTwo) == 0:
                        pre_acc_info = acc_info
                        continue

                    loccurtimeOne = acc_info['loccurtime']
                    loccurtimeTwo = pre_acc_info['loccurtime']
                    if loccurtimeOne is None or loccurtimeTwo is None:
                        pre_acc_info = acc_info
                        continue

                    if self.land_speed_violation(csrcgpsOne, csrcgpsTwo, loccurtimeOne, loccurtimeTwo):
                        offline_result.append(acc_info.to_dict())

                pre_acc_info = acc_info

        # 再检查一小时前且24小时内的最后一次登录，与此次检测这一小时最早一次登录的时间间隔，是否违反陆地速度
        end_time = start_time
        start_time = end_time - 24 * 60 * 60 * 1000
        login_data_df = self.get_login_data_latest(start_time, end_time)

        if login_data_df.empty:
            return offline_result

        for index, row in login_data_df.iterrows():
            cuseraccountname = row['cuseraccountname']
            cuseraccounttype = row['cuseraccounttype']
            if cuseraccountname is None or len(cuseraccountname) == 0 \
                    or cuseraccounttype is None or len(cuseraccounttype) == 0:
                continue

            key = cuseraccountname + SPLIT_CHAR + cuseraccounttype

            if key in earliest_acc_dict:
                csrcgpsOne = earliest_acc_dict[key]['csrcgps']
                csrcgpsTwo = row['csrcgps']

                if csrcgpsOne is None or len(csrcgpsOne) == 0 \
                        or csrcgpsTwo is None or len(csrcgpsTwo) == 0:
                    continue

                loccurtimeOne = earliest_acc_dict[key]['loccurtime']
                loccurtimeTwo = row['loccurtime']
                if loccurtimeOne is None or loccurtimeTwo is None:
                    continue

                if self.land_speed_violation(csrcgpsOne, csrcgpsTwo, loccurtimeOne, loccurtimeTwo):
                    offline_result.append(row.to_dict())

        return offline_result

    def run(self):
        return self.get_data()

    def get_login_data(self, start_time, order):

        # set default ts_window if None
        if start_time is None:
            start_time = TimeUtil.get_now_timestamp() - 60 * 60 * 1000

        # set default order if None
        default_order = "desc"
        if order is None:
            order = default_order
        else:
            order = order.strip()
            if order != "desc" and order != "asc":
                order = default_order

        sql = """
            select cuseraccountname, cuseraccounttype, loccurtime, cuserid, csrcgps, csrcip, cdstip, '/login' as coperation, '1007885906163541233' as behaviorid
            from mat_distributed_1007885906163541233
            where loccurtime >= {start_time}
                  and csrcipinner = 2
            order by loccurtime {order}
            """.format(
            start_time=start_time,
            order=order
        )
        self._logger.get_log().info(f'LandSpeedViolation algorithm. Executing sql:\n{sql}')

        df = self._ck_handler.get_data_func(sql)

        if df.empty:
            self._logger.get_log().info(f'LandSpeedViolation algorithm, login data is empty')

        return df

    def get_login_data_latest(self, start_time, end_time):

        # set default time if None
        if end_time is None:
            end_time = TimeUtil.get_now_timestamp()

        if start_time is None:
            start_time = end_time - 60 * 60 * 1000

        sql = """
            SELECT cuseraccountname, cuseraccounttype, loccurtime, cuserid, csrcgps, csrcip, cdstip, '/login' as coperation, '1007885906163541233' as behaviorid
            FROM mat_distributed_1007885906163541233 t, 
            (
                SELECT cuseraccountname, cuseraccounttype, max(loccurtime) as maxtime
                FROM mat_distributed_1007885906163541233 
                where loccurtime >= {start_time} and loccurtime < {end_time} 
                      and csrcipinner = 2
                GROUP BY cuseraccountname, cuseraccounttype
            ) t2 
            where t.loccurtime >= {start_time} and t.loccurtime < {end_time} 
                and t.cuseraccountname = t2.cuseraccountname	
                and t.cuseraccounttype = t2.cuseraccounttype
                and t.loccurtime >= t2.maxtime 
                    """.format(
            start_time=start_time,
            end_time=end_time
        )
        self._logger.get_log().info(f'LandSpeedViolation algorithm. Executing sql:\n{sql}')

        df = self._ck_handler.get_data_func(sql)

        if df.empty:
            self._logger.get_log().info(f'LandSpeedViolation algorithm, login data is empty')

        return df

    def land_speed_violation(self, csrcgpsOne, csrcgpsTwo, loccurtimeOne, loccurtimeTwo):

        split = csrcgpsOne.split(",")
        csrcgpsOneT = (float(split[0]), float(split[1]))
        split = csrcgpsTwo.split(",")
        csrcgpsTwoT = (float(split[0]), float(split[1]))

        dis = haversine(csrcgpsOneT, csrcgpsTwoT)
        minimum_time = (dis / AIRPLANE_MAXIMUM_SPEED) * 60 * 60 * 1000
        if loccurtimeOne - loccurtimeTwo < minimum_time:
            return True

        return False
