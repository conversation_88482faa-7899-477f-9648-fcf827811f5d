# -*- coding:utf-8 -*-
import time


def get_timestamp(datetime_str):
    datetime_str = datetime_str.strip()
    if not datetime_str:
        return None
    time_array = time.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
    time_stamp = int(time.mktime(time_array)) * 1000
    return time_stamp


def get_now_timestamp():
    t = time.time()
    return int(round(t * 1000))


def get_timeday_by_str(datetime_str):
    datetime_str = datetime_str.strip()
    if not datetime_str:
        return None
    if ' ' in datetime_str:
        return datetime_str.split(' ')[0]
    return None


def get_timeday_by_ts(timestamp_ms):
    return time.strftime('%Y-%m-%d', time.localtime(timestamp_ms/1000))


def get_datetime_by_ts(timestamp_ms):
    """
    :param timestamp_ms: 13为时间戳
    :return: 字符串型时间 2020-12-22 15:24:19
    """
    return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp_ms/1000))
