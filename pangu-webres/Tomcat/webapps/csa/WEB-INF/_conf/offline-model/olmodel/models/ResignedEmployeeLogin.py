# -*- coding:utf-8 -*-
from olmodel.models.BaseModel import BaseModel
import olmodel.utils.TimeUtil as TimeUtil


class ResignedEmployeeLogin(BaseModel):

    def __init__(self, ck_handler, mysql_handler, model_config, ts_window=7, name="离职人员登录", **kwargs):
        super().__init__(ck_handler)
        self._mysql_handler = mysql_handler
        self._model_config = model_config
        self._ts_window = ts_window
        self._name = name

    def get_data(self):

        user_account_set = self.get_user_account()

        if len(user_account_set) == 0:
            return []

        login_data_df = self.get_login_data()

        if login_data_df.empty:
            return []

        offline_result = []

        for index, row in login_data_df.iterrows():
            user_id = row['cuserid']
            if user_id in user_account_set:
                offline_result.append(row.to_dict())

        return offline_result

    def run(self):
        return self.get_data()

    def get_login_data(self):

        ts_window = TimeUtil.get_now_timestamp() - 60 * 60 * 1000

        sql = """ 
            select cuseraccountname, cuseraccounttype, loccurtime, cuserid, csrcip, cdstip, '/login' as coperation, '1007885906163541233' as behaviorid
            from mat_distributed_1007885906163541233
            where loccurtime >= {ts_window}
            """.format(
            ts_window=ts_window
        )
        self._logger.get_log().info(f'ResignedEmployeeLogin algorithm. Executing sql:\n{sql}')

        df = self._ck_handler.get_data_func(sql)

        if df.empty:
            self._logger.get_log().info(f'ResignedEmployeeLogin algorithm, login data is empty')

        return df

    def get_user_account(self):

        user_sql = """select user_account 
                      from asap_ueba_user 
                      where resignation_status = 5 or resignation_status = 6"""

        self._logger.get_log().info(f'ResignedEmployeeLogin algorithm. Executing sql:\n{user_sql}')

        user_account_result = self._mysql_handler.select_all(user_sql)

        if len(user_account_result) == 0:
            self._logger.get_log().info(f'ResignedEmployeeLogin algorithm, user account result is empty')
            return set()

        user_account_set = set()

        for value in iter(user_account_result):
            user_account_set.add(value[0])

        return user_account_set
