"""Peaks over Threshold (POT)Algorithm.
Strictly for Univariate Data.
"""
from __future__ import division
from __future__ import print_function

import numpy as np
from sklearn.utils import check_array
from sklearn.utils.validation import check_is_fitted

from math import log
from scipy.optimize import minimize

from .base import BaseDetector


def _check_dim(X):
    """
    Internal function to assert univariate data
    """
    if X.shape[1] != 1:
        raise ValueError('POT algorithm is just for univariate data. '
                         'Got Data with {} Dimensions.'.format(X.shape[1]))


class POT(BaseDetector):
    """
    Peaks over Threshold
    """
    def __init__(self, risk=0.001, init_level=0.8, extreme="maximum", enhancement=True):
        super(POT, self).__init__()
        self.decision_scores_ = None
        self._risk = risk
        self._init_level = init_level
        self._extreme = extreme
        self._enhancement = enhancement
        self.z = None
        self.t = None

    def fit(self, X, y=None):
        X = check_array(X, ensure_2d=False)
        _check_dim(X)
        self._set_n_classes(y)
        self.decision_function(X)
        return self

    def decision_function(self, X):
        X = check_array(X, ensure_2d=False)
        _check_dim(X)
        return self._pot(X)

    def _pot(self, X):

        if self._extreme == "maximum":
            obs = np.reshape(X, (-1, 1))
            if self._enhancement and obs.shape[0] < 30:
                fill_value = np.min(obs)
                enh_row_num = 30 - obs.shape[0]
                enh_data = np.ones((enh_row_num, 1)) * fill_value
                obs = np.vstack((obs, enh_data))
        else:
            obs = - np.reshape(X, (-1, 1))
            if self._enhancement and obs.shape[0] < 30:
                fill_value = np.min(obs)  # 取相反后最小值就是原来的最大值
                enh_row_num = 30 - obs.shape[0]
                enh_data = np.ones((enh_row_num, 1)) * fill_value
                obs = np.vstack((obs, enh_data))

        if self.z is None:
            self.z, self.t = pot(obs, risk=self._risk, init_level=self._init_level)
        else:
            self.z = self.z

    def predict(self, X):
        check_is_fitted(self, ['z'])

        X = check_array(X, ensure_2d=False)
        _check_dim(X)

        if self._extreme == "maximum":
            obs = np.reshape(X, (-1, 1))
        else:
            obs = - np.reshape(X, (-1, 1))

        prediction = (obs > self.z).astype('int').ravel()

        return prediction


def grimshaw(peaks: np.array, threshold: float, num_candidates: int = 10, epsilon: float = 1e-8):
    """ The Grimshaw's Trick Method
    The trick of thr Grimshaw's procedure is to reduce the two variables
    optimization problem to a signle variable equation.
    Args:
        peaks: peak nodes from original dataset.
        threshold: init threshold
        num_candidates: the maximum number of nodes we choose as candidates
        epsilon: numerical parameter to perform
    Returns:
        gamma: estimate
        sigma: estimate
    """
    min = peaks.min()
    max = peaks.max()
    mean = peaks.mean()

    if abs(-1 / max) < 2 * epsilon:
        epsilon = abs(-1 / max) / num_candidates

    a = -1 / max + epsilon
    b = 2 * (mean - min) / (mean * min)
    c = 2 * (mean - min) / (min ** 2)

    candidate_gamma = solve(function=lambda t: function(peaks, threshold),
                            dev_function=lambda t: dev_function(peaks, threshold),
                            bounds=(a + epsilon, -epsilon),
                            num_candidates=num_candidates
                            )
    candidate_sigma = solve(function=lambda t: function(peaks, threshold),
                            dev_function=lambda t: dev_function(peaks, threshold),
                            bounds=(b, c),
                            num_candidates=num_candidates
                            )
    candidates = np.concatenate([candidate_gamma, candidate_sigma])

    gamma_best = 0
    sigma_best = mean
    log_likelihood_best = cal_log_likelihood(peaks, gamma_best, sigma_best)

    for candidate in candidates:
        gamma = np.log(1 + candidate * peaks).mean()
        sigma = gamma / candidate
        log_likelihood = cal_log_likelihood(peaks, gamma, sigma)
        if log_likelihood > log_likelihood_best:
            gamma_best = gamma
            sigma_best = sigma
            log_likelihood_best = log_likelihood

    return gamma_best, sigma_best


def function(x, threshold):
    s = 1 + threshold * x
    u = 1 + np.log(s).mean()
    v = np.mean(1 / s)
    return u * v - 1


def dev_function(x, threshold):
    s = 1 + threshold * x
    u = 1 + np.log(s).mean()
    v = np.mean(1 / s)
    dev_u = (1 / threshold) * (1 - v)
    dev_v = (1 / threshold) * (-v + np.mean(1 / s ** 2))
    return u * dev_v + v * dev_u


def obj_function(x, function, dev_function):
    m = 0
    n = np.zeros(x.shape)
    for index, item in enumerate(x):
        y = function(item)
        m = m + y ** 2
        n[index] = 2 * y * dev_function(item)
    return m, n


def solve(function, dev_function, bounds, num_candidates):
    step = (bounds[1] - bounds[0]) / (num_candidates + 1)
    x0 = np.arange(bounds[0] + step, bounds[1], step)
    optimization = minimize(lambda x: obj_function(x, function, dev_function),
                            x0,
                            method='L-BFGS-B',
                            jac=True,
                            bounds=[bounds]*len(x0)
                            )
    x = np.round(optimization.x, decimals=5)
    return np.unique(x)


def cal_log_likelihood(peaks, gamma, sigma):
    if gamma != 0:
        tau = gamma/sigma
        log_likelihood = -peaks.size * log(sigma) - (1 + (1 / gamma)) * (np.log(1 + tau * peaks)).sum()
    else:
        log_likelihood = peaks.size * (1 + log(peaks.mean()))
    return log_likelihood


def pot(data: np.array, risk: float = 1e-4, init_level: float = 0.98, num_candidates: int = 10,
        epsilon: float = 1e-8) -> float:
    """ Peak-over-Threshold Alogrithm
    References:
    Siffer, Alban, et al. "Anomaly detection in streams with extreme value theory."
    Proceedings of the 23rd ACM SIGKDD International Conference on Knowledge
    Discovery and Data Mining. 2017.
    Args:
        data: data to process
        risk: detection level
        init_level: probability associated with the initial threshold
        num_candidates: the maximum number of nodes we choose as candidates
        epsilon: numerical parameter to perform

    Returns:
        z: threshold searching by pot
        t: init threshold
    """
    # Set init threshold
    t = np.sort(data)[int(init_level * data.size)]
    peaks = data[data > t] - t

    # Grimshaw
    gamma, sigma = grimshaw(peaks=peaks,
                            threshold=t,
                            num_candidates=num_candidates,
                            epsilon=epsilon
                            )

    # Calculate Threshold
    r = data.size * risk / peaks.size
    if gamma != 0:
        z = t + (sigma / gamma) * (pow(r, -gamma) - 1)
    else:
        z = t - sigma * log(r)

    return z, t
