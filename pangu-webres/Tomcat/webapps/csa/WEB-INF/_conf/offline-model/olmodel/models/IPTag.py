from pathlib import Path
import time
import datetime
import joblib
import pandas as pd
import numpy as np
from olmodel.models.utils import map_event_to_being_value, map_event_to_tag_name, get_subnet
from olmodel.models.BaseModel import BaseModel


class IPTagModel(BaseModel):
    def __init__(self, ck_handler, mysql_handler, model_config, name='scan', ts_window=60, **kwargs):
        super().__init__(ck_handler)
        self._ts_window = ts_window
        self._baseline_time = 14
        self._name = name
        self._time_offset = 0
        self._mysql_handler = mysql_handler
        # get time (format: 220603)
        self.event_table = datetime.datetime.now().strftime('%y%m%d')
        self._now_datetime = (datetime.datetime.now() + datetime.timedelta(minutes=self._time_offset)
                              ).strftime("%Y-%m-%d %H:%M:%S")

    def check_existing_scanner(self, csrcip, being_value):
        query_sql = """
        SELECT COUNT(*) FROM asap_tag_data_ip 
        WHERE being = '{being_ip}' AND being_value = '{being_value_ip}'
        """.format(
            being_ip=csrcip,
            being_value_ip=being_value
        )
        result = self._mysql_handler.select_all(query_sql)
        self._logger.get_log().info(f'查询结果: {result[0][0]}')
        return result and result[0][0] > 0

    def get_data_scan(self):
        sql_scan = """
        select csrcip, uniq(ceventname)as n
         from {event_table}
        where csrclogtype = '/security/IDS/Alert' 
		and startsWith(ceventtype, '/att')
		and ceventname not in ('HTTP_可疑行为_HTTP请求无法识别认证方法', 'TCP_协议握手_SSH连接_响应', 
		'HTTP_网络行为_基础认证Base64密码明文传输', 'HTTP_可疑行为_PE文件或DLL文件下载', 
		'TCP_网络行为_windows_update_p2p活动')
        and ceventname not like '%transfer of%'
		and ceventname not like '%文件还原%'
		and ceventname not like '%远程控制软件%'
        group by csrcip
        having n >= 10
        union all 
        select csrcip, count(1) as n from {event_table}
        where csrclogtype = '/security/IDS/Alert'
        and (ceventname like '%网络扫描%' or ceventname like '%扫描器%')
        and ceventname not in ('HTTP_网络扫描_不完整HTTP头部扫描行为', 'HTTP_网络扫描_CobaltStrike_team_server')
        group by csrcip
        """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f'Scan Model. Executing sql:\n{sql_scan}')
        df_data = self._ck_handler.get_data_func(sql_scan)
        return df_data

    def get_data_attack(self):
        sql = """
           select csrcip, ceventtype from incident_behavior ib 
           where toDate(update_datetime) >= today()
           and (startsWith(ceventtype, '/att') or startsWith(ceventtype, '/spy') or startsWith(ceventtype, '/malware'))
           group by csrcip, ceventtype
           """
        self._logger.get_log().info(f'Attack Model, Executing sql:\n{sql}')
        df_data = self._ck_handler.get_data_func(sql)
        return df_data

    def get_data_remote_control(self):
        sql = """
           select csrcip  
           from 
            {event_table}
            where ceventname like '%远程控制%'
            group by csrcip 
           """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f'Scan Model. Executing sql:\n{sql}')
        df_data = self._ck_handler.get_data_func(sql)
        return df_data

    def update_tag(self, df_data, tag_name, being_value):
        df_data['tag_name'] = tag_name
        df_data['being_value'] = being_value
        current_time_millis = int(time.time() * 1000)
        # expirtime = 3 days in ms
        df_data['expiry_time'] = current_time_millis + 3 * 24 * 60 * 60 * 1000
        df_data['tag_type'] = 3
        df_data['update_time'] = current_time_millis
        insert_data = [(row['tag_name'], row['csrcip'], row['being_value'],
                        row['tag_type'], row['expiry_time'], row['update_time'])
                       for index, row in df_data.iterrows()]
        return insert_data

    def run(self):
        # 扫描器标签
        df_data_scan = self.get_data_scan()
        if len(df_data_scan) == 0:
            self._logger.get_log().info("df_data_scan 当前没有数据。")
        elif len(df_data_scan) > 0:
            self._logger.get_log().info("模型执行成功。")
            insert_data_scan = self.update_tag(df_data_scan, tag_name='扫描ip', being_value='扫描器')
            self.insert_tag_data(insert_data_scan)

        df_data_remote_control = self.get_data_remote_control()
        if len(df_data_remote_control) == 0:
            self._logger.get_log().info("df_data_remote_control 当前没有数据。")
        elif len(df_data_remote_control) > 0:
            self._logger.get_log().info("模型执行成功。")
            insert_data_remote_control = self.update_tag(df_data_remote_control, tag_name='远程控制', being_value='远程控制软件')
            self.insert_tag_data(insert_data_remote_control)

        df_data_attack = self.get_data_attack()
        if len(df_data_attack) == 0:
            self._logger.get_log().info("df_data_attack 当前没有数据。")
        elif len(df_data_attack) > 0:
            self._logger.get_log().info("attack模型执行成功。")
            df_data_attack = self.update_attack_tag(df_data_attack)
            self.insert_attack_tag_data(df_data_attack)

        # 给ip打上标签：服务器ip，终端ip，未知
        df_data_ip_tag = self.ip_type_tag()
        if len(df_data_ip_tag) == 0:
            self._logger.get_log().info("df_data_ip_tag 当前没有数据")
        elif len(df_data_ip_tag) > 0:
            self.insert_ip_type_tag_data(df_data_ip_tag)
        return []

    def update_attack_tag(self, df_data_attack):
        df_data_attack['tag_name'] = df_data_attack['ceventtype'].apply(lambda x: map_event_to_tag_name(x))
        df_data_attack['being_value'] = df_data_attack['ceventtype'].apply(lambda x: map_event_to_being_value(x))
        df_data_attack = df_data_attack[df_data_attack['being_value'] != '']
        current_time_millis = int(time.time() * 1000)
        # expirtime = 3 days in ms
        df_data_attack['expiry_time'] = current_time_millis + 3 * 24 * 60 * 60 * 1000
        df_data_attack['tag_type'] = 3
        df_data_attack['update_time'] = current_time_millis

        df_attack_grouped = df_data_attack.groupby(['csrcip', 'tag_name']).agg({
            'being_value': lambda x: ','.join(set(x)),  # 使用set去重
            'expiry_time': 'max',
            'tag_type': 'first',
            'update_time': 'max'
        }).reset_index()
        self._logger.get_log().info("df_data_attack 未插入前的数据共{}条".format(len(df_attack_grouped)))
        return df_attack_grouped

    def insert_attack_tag_data(self, df_data_attack):
        filtered_insert_data = []
        for index, row in df_data_attack.iterrows():
            if not self.check_existing_scanner(row['csrcip'], '扫描器'):
                filtered_insert_data.append((row['tag_name'], row['csrcip'], row['being_value'],
                                             row['tag_type'], row['expiry_time'], row['update_time']))
        if filtered_insert_data:
            self._logger.get_log().info(f"要插入的数据: {filtered_insert_data}")
            insert_sql = """
                        INSERT INTO asap_tag_data_ip (tag_name, being, being_value, tag_type, expiry_time, update_time) 
                        VALUES (%s, %s, %s, %s, %s, %s) 
                        ON DUPLICATE KEY UPDATE 
                        being_value=VALUES(being_value), expiry_time=VALUES(expiry_time), update_time=VALUES(update_time)
                        """
            self._mysql_handler.replace_many(insert_sql, filtered_insert_data)
            self._logger.get_log().info("df_data_attack 数据插入成功, 共插入{}条数据".format(len(filtered_insert_data)))
        else:
            self._logger.get_log().info("df_data_attack 没有新的数据需要插入。")

    def insert_tag_data(self, insert_data):
        insert_sql = """
       INSERT INTO asap_tag_data_ip (tag_name, being, being_value, tag_type, expiry_time, update_time) 
       VALUES (%s, %s, %s, %s, %s, %s) 
       ON DUPLICATE KEY UPDATE 
       being_value=VALUES(being_value), expiry_time=VALUES(expiry_time), update_time=VALUES(update_time)
               """
        self._mysql_handler.replace_many(insert_sql, insert_data)
        self._logger.get_log().info("数据插入成功, 共插入{}条数据".format(len(insert_data)))

    def combine_dicts(self, *dict_lists):
        """
        合并多个字典列表。

        参数:
        *dict_lists: 变量数量的字典列表

        返回:
        合并后的字典列表
        """
        combined_list = []
        for dict_list in dict_lists:
            combined_list.extend(dict_list)
        return combined_list

    def ip_type_tag(self):
        """
        判断ip类型：1. 终端ip 2. 服务器ip 3. 未知
        server_ip, device_ip, unknown
        Returns
        -------
        服务器ip：开了很多端口
        能解析成域名的ip一般是服务器ip
        服务器常用端口号：21, 22, 23, 25, 53, 80, 110, 143, 443, 1433, 1434, 1521, 3306, 3389, 8080, 137, 138, 139
        < 1024
        终端ip：高端端口范围内的随机端口，这些端口主要用于向外部服务发送请求。
        """
        # 服务器常用服务
        app_protocol = ('db2', 'http', 'ssh', 'rdp', 'telnet', 'smb', 'pop3', 'smtp', 'redis', 'ftp',
                        'mqtt', 'dns', 'rfb', 'decerpc', 'krb5', 'pgsql', 'ftp-data', 'nfs', 'http2')
        # 服务器常用端口号
        app_port_num = (21, 22, 23, 25, 53, 80, 110, 143, 443, 1433,
                        1434, 1521, 3306, 3308, 3389, 8443, 8080, 8123, 6379)

        mysql_sql_ip = """
          SELECT ip, COUNT(*) AS count_open_port,
           SUM(CASE WHEN portNum IN {app_port_num} THEN 1 ELSE 0 END) AS count_port_server,
           SUM(CASE WHEN appprotocol IN {app_protocol} THEN 1 ELSE 0 END) AS count_port_app,
           CAST(SUM(CASE WHEN portNum > 10000 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) AS ports_server_ratio,
           CAST(SUM(CASE WHEN appprotocol IN {app_protocol} THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) AS ratio
        FROM xfc_ip_service
        GROUP BY ip
        """.format(
            app_protocol=app_protocol,
            app_port_num=app_port_num
        )
        self._logger.get_log().info(f"ip使用类型 mysql：{mysql_sql_ip}")
        server_ip_tuple = self._mysql_handler.select_all(mysql_sql_ip)
        df_data_ip = pd.DataFrame(server_ip_tuple,
                                  columns=['ip', 'count_open_port', 'count_port_server',
                                           'count_port_app', 'ports_server_ratio',
                                           'ratio'])
        # 获取ip所属的网段
        df_data_ip['subnet'] = df_data_ip['ip'].apply(lambda x: get_subnet(x, 24))
        # 统计每个网段的ip数量
        subnet_ip_count = df_data_ip.groupby('subnet')['ip'].count().reset_index(name='subnet_ip_count')
        df_data_ip = pd.merge(df_data_ip, subnet_ip_count, on='subnet', how='left')
        ip_tuple = tuple(df_data_ip['ip'])
        if len(ip_tuple) == 0:
            return pd.DataFrame()

        # 作为源ip时的特征
        ck_sql_csrcip = """
          SELECT
            csrcip as ip,
            uniq(cdstprovincename) AS uniq_cdstprovincename,
            uniq(dns_main_name) AS uniq_dns_main_name,
            uniq(cdstlocation) AS uniq_cdstlocation,
            uniqIf(cdstip, idstport IN (443, 110, 8080, 8000, 80, 3389)) AS uniq_service,
            countIf(isrcport, isrcport > 40000) AS isrcport_g1,
            countIf(isrcport, isrcport > 1024) AS isrcport_g2
        FROM
            {event_table}
            where csrcip in {ip_tuple}
            GROUP BY csrcip
    """.format(
            event_table="event20" + self.event_table,
            ip_tuple=ip_tuple,
        )
        self._logger.get_log().info(f"ip使用类型 源ip sql：{ck_sql_csrcip}")
        # 作为目的ip时的特征
        ck_sql_cdstip = """
               SELECT
                cdstip as ip,
                count() AS total_count,
                uniq(csrcip) AS uniq_csrcip,
                uniq(csrcprovincename) AS uniq_csrcprovincename,
                uniq(csrclocation) AS uniq_csrclocation,
                countIf(ceventname, ceventname = 'SSH协议日志') AS count_ssh_log
            FROM
                {event_table}
                 where cdstip in {ip_tuple}
                      GROUP BY
                cdstip
           """.format(
            event_table="event20" + self.event_table,
            ip_tuple=ip_tuple,
        )
        self._logger.get_log().info(f"ip使用类型 目的ip：{ck_sql_cdstip}")

        df_data_csrcip = self._ck_handler.get_data_func(ck_sql_csrcip)
        df_data_cdstip = self._ck_handler.get_data_func(ck_sql_cdstip)
        df_data = pd.merge(df_data_csrcip, df_data_cdstip, on='ip', how='outer')
        merged_df = pd.merge(df_data_ip, df_data, on='ip', how='left')

        columns_to_fill = ['isrcport_g1', 'isrcport_g2', 'uniq_service',
                             'uniq_cdstprovincename', 'uniq_dns_main_name', 'uniq_cdstlocation',
                             'total_count', 'uniq_csrcip', 'uniq_csrcprovincename',
                             'uniq_csrclocation', 'count_ssh_log',
                              'count_open_port', 'count_port_server']
        # 填充缺失值：按照所属网段特征的平均值填充
        merged_df[columns_to_fill] = merged_df.groupby('subnet')[columns_to_fill].transform(lambda x: x.fillna(x.mean()))
        # 如果仍有缺失值，填充0
        merged_df.fillna(0, inplace=True)
        X = merged_df[['uniq_service', 'count_open_port', 'count_port_server',
            'count_port_app', 'uniq_cdstprovincename', 'uniq_dns_main_name', 'uniq_cdstlocation',
            'total_count', 'uniq_csrcip', 'uniq_csrcprovincename',
            'uniq_csrclocation', 'count_ssh_log']].values

        current_path = Path(__file__).resolve()
        upper_path = current_path.parent.parent
        model = joblib.load(upper_path / 'model-lib' / 'model.pkl')
        labels = model.predict(X)
        df_data_ip['tag_name'] = np.where(labels == 1, '服务器', '终端')
        df_data_ip['being'] = df_data_ip['ip']
        return df_data_ip

    def insert_ip_type_tag_data(self, df_data_server_ip):
        tag_name_list = ['服务器', '终端']
        for tag_name in tag_name_list:
            insert_data = self.update_ip_type_tag(df_data_server_ip[df_data_server_ip['tag_name'] == tag_name],
                                                  tag_name='ip使用类型', being_value=tag_name)
            self._logger.get_log().info(f"{tag_name} 准备写入数据...")
            self.insert_tag_data(insert_data)

    def update_ip_type_tag(self, df_data, tag_name, being_value):
        # 标签分类,1:基础属性, 2:行为习惯, 3:安全
        df_data['tag_name'] = tag_name
        df_data['being_value'] = being_value
        current_time_millis = int(time.time() * 1000)
        # expirtime = 3 days in ms
        df_data['expiry_time'] = current_time_millis + 3 * 24 * 60 * 60 * 1000
        df_data['tag_type'] = 1
        df_data['update_time'] = current_time_millis
        insert_data = [(row['tag_name'], row['being'], row['being_value'],
                        row['tag_type'], row['expiry_time'], row['update_time'])
                       for index, row in df_data.iterrows()]
        return insert_data
