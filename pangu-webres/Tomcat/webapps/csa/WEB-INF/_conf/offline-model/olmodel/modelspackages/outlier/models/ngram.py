# -*- coding: utf-8 -*-
from __future__ import division
from __future__ import print_function
from nltk.util import bigrams
from nltk.lm import MLE
from nltk.lm import KneserNeyInterpolated
from nltk.lm.preprocessing import padded_everygram_pipeline
from nltk.corpus import stopwords
import warnings
from itertools import combinations
import numpy as np
from .base import BaseDetector


# noinspection PyPep8Naming
class Ngram(BaseDetector):

    def __init__(self, contamination: float, n):
        super(Ngram, self).__init__()
        self.contamination = contamination
        self.n = n

    def fit(self, X, y=None):
        """Fit detector. y is ignored in unsupervised methods.

        Parameters
        ----------
        X : numpy array of shape (n_samples, n_features)
            The input samples.

        y : Ignored
            Not used, present for API consistency by convention.

        Returns
        -------
        self : object
            Fitted estimator.
        """
        token_X = self.tokennization(X)
        train_data, padded_sents = padded_everygram_pipeline(self.n, token_X)
        # self.detector_ = MLE(self.n)
        self.detector_ = KneserNeyInterpolated(self.n)
        self.detector_.fit(train_data, padded_sents)
        self.decision_scores_ = self.decision_function(X)
        return self

    def tokennization(self, X):
        """
        description:
            分词
        Args:
            text: 文本 type: pd.DataFrame
        Returns:
            final_data: 分词后的文本 type: List
        """
        final_lines = list()
        lines = X.values.tolist()

        count = 0
        for line in lines:
            tokens = self.text_to_word_sequence(input_text=line[0])
            tokens = [w.lower() for w in tokens]
            test_puc = '!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~//'
            table = str.maketrans('', '', test_puc)
            stripped = [w.translate(table) for w in tokens]
            # words = [word for word in stripped if word.isalpha()]
            words = [word for word in stripped]
            final_lines.append(words)
            count += 1

        return final_lines

    def predict(self, X):
        """
        Predict if a particular sample is an outlier or not.
        Parameters
        ----------
        X : {array-like, sparse matrix} of shape (n_samples, n_features)
            The input samples. Internally, it will be converted to
            ``dtype=np.float32`` and if a sparse matrix is provided
            to a sparse ``csr_matrix``.
        Returns
        -------
        is_inlier : ndarray of shape (n_samples,)
            For each observation, tells whether or not (+1 or -1) it should
            be considered as an inlier according to the fitted model.
        """
        decision_func = self.decision_function(X)
        is_inlier = np.zeros_like(decision_func, dtype=int)
        threshold = np.percentile(self.decision_scores_, self.contamination * 100)
        is_inlier[decision_func < threshold] = 1
        return is_inlier, decision_func, threshold

    def decision_function(self, X):
        """
        Average anomaly score of X of the base classifiers.
        The anomaly score of an input sample is computed as
        the mean anomaly score of the trees in the forest.
        The measure of normality of an observation given a tree is the depth
        of the leaf containing this observation, which is equivalent to
        the number of splittings required to isolate this point. In case of
        several observations n_left in the leaf, the average path length of
        a n_left samples isolation tree is added.
        Parameters
        ----------
        X : {array-like, sparse matrix} of shape (n_samples, n_features)
            The input samples. Internally, it will be converted to
            ``dtype=np.float32`` and if a sparse matrix is provided
            to a sparse ``csr_matrix``.
        Returns
        -------
        scores : ndarray of shape (n_samples,)
            The anomaly score of the input samples.
            The lower, the more abnormal. Negative scores represent outliers,
            positive scores represent inliers.
        """
        final_lines = self.tokennization(X)
        prob_list = []
        if self.n == 2:
            for l in final_lines:
                if len(l) == 1:
                    prob_list.append(self.detector_.score(l[0]))
                else:
                    prob_list.append(self.__get_prob_2(self.__get_bigrams(l)))
        elif self.n == 3:
            for l in final_lines:
                if len(l) == 1:
                    prob_list.append(self.detector_.score(l[0]))
                elif len(l) == 2:
                    prob_list.append(self.__get_prob_2(self.__get_bigrams(l)))
                else:
                    prob_list.append(self.__get_prob_3(self.__get_trigrams(l)))

        return np.array(prob_list)

    def __get_bigrams(self, lines):
        """
        description:
            获取词二元组
        """
        return list(bigrams(lines))

    def __get_trigrams(self, lines):
        """
        description:
            获取词三元组
        """
        return list(self.trigrams(lines))

    def __get_prob_2(self, list_bigrams):
        """
        description:
            二元组概率累乘
        """
        p = 0
        for iter1, iter2 in list_bigrams:
            p += np.log(self.detector_.score(iter2, [iter1]))
        return p

    def __get_prob_3(self, list_trigrams):
        """
        description:
            三元组概率累乘
        """
        p = 0
        for iter1, iter2, iter3 in list_trigrams:
            p += self.detector_.score(iter3, [iter1, iter2])
        return p

    def get_prob_list(self, final_lines):
        prob_list = []
        if self.n == 2:
            for l in final_lines:
                if len(l) == 1:
                    prob_list.append(self.detector_.score(l[0]))
                else:
                    prob_list.append(self.__get_prob_2(self.__get_bigrams(l)))
        elif self.n == 3:
            for l in final_lines:
                if len(l) == 1:
                    prob_list.append(self.detector_.score(l[0]))
                elif len(l) == 2:
                    prob_list.append(self.__get_prob_2(self.__get_bigrams(l)))
                else:
                    prob_list.append(self.__get_prob_3(self.__get_trigrams(l)))
            prob_list = 1 - np.array(prob_list)

        # 获取整个句子的概率，1-prob为异常概率
        prob_list = 1 - np.array(prob_list)
        return prob_list

    def text_to_word_sequence(self, input_text,
                              filters='!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~\t\n',
                              lower=True,
                              split=' '):
        r"""Converts a text to a sequence of words (or tokens).
        Deprecated: `tf.keras.preprocessing.text.text_to_word_sequence` does not
        operate on tensors and is not recommended for new code. Prefer
        `tf.strings.regex_replace` and `tf.strings.split` which provide equivalent
        functionality and accept `tf.Tensor` input. For an overview of text handling
        in Tensorflow, see the [text loading tutorial]
        (https://www.tensorflow.org/tutorials/load_data/text).
        This function transforms a string of text into a list of words
        while ignoring `filters` which include punctuations by default.
        >>> sample_text = 'This is a sample sentence.'
        >>> tf.keras.preprocessing.text.text_to_word_sequence(sample_text)
        ['this', 'is', 'a', 'sample', 'sentence']
        Args:
            input_text: Input text (string).
            filters: list (or concatenation) of characters to filter out, such as
                punctuation. Default: ``'!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~\\t\\n'``,
                  includes basic punctuation, tabs, and newlines.
            lower: boolean. Whether to convert the input to lowercase.
            split: str. Separator for word splitting.
        Returns:
            A list of words (or tokens).
        """
        if lower:
            input_text = input_text.lower()

        translate_dict = {c: split for c in filters}
        translate_map = str.maketrans(translate_dict)
        input_text = input_text.translate(translate_map)

        seq = input_text.split(split)
        return [i for i in seq if i]
