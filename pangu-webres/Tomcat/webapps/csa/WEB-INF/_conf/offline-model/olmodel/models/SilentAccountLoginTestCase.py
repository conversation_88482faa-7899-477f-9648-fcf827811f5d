import unittest
from olmodel.models.BaseTestCase import BaseTestCase
from olmodel.models.SilentAccountLogin import SilentAccountLogin


class SilentAccountLoginTestCase(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(SilentAccountLoginTestCase, self).__init__(*args, **kwargs)
        btc = BaseTestCase()
        self._test_case = SilentAccountLogin(btc._ck_handler, None)

    def test_get_data(self):
        self._test_case.get_data()

    def test_list(self):
        tmp1 = list()
        tmp1.append("dfdf")
        tmp1.append("33333")
        print(",".join(tmp1))


if __name__ == '__main__':
    unittest.main()
