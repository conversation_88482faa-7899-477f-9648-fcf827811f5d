# -*- coding: utf-8 -*-
from __future__ import division
from __future__ import print_function
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import Normalizer
from sklearn.decomposition import TruncatedSVD
from sklearn.pipeline import make_pipeline
import numpy as np
from .base import BaseDetector


# noinspection PyPep8Naming
class Tfidf(BaseDetector):

    def __init__(self, contamination: float, norm):
        super(Tfidf, self).__init__()
        self._dim = 50  # data preprocessing default data dimension >= 50
        self.contamination = contamination
        self.norm = norm

    def fit(self, X, y=None):
        """Fit detector. y is ignored in unsupervised methods.

        Parameters
        ----------
        X : numpy array of shape (n_samples, n_features)
            The input samples.

        y : Ignored
            Not used, present for API consistency by convention.

        Returns
        -------
        self : object
            Fitted estimator.
        """
        token_X = self.tokennization(X)
        X, self._itf_scaler = self.itf_scaler(token_X)
        if X.shape[1] < self._dim:
            X, self._svdscaler = self.svd_normalizer(X.toarray())
            self.decision_scores_ = self.decision_function_v2(X)
            self.threshold = np.percentile(self.decision_scores_, self.contamination * 100)
        else:
            X, self._svdscaler = self.svd_normalizer(X.toarray())
            self.decision_scores_ = self.decision_function_v2(X)
            self.threshold = np.percentile(self.decision_scores_, self.contamination * 100)
        return self

    def decision_function_v2(self, X):
            detector_score = self.euklid_distance(X)
            return detector_score

    def tokennization(self, X):
        """
        description:
            分词
        Args:
            text: 文本 type: pd.DataFrame
        Returns:
            final_data: 分词后的文本 type: List
        """

        final_lines = list()
        lines = X.values.tolist()

        count = 0
        for line in lines:
            tokens = self.text_to_word_sequence(input_text=line[0])
            tokens = [w.lower() for w in tokens]
            test_puc = '!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~//'
            table = str.maketrans('', '', test_puc)
            stripped = [w.translate(table) for w in tokens]
            words = [word for word in stripped if word.isalpha()]
            final_lines.append(words)
            count += 1

        idf_l = []
        for line in final_lines:
            tem_str = ' '.join(line)
            idf_l.append(tem_str)
        return idf_l

    def itf_scaler(self, X):
        tfidf_vec = TfidfVectorizer(norm=self.norm)
        tfidf_vec.fit(X)
        tfidf_matrix = tfidf_vec.transform(X)
        return tfidf_matrix, tfidf_vec

    def svd_normalizer(self, tfidf_matrix):
        if tfidf_matrix.shape[1] <= self._dim:
            svd = TruncatedSVD(n_components=4)
            x_train = svd.fit_transform(tfidf_matrix)
        else:
            svd = TruncatedSVD(n_components=self._dim)
            x_train = svd.fit_transform(tfidf_matrix)
        return x_train, svd

    def predict(self, X):
        """
        Predict if a particular sample is an outlier or not.
        Parameters
        ----------
        X : {array-like, sparse matrix} of shape (n_samples, n_features)
            The input samples. Internally, it will be converted to
            ``dtype=np.float32`` and if a sparse matrix is provided
            to a sparse ``csr_matrix``.
        Returns
        -------
        is_inlier : ndarray of shape (n_samples,)
            For each observation, tells whether or not (+1 or -1) it should
            be considered as an inlier according to the fitted model.
        """
        token_X = self.tokennization(X)
        X = self._itf_scaler.transform(token_X)
        if X.shape[1] < self._dim:
            decision_func = self.decision_function_v2(X.toarray())
        else:
            X = self._svdscaler.transform(X)
            decision_func = self.decision_function_v2(X)
        is_inlier = np.zeros_like(decision_func, dtype=int)
        is_inlier[decision_func < self.threshold] = 1
        return is_inlier, decision_func, self.threshold

    def euklid_distance(self, X):
        return np.sqrt(np.sum(np.square(X), axis=1))

    def decision_function(self, X):
        """
        Average anomaly score of X of the base classifiers.
        The anomaly score of an input sample is computed as
        the mean anomaly score of the trees in the forest.
        The measure of normality of an observation given a tree is the depth
        of the leaf containing this observation, which is equivalent to
        the number of splittings required to isolate this point. In case of
        several observations n_left in the leaf, the average path length of
        a n_left samples isolation tree is added.
        Parameters
        ----------
        X : {array-like, sparse matrix} of shape (n_samples, n_features)
            The input samples. Internally, it will be converted to
            ``dtype=np.float32`` and if a sparse matrix is provided
            to a sparse ``csr_matrix``.
        Returns
        -------
        scores : ndarray of shape (n_samples,)
            The anomaly score of the input samples.
            The lower, the more abnormal. Negative scores represent outliers,
            positive scores represent inliers.
        """
        # We subtract self.offset_ to make 0 be the threshold value for being
        # an outlier:
        token_X = self.tokennization(X)
        X = self._itf_scaler.transform(token_X)
        if X.shape[1] < self._dim:
            detector_score = self.detector_.score_samples(X)
            _offset = np.percentile(detector_score, self.contamination * 100)
            return detector_score - _offset
        else:
            X = self._svdscaler.transform(X)
            dis_score = self.euklid_distance(X)
            # detector_score = self.detector_.score_samples(X)
            # _offset = np.percentile(detector_score, self.contamination * 100)
            self.threshold = np.percentile(dis_score, self.contamination * 100)

            return dis_score

    def text_to_word_sequence(self, input_text,
                              filters='!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~\t\n',
                              lower=True,
                              split=' '):
        r"""Converts a text to a sequence of words (or tokens).
        Deprecated: `tf.keras.preprocessing.text.text_to_word_sequence` does not
        operate on tensors and is not recommended for new code. Prefer
        `tf.strings.regex_replace` and `tf.strings.split` which provide equivalent
        functionality and accept `tf.Tensor` input. For an overview of text handling
        in Tensorflow, see the [text loading tutorial]
        (https://www.tensorflow.org/tutorials/load_data/text).
        This function transforms a string of text into a list of words
        while ignoring `filters` which include punctuations by default.
        >>> sample_text = 'This is a sample sentence.'
        >>> tf.keras.preprocessing.text.text_to_word_sequence(sample_text)
        ['this', 'is', 'a', 'sample', 'sentence']
        Args:
            input_text: Input text (string).
            filters: list (or concatenation) of characters to filter out, such as
                punctuation. Default: ``'!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~\\t\\n'``,
                  includes basic pun ctuation, tabs, and newlines.
            lower: boolean. Whether to convert the input to lowercase.
            split: str. Separator for word splitting.
        Returns:
            A list of words (or tokens).
        """
        if lower:
            input_text = input_text.lower()

        translate_dict = {c: split for c in filters}
        translate_map = str.maketrans(translate_dict)
        input_text = input_text.translate(translate_map)

        seq = input_text.split(split)
        return [i for i in seq if i]