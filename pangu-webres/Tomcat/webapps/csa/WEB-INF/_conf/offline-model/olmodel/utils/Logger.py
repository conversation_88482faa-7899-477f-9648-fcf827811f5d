# -*- coding:utf-8 -*-
import logging
import logging.handlers
import sys
from olmodel.constants import GlobalConsts
import os
import codecs
import yaml

# 从配置文件中读取输出日志的位置
filepath = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                        "model-config.yaml")
with codecs.open(filepath, 'r', encoding='utf-8') as f:
    content = yaml.safe_load(f)
    for item in content['configs']:
        if item['name'] == "log_path":
            logpath = item["value"]
            if not os.path.exists(logpath):
                os.makedirs(logpath)
            logfilepath = os.path.join(logpath, "offline-model.log")


class Logger(object):

    def __init__(self, logger_name):
        logger_level_map = {
            'debug': logging.DEBUG,
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR
        }
        # 创建一个logger
        self.logger = logging.getLogger(logger_name)

        # 从全局配置中获取日志级别
        log_level = GlobalConsts.get_value('log_level')

        # 设置日志级别
        if log_level not in logger_level_map:
            print(f'log_level配置错误, 只能设置为(debug, info, error, warning), 然而为：{log_level}')
            sys.exit(-1)
        _log_level = logger_level_map.get(log_level)
        # print(f'log_level为{_log_level}')
        self.logger.setLevel(_log_level)

        # 为了防止输出重复日志记录添加判断，如果logger.handlers列表为空，则添加handler，否则，直接去写日志
        # 执行多次addHandler可能会造成一条日志重复输出的情况
        if not self.logger.handlers:
            # 创建一个handler，用于写入日志文件
            filehandler = logging.handlers.RotatingFileHandler(
                filename=logfilepath,
                # 每个日志文件的最大字节数
                maxBytes=10 * 1024 * 1024,
                # 保存日志备份个数
                backupCount=5)
            # filehandler = logging.handlers.TimedRotatingFileHandler(
            #     filename=logfilepath,
            #     when='D',
            #     interval=1,
            #     encoding='utf-8'
            # )
            filehandler.suffix = '%Y-%m-%d.log'
            filehandler.setLevel(_log_level)

            # 创建一个handler，用于将日志输出到控制台
            ch = logging.StreamHandler(sys.stdout)
            ch.setLevel(_log_level)

            # 定义handler的输出格式
            formatter = logging.Formatter(
                '%(asctime)s-%(module)s-%(funcName)s-%(levelname)s-line[%(lineno)s]-[%(thread)s]: %(message)s'
            )
            filehandler.setFormatter(formatter)
            ch.setFormatter(formatter)

            # 给logger添加handler
            self.logger.addHandler(filehandler)
            self.logger.addHandler(ch)

    def get_log(self):
        """定义一个函数，回调logger实例"""
        return self.logger


def test():
    Logger(__name__).get_log().debug("User %s is loging" % 'jeck')


if __name__ == '__main__':
    test()
