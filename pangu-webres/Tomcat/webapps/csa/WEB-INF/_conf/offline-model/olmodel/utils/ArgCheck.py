# -*- coding:utf-8 -*-
from olmodel.constants.AlgConsts import Type_Int, Type_Float, Type_Boolean, Type_String
from olmodel.constants.ModelConsts import MODEL_ARGS
from olmodel.exceptions.ParamException import ParamException
from olmodel.utils.Logger import Logger


logger = Logger(__name__)


def model_args_check(web_param_dict):
    """
    执行模型的参数检查(不检查参数类型)
    :param param_dict: 模型参数 dict type
    :return:
    """
    for name in MODEL_ARGS:
        if name not in web_param_dict:
            raise ParamException(f'执行模型参数检查，缺少模型参数：{name}')
    logger.get_log().debug('模型参数检查通过')


def algorithm_args_check(alg_name, arg_param_dict, default_alg_lib):
    """
    执行算法的参数检查，检查前端界面传过来的算法参数是否完整
    界面传来的参数需包含某算法所需的全部参数
    如参数不全，返回False，将启用内置配置文件中的算法参数
    :param default_alg_lib:
    :param alg_name: 算法名称
    :param arg_param_dict: 界面传来的参数字典
    :return: alg_dict or False (参数完整 or 不完整)
    """
    if alg_name not in default_alg_lib:
        raise ParamException(f'执行算法参数检查，算法名称不存在，算法名称：{alg_name}')

    if not arg_param_dict:
        return None

    _args_ = default_alg_lib.get(alg_name)

    alg_dict = {}
    for arg_name, algParam in _args_.algorithm_params.items():
        if arg_name not in arg_param_dict:
            logger.get_log().warning(f'执行算法参数检查，缺少参数：{arg_name}')
            return False
        alg_dict[arg_name] = arg_param_dict.get(arg_name)
        if algParam.value_type == Type_Int:
            alg_dict[arg_name] = int(alg_dict[arg_name])
        elif algParam.value_type == Type_Float:
            alg_dict[arg_name] = float(alg_dict[arg_name])
        elif algParam.value_type == Type_Boolean:
            alg_dict[arg_name] = boolean(alg_dict[arg_name])
        elif algParam.value_type == Type_String:
            alg_dict[arg_name] = str(alg_dict[arg_name])
        # elif algParam.value_type == Type_Json:
        #     alg_dict[arg_name] = json(alg_dict[arg_name])
        else:
            raise TypeError(f'Unexpected Offline argument type: {algParam.value_type}')

    return alg_dict


def boolean(input_str):
    if isinstance(input_str, str):
        input_str = input_str.lower()
        if input_str == 'true':
            return True
        if input_str == 'false':
            return False
    return input_str
