import numpy as np
import pickle
import re
import pandas as pd

from olmodel.modelspackages.cmdcontentclassifer.stringrandom import gib_detect_train

GIB_MODEL_PATH = 'olmodel/modelspackages/cmdcontentclassifer/stringrandom/gib_model.pki'
ENWORDS_N_GRAM_PATH = 'olmodel/modelspackages/cmdcontentclassifer/stringrandom/enwords_n_gram.txt'

class StrRandFeatureExtractor:

    def __init__(self):
        self.enwords_n_gram = set()
        # read 英文单词的n-gram
        with open(ENWORDS_N_GRAM_PATH, 'r') as f:
            for line in f.readlines():
                line = line.strip()
                self.enwords_n_gram.add(line)

        self.gib_model_data = pickle.load(open(GIB_MODEL_PATH, 'rb'))
        self.model_mat = self.gib_model_data['mat']
        self.threshold = self.gib_model_data['thresh']

        self.columns_name = ['bigram_enwords_rate', 'trigram_enwords_rate', 'vowel_ratio', 'digit_ratio', 'unique_ratio', 'gib_value', 'cnt_pon']

    def gen_feature(self, row):
        sl_domain = '$' + str(row) + '$'
        main_domain = re.sub('([0-9]+)', '#', sl_domain) # 把数字替换为井号

        f_len = len(sl_domain)

        bigram_list = self.ngrams(2, sl_domain)
        trigram_list = self.ngrams(3, sl_domain)

        bigram_enwords_cnt = 0
        for gram in bigram_list:
            if gram in self.enwords_n_gram:
                bigram_enwords_cnt += 1

        trigram_enwords_cnt = 0
        for gram in trigram_list:
            if gram in self.enwords_n_gram:
                trigram_enwords_cnt += 1

        bigram_enwords_rate = bigram_enwords_cnt / len(bigram_list)
        trigram_enwords_rate = trigram_enwords_cnt / len(trigram_list)


        vowel_ratio = self.count_vowels(main_domain) / f_len
        digit_ratio = self.count_digits(sl_domain) / f_len
        unique_ratio = self.unique_letters(sl_domain) / f_len

        gib_value = int(gib_detect_train.avg_transition_prob(sl_domain.strip('$'), self.model_mat) > self.threshold)

        cnt_pon = main_domain.count('#')

        return [bigram_enwords_rate, trigram_enwords_rate, vowel_ratio, digit_ratio, unique_ratio, gib_value, cnt_pon]


    def ngrams(self, n, word):   #字符串切分
        ngram_list = []
        if len(word) < n:
            return ngram_list
        for i in range(len(word)-n+1):
            ngram_list.append(word[i:i+n])
        return ngram_list

    def count_vowels(self,word):  # how many a,e,i,o,u
        vowels = list('aeiou')
        return sum(vowels.count(i) for i in word.lower())

    def count_digits(self,word):  # how many digits
        digits = list('0123456789')
        return sum(digits.count(i) for i in word.lower())

    def unique_letters(self,word):
        words = set(word)
        return len(words)

    def normalize(self, column):
        f_mean = column.mean()
        f_std = column.std()

        if np.isnan(f_std): # 方差为NAN则直接返回
            return column

        if f_std == 0: # 方差为0则归一化的结果后全为0
            f_std =1

        column = (column - f_mean) / f_std
        return column

    def mean_std(self, df):
        df.fillna(0.0,inplace = True)
        feas_list = list(df.loc['mean']) + list(df.loc['std'])
        return feas_list

    def run(self, con_list):
        feas_list = []
        for con in con_list:
            fea = self.gen_feature(con)
            feas_list.append(fea)

        feas_df = pd.DataFrame(feas_list, columns=self.columns_name)
        # print(feas_df)
        feas_meanstd_list = self.mean_std(feas_df.describe())
        return feas_meanstd_list

if __name__ == '__main__':
    con_ist = ['af070de58', 'QRFXGBC', '677RNOS', 'nBF', 'Zvpebfbsg', 'Jvaqbjf', 'nBF', 'Ohvyq', 'nBF', 'Zvpebfbsg', 'Pbecbengvba', 'nBF', 'nBF', 'Zhygvcebprffbe', 'Serr', 'Jvaqbjf', 'NN077', 'IZjner', 'Vap', 'IZjner7', 'k64', 'onfrq', 'Vagry64', 'Snzvyl', 'Zbqry', 'Fgrccvat', 'TrahvarVagry', 'Zum', 'nOVBF', 'IZjner', 'Vap', 'IZJ71', '00I', 'O64', 'nJvaqbjf', 'Jvaqbjf', 'Jvaqbjf', 'flfgrz32', 'Qrivpr', 'UneqqvfxIbyhzr1', 'HGP', 'cntrsvyr', 'flf', 'JBEXTEBHC', 'QRFXGBC', '677RNOS', 'AVP', 'Vagry', '82574Y', 'Tvtnovg', 'Argjbex', 'Pbaarpgvba', 'Rgurearg0', 'QUPC', 'QUPC', 'sr80', 'p674', '22p0', 'nUlcre', 'Ulcre', 'cucfghql', 'n627562']

    strrand_fea = StrRandFeatureExtractor()
    feas_list = strrand_fea.run(con_ist)
    print(feas_list)




