import unittest
import os
from olmodel.utils.FileUtil import FileUtil


class MyTestCase(unittest.TestCase):
    def test_something(self):
        self.assertEqual(True, False)

    def test_read_lines(self):
        pwd = os.getcwd()
        file_path = os.path.join(pwd, "test.txt")
        file_util = FileUtil()
        lines = file_util.read_lines(file_path)

        for line in lines:
            print(line)

    def test_write(self):
        pwd = os.getcwd()
        file_util = FileUtil()

        c1 = list()
        c1.append("fdfdf")
        c1.append("oo999ii")
        c2 = list()
        c2.append("kkkkkk")
        c2.append("dfsdf")

        content = list()
        content.append(",".join(c1))
        content.append(",".join(c2))

        file_util.write(pwd, "test1.txt", "\n".join(content))


if __name__ == '__main__':
    unittest.main()
