# -*- coding:utf-8 -*-
import time
import Levenshtein
from typing import List, Tuple, Dict, Set, Union, Optional, Iterable
import numpy as np
import datetime
import tldextract
from olmodel.models.BaseModel import BaseModel

# Levenshtein distance
# https://en.wikipedia.org/wiki/Levenshtein_distance


def similarity(text1: str, text2: str, round_n=3) -> float:
    return np.round(Levenshtein.ratio(text1, text2), round_n)


class Mimic(BaseModel):

    def __init__(self, ck_handler, model_config, ts_window=60, name="dns_main_name", target="Google, Twitter, Github", **kwargs):
        super().__init__(ck_handler)
        self._ts_window = ts_window
        self._baseline_time = 14
        self._name = name
        self._time_offset = 0
        self._target = target
        # get time (format: 220603)
        self.event_table = datetime.datetime.now().strftime('%y%m%d')
        self._now_datetime = (datetime.datetime.now() + datetime.timedelta(minutes=self._time_offset)
                              ).strftime("%Y-%m-%d %H:%M:%S")

    def get_data(self):
        sql = """
            select cuserid, csrcip, cdstip, idstport, csrcipinner, cdstipinner, iprotocol, iappprotocol, dns_main_name, '999784571044127150' as behaviorid
            from {event_table} where startsWith(ceventtype,'/behavior') and notEmpty(dns_main_name)
            and eventdate > subtractMinutes(toDateTime('{now_datetime}'), {window_time})
            and multiMatchAny(dns_main_name, ['[a-zA-Z]']) 
        """.format(
            event_table="event20" + self.event_table,
            now_datetime=self._now_datetime,
            window_time=self._ts_window
        )
        self._logger.get_log().info(f'Mimic algorithm. Executing sql:\n{sql}')
        df = self._ck_handler.get_data_func(sql)
        return df

    def run(self):
        self._target = self._target.split(",")
        df = self.get_data()
        if len(df) == 0:
            self._logger.get_log().info("字符串模仿算法，当前没有数据。")
            return []
        df['domain'] = df['dns_main_name'].apply(lambda url: tldextract.extract(url).domain)
        test_set = df['domain']
        # idx score > 0.7
        abnormal_score_idx = [idx for idx, score in enumerate(self.predict(test_set)) if score > 0.7]
        abnormal_data = df.iloc[abnormal_score_idx]
        # filter target not in abnormal_data['domain'] not case sensitive
        # abnormal_data = abnormal_data[abnormal_data['domain'].apply(lambda x: not any(target.lower() in x.lower() for target in self.target))]
        # to dict
        abnormal_data_dict = abnormal_data.to_dict('records')
        return abnormal_data_dict

    def predict(self, test_set) -> Iterable[float]:
        for text in test_set:
            max_score = 0
            for i in self._target:
                score = similarity(text, i)
                if score > max_score:
                    max_score = score
            yield max_score





