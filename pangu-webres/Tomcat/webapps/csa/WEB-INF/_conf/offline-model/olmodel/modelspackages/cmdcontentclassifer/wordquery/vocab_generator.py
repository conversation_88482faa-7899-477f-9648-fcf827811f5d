# coding: utf-8
import re

# 用于生成字典 vocab,py


def only_letters(tested_string):
    # match = re.match("^[A-Za-z]*$", tested_string)

    match = re.match("[A-Za-z]", tested_string)
    return match is not None


# 可以排除乱码
def only_contain_letter_and_number_exclude_luanma(s):
    return all(i.isdigit() or only_letters(i) for i in s)


def parser():
    word_dict = {}
    word_set = set()

    read_file = r'G:\anc_all_count.txt'
    with open(read_file, errors='ignore') as f_read:
        lines = f_read.readlines()
        for line in lines:
            if '\t' in line:
                line_split = line.split('\t')
            else:
                break
            word, word_freq = line_split[0], line_split[-1].strip()
            # print(word, word_freq, type(word_freq))
            # if word_freq == '100':
            #     print(1111)
            #     break
            if 15 > len(word) > 1 and only_contain_letter_and_number_exclude_luanma(word):

                if int(word_freq) < 400 and len(word) <= 2:
                    print(word)
                    continue

                if int(word_freq) < 300 and len(word) <= 5:
                    print(word)
                    continue

                if int(word_freq) < 200:
                    print(word)
                    continue

                if len(word) not in word_dict:
                    word_dict[len(word)] = set()

                word_dict[len(word)].add(word)
                word_set.add(word)
    print('word_cnt : {}'.format(len(word_set)))
    print(sorted(list(word_dict.keys())))
    word_dict[6].add('jquery')
    word_dict[6].add('upload')
    word_dict[3].add('bug')

    with open('vocab.py', 'w') as f_write:
        # f_write.write('\nword_dict = {}\n\nword_list = {}\n\n'.format(word_dict, list(word_set)))
        f_write.write('\nword_dict = {}\n'.format(word_dict))


if __name__ == '__main__':
    # jquery upload
    parser()
