# -*- coding:utf-8 -*-
from olmodel.models.BaseModel import BaseModel
from olmodel.models.ResignedEmployeeLogin import ResignedEmployeeLogin
from olmodel.models.LandSpeedViolation import LandSpeedViolation
from olmodel.models.SilentAccountLogin import SilentAccountLogin
from olmodel.models.TopNToWatchList import TopNToWatchList


class Others(BaseModel):

    def __init__(self, ck_handler, mysql_handler, model_config, ts_window=7, name="others", params='{"silentDays":30}'):
        super().__init__(ck_handler)
        self._mysql_handler = mysql_handler
        self._model_config = model_config
        self._ts_window = ts_window
        self._name = name
        self._params = params

    def get_data(self):
        if self._name == "ResignedEmployeeLogin":
            resigned_employee_login = ResignedEmployeeLogin(self._ck_handler, self._mysql_handler, self._model_config)
            return resigned_employee_login.get_data()

        elif self._name == "LandSpeedViolation":
            land_speed_violation = LandSpeedViolation(self._ck_handler, self._model_config)
            return land_speed_violation.get_data()

        elif self._name == "SilentAccountLogin":
            silent_account_login = SilentAccountLogin(self._ck_handler, self._model_config, self._params)
            return silent_account_login.get_data()
        elif self._name =="TopNToWatchList":
            TopNToWatchList(self._ck_handler, self._model_config, self._params)
            return []
        return []

    def run(self):
        offline_result = self.get_data()
        return offline_result
