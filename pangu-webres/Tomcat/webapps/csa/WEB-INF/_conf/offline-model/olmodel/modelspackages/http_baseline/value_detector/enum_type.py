from .base import BaseDetector
from ..base_function import modify_sigmoid


class EnumDetector(BaseDetector):

    def __init__(self):
        self.values_counts = None

    def fit(self, data):
        self.values_counts = data
        return self

    def predict(self, data):
        if data not in self.values_counts.index:
            return 0.7
        else:
            s = modify_sigmoid(self.values_counts[data], alpha=0.7)
            return s
