# -*- coding:utf-8 -*-
import datetime
import json
from olmodel.models.BaseModel import BaseModel
from olmodel.sinks.KafkaSender import KafkaSender


class TopNToWatchList(BaseModel):
    def __init__(self, ck_handler, model_config, params='{"mvID":1594779557989205718,"topN":3}', ts_window=7, name="TopNToWatchList", **kwargs):
        super().__init__(ck_handler)
        self._model_config = model_config
        self._logger.get_log() \
            .info(f'params1 {ts_window}')
        self._ts_window = ts_window
        self._logger.get_log() \
            .info(f'params2 {name}')
        self._name = name
        self._logger.get_log() \
            .info(f'params3 {params}')
        self._params = params

    def get_data(self):
        self._logger.get_log() \
            .error(f'TopNToWatchList algorithm!!!!123123')
        count_indicator = ""
        mvID = 0
        topN = 3
        groupByFields = ""
        watchListID = ""

        if self._params is None or len(self._params) == 0:
            self._logger.get_log() \
                .error(f'TopNToWatchList algorithm, param is empty')
            return
        try:
            # {"groupByFields": "scrip", "countIndicator":
            # "1594779557989205718", "mvID": "1594779557989205718", "topN": 3,"watchListID":"12434"}
            self._logger.get_log() \
                .error(f'TopNToWatchList algorithm!!!!123123{self._params}')
            self._logger.get_log() \
                .error(f'TopNToWatchList algorithm!!!!123123{self._params.strip()}')
            param_dict = json.loads(self._params.strip())
            self._logger.get_log() \
                .error(f'TopNToWatchList algorithm!!!!123123{param_dict}')
            if "groupByFields" not in param_dict:
                self._logger.get_log() \
                    .error(f'TopNToWatchList algorithm, param(groupByFields) is empty')
                return
            if "countIndicator" not in param_dict:
                self._logger.get_log() \
                    .error(f'TopNToWatchList algorithm, param(countIndicator) is empty')
                return
            if "mvID" not in param_dict:
                self._logger.get_log() \
                    .error(f'TopNToWatchList algorithm, param(mvID) is empty')
                return
            if "topN" not in param_dict:
                self._logger.get_log() \
                    .error(f'TopNToWatchList algorithm, param(topN) is empty')
                return
            if "watchListID" not in param_dict:
                self._logger.get_log() \
                    .error(f'TopNToWatchList algorithm, param(watchListID) is empty')
                return

            self._logger.get_log().info(f'TopNToWatchList algorithm, param:{param_dict}')
            mvID = param_dict['mvID']
            topN = param_dict['topN']
            count_indicator = param_dict['countIndicator']
            groupByFields = param_dict['groupByFields']
            watchListID = param_dict['watchListID']
        except Exception as e:
            self._logger.get_log().error(f'TopNToWatchList algorithm, load param cause error:{e}')
#{"groupByFields": "csrcip", "countIndicator": "1598369217667746800", "mvID": "1598369217667681264", "topN": 3,"watchListID":"2001"}
        sql = """ 
                   select {groupByFields},{countIndicator}  as c
                   from mat_distributed_{mvID}
                   where  win_start > subtractMinutes(toDateTime('{now_datetime}'), {window_time}) group by {groupByFields} order by c desc limit {topN}
                   """.format(
            groupByFields=groupByFields,
            countIndicator=count_indicator,
            mvID=mvID,
            now_datetime=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            window_time=self._ts_window,
            topN=topN
        )
        self._logger.get_log().info(f'TopNToWatchList algorithm. Executing sql:\n{sql}')

        data = self._ck_handler.get_data_func(sql)

        result = data.to_dict('records')
        if data.empty:
            self._logger.get_log().info(f'TopNToWatchList algorithm, data is empty')
        clear_watchList_kafka = {"intelligenceGroupIds": [watchListID], "action": "clear"}
        self._logger.get_log().info(f"清除观察列表数据{clear_watchList_kafka}")

        kafka_topic = "rule-orion-action-intelligence"
        kafka_security_protocol = 'SASL_PLAINTEXT'
        kafka_sender = KafkaSender(
            bootstrap_server=self._model_config.kafka_host,
            topic=kafka_topic,
            kafka_security_protocol=kafka_security_protocol,
            sasl_mechanism=self._model_config.kafka_mechanism,
            sasl_plain_username=self._model_config.kafka_username,
            sasl_plain_password=self._model_config.kafka_password
        )
        kafka_sender.send(clear_watchList_kafka)
        self._logger.get_log().info(f"成功通过kafka发送{len(clear_watchList_kafka)}topic：{kafka_topic}")

        if result!={}:
            add_watchList_kafka = {"modelName": "行为分析", "intelligenceGroupIds": [watchListID], "modelId": "0",
                                   "action": "add", "events": result}
            self._logger.get_log().info(f"添加观察列表数据{add_watchList_kafka}")
            kafka_sender.send(add_watchList_kafka)
            self._logger.get_log().info(f"成功通过kafka发送{len(add_watchList_kafka)}topic：{kafka_topic}")
        kafka_sender.close()
        return

    def run(self):
        return self.get_data()
