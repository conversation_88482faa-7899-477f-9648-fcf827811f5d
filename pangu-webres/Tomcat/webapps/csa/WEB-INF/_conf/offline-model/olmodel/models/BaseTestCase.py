import unittest
import json
from olmodel.dbhandler.MySqlHandler import MySqlHandler
from olmodel.dbhandler.CkhHandler import CkhHandlerPangu


class BaseTestCase(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(BaseTestCase, self).__init__(*args, **kwargs)
        # Clickhouse API Config
        username = "apiuser"
        apiSecret = "9ca8b79bc7e191e7526db620951bc007966c045b2a80e0e24b9ef055e9a0e01a"
        address = "https://***********:8443/csa"
        self._ck_handler = CkhHandlerPangu(username, apiSecret, address)

        # Mysql init
        host = "127.0.0.1"
        port = 3308
        user = "tsoc"  # tsoc : decrypt_message("ENC(VyBwRDS6wCNxy8+jG1UqDA==")
        password = "ENC(tfRtRIRuy+qWsedzn85D2GDmfnhM+5lENH450cjm0X0=)"  # fake.PWD.fool.4.U, decrypt_message("ENC(tfRtRIRuy+qWsedzn85D2GDmfnhM+5lENH450cjm0X0=)")
        database = "tsoc"

        mysql_handler = MySqlHandler(database, host, port, user, password)
        self._mysql_handler = mysql_handler

    def test_mysql_conn(self):
        self._mysql_handler.get_mysql_conn()

    def test_json_to_dict(self):
        param = "{\"fd\":\"sdfd\"}"
        dic = json.loads(param)

        for key, value in dic.items():
            print(key)
            print(value)


if __name__ == '__main__': unittest.main()
