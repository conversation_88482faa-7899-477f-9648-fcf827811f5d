import unittest
from olmodel.models.BaseTestCase import BaseTestCase
from olmodel.models.LandSpeedViolation import LandSpeedViolation
import olmodel.utils.TimeUtil as TimeUtil
from haversine import haversine


class LandSpeedViolationTestCase(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(LandSpeedViolationTestCase, self).__init__(*args, **kwargs)
        btc = BaseTestCase()
        self._test_case = LandSpeedViolation(btc._ck_handler, None)

    def test_get_login_data(self):
        start_time = TimeUtil.get_now_timestamp() - 60 * 60 * 1000
        self._test_case.get_login_data(start_time, "asc")

    def test_get_login_data_latest(self):
        end_time = TimeUtil.get_now_timestamp()
        start_time = end_time - 24 * 60 * 60 * 1000
        self._test_case.get_login_data_latest(start_time, end_time)


    def test_get_data(self):
        self._test_case.get_data()

    def test_haversine(self):
        csrcgpsOne = (111.5, 36.08)
        csrcgpsTwo = (121.47, 31.23)
        dis = haversine(csrcgpsOne, csrcgpsTwo)
        print(dis)

    def test_land_speed_violation(self):
        csrcgps1 = "120.168903,30.229060"
        csrcgps2 = "120.181176,30.184315"
        loccurtimeOne = 1656838410431
        loccurtimeTwo = 1656898897985
        res = self._test_case.land_speed_violation(csrcgps2, csrcgps1, loccurtimeTwo, loccurtimeOne)
        print(res)


if __name__ == '__main__':
    unittest.main()
