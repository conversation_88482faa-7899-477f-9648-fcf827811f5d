# -*- coding:utf-8 -*-
class AlgorithmParam(object):

    def __init__(self, name, value_type, value):
        self._name = name
        self._value_type = value_type
        self._value = value

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def value_type(self):
        return self._value_type

    @value_type.setter
    def value_type(self, value_type):
        self._value_type = value_type

    @property
    def value(self):
        return self._value

    @value.setter
    def value(self, value):
        self._value = value

    def __repr__(self):
        return f'name:{self.name}, value_type:{self.value_type}, value:{self.value}'
