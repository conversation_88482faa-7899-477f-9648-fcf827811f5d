import datetime
import pandas as pd
import numpy as np
from olmodel.models.constants import HTTP_Suspicous
from olmodel.models.BaseModel import BaseModel
import ipaddress


def is_private_ip(ip):
    """
    判断一个IP地址是否为内网IP地址。

    参数:
        ip (str): 要检查的IP地址。

    返回:
        int: 如果是内网IP地址返回1，否则返回0。
    """
    try:
        ip_obj = ipaddress.ip_address(ip)
        return 1 if ip_obj.is_private else 2
    except ValueError:
        return 0  # 无效的IP地址


# 告警名称：
class HTTPSuspiciousDetection(BaseModel):
    def __init__(self, ck_handler, mysql_handler, model_config, name='httpmodel', ts_window=60,
                 **kwargs):
        super().__init__(ck_handler)
        self._ts_window = ts_window
        self._baseline_time = 14
        self._name = name
        self._time_offset = 0
        self._mysql_handler = mysql_handler
        # get time (format: 220603)
        self.event_table = datetime.datetime.now().strftime('%y%m%d')
        self._now_datetime = (datetime.datetime.now() + datetime.timedelta(minutes=self._time_offset)
                              ).strftime("%Y-%m-%d %H:%M:%S")

    # 每小时运行一次，第3分钟运行
    # HTTP_可疑外联_假冒域名
    def get_data_dst_ip_first(self):
        sql = """
        select csrcip, cdstip, idstport, http_host,dns_main_name, count(1) as n,
        countIf(request_body_len > response_body_len) as n1,
        countIf(request_body_len > 10000) as n2, countIf(response_body_len > 10000) as n3,
        uniq(http_uri_no_parameter) as u1,
        uniq(crequestbody) as u2, uniq(cresponsebody) as u3
        from 
        (
            SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, iappprotocol,  
            http_host, http_uri_no_parameter,  request_body_len, response_body_len, crequestbody, cresponsebody,
            http_host_main_domain_alexa_rank,  http_host_main_domain_rarity,
            dns_main_name, 
            dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
            FROM {event_table}
            WHERE (1 = 1) 
            AND (csrclogtype = '/security/IDS/HTTP') 
            AND (cdstipinner = 2) AND (csrcipinner = 1)
            AND (http_host_main_domain_alexa_rank != '0') 
            and notEmpty(dns_main_name)
            and cdstip not in 
            (
                select dst_ip as cdstip from cache_flow_log_view
                where event_day >= today() - 14 and event_day < today() and dst_ip_inner_ip = 2 AND (src_log_type = '/security/IDS/HTTP') 
                group by dst_ip

            )
            and IPv4NumToStringClassC(IPv4StringToNumOrDefault(cdstip)) not in 
            (
                select IPv4NumToStringClassC(IPv4StringToNumOrDefault(ip)) from 
                (
                    select groupUniqArrayArrayMerge(mat_indic_1022537078354748657) as ips from mat_view_999784571044127150
                    where viewdate >= today() - 14 and notEmpty(dns_main_name) 
                )
                array join ips as ip 
                where notEmpty(ip)
            )

        )
        group by csrcip, cdstip, idstport, http_host,dns_main_name
        having n > 10 
        and n1 >= 5 and n2 > 0 and n3 > 0
        and u1 <= 10
        and u2 >=5 and u3 >= 5		
        """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"-HTTP HOST是知名域名，目的IP首次出现，且不是通过DNS解析出的:\n{sql}")
        df_data = self._ck_handler.get_data_func(sql)
        return df_data

    # HTTP_可疑外联_少见域名
    def get_data_new_domain(self):
        # HTTP HOST是新域名，且alexarank在一百万之外
        sql_new_domain = """
            select csrcip, cdstip, idstport, http_host,dns_main_name, count(1) as n,
            countIf(request_body_len > response_body_len) as n1,
            countIf(request_body_len > 10000) as n2, countIf(response_body_len > 10000) as n3,
            uniq(http_uri_no_parameter) as u1,
            uniq(crequestbody) as u2, uniq(cresponsebody) as u3
            from 
            (
                SELECT toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport,  iappprotocol,  
                http_host, http_uri_no_parameter,  request_body_len, response_body_len, crequestbody, cresponsebody,
                http_host_main_domain_alexa_rank,  http_host_main_domain_rarity,
                dns_main_name, 
                dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
                FROM {event_table}
                WHERE (1 = 1) 
                AND (csrclogtype = '/security/IDS/HTTP') 
                AND (cdstipinner = 2) AND (csrcipinner = 1)
                AND (http_host_main_domain_alexa_rank = '0') 
                and notEmpty(dns_main_name)
                and dns_main_name not in 
                (
                    select dns_main_name as md from mat_view_999784571044127150
                    where viewdate < today() and viewdate >= today() - 14 and notEmpty(dns_main_name) and dns_main_name_alexa_rank = 0
                    group by dns_main_name
                )
            )
            group by csrcip, cdstip, idstport, http_host,dns_main_name
            having n > 10 
            and n1 >= 5 and n2 > 0 and n3 > 0
            and u1 <= 10
            and u2 >=5 and u3 >= 5	
            """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"HTTP HOST是新域名，且alexarank在一百万之外:\n{sql_new_domain}")
        df_data_new_domain = self._ck_handler.get_data_func(sql_new_domain)
        return df_data_new_domain

    # HTTP_可疑外联_少见IP
    def get_data_ip_new(self):
        # HTTP HOST为空，或为IP，目的IP是新的
        sql_ip_new = """
            select csrcip, cdstip, idstport, http_host,dns_main_name, count(1) as n,
            countIf(request_body_len > response_body_len) as n1,
            countIf(request_body_len > 10000) as n2, countIf(response_body_len > 10000) as n3,
            uniq(http_uri_no_parameter) as u1,
            uniq(crequestbody) as u2, uniq(cresponsebody) as u3
            from 
            (
                SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, iappprotocol,  
                http_host, http_uri_no_parameter, request_body_len, 
                response_body_len, crequestbody, cresponsebody,
                http_host_main_domain_alexa_rank, http_host_main_domain_rarity,
                dns_main_name, 
                dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
                FROM {event_table}
                WHERE (1 = 1) 
                AND (csrclogtype = '/security/IDS/HTTP') 
                AND (cdstipinner = 2) AND (csrcipinner = 1)
                and notEmpty(http_host)
                and {match}
                and cdstip not in
                (
                    select dst_ip as cdstip from cache_flow_log_view
                    where event_day >= today() - 14 and event_day < today() and dst_ip_inner_ip = 2 AND (src_log_type = '/security/IDS/HTTP') 
                    group by dst_ip
                )
            )
            group by csrcip, cdstip, idstport, http_host,dns_main_name
            having n > 10 
            and n1 >= 5 and n2 > 0 and n3 > 0
            and u1 <= 10
            and u2 >=5 and u3 >= 5
            """.format(
            event_table="event20" + self.event_table,
            match='match(http_host, \'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\')',
        )
        self._logger.get_log().info(f"HTTP HOST为空，或为IP，目的IP是新的:\n{sql_ip_new}")
        df_data_ip_new = self._ck_handler.get_data_func(sql_ip_new)
        return df_data_ip_new

    def run(self):
        """
        注意事项：
            输出字段必须是event表中已有的字段
            HTTP_可疑外联模型通过constant.py中设置的HTTP_Suspicous获取
        """
        # csrcip, cdstip, idstport, http_host, dns_main_name, n, n1, n2, n3, u1, u2, u3
        df_data_new_domain = self.get_data_new_domain()
        df_data_new_domain['ceventdigest'] = 'HTTP_可疑外联_少见域名'
        # csrcip, cdstip, idstport, http_host, dns_main_name, n, n1, n2, n3, u1, u2, u3
        df_data_ip_new = self.get_data_ip_new()
        df_data_ip_new['ceventdigest'] = 'HTTP_可疑外联_少见IP'
        df_data_dst_ip_first = self.get_data_dst_ip_first()
        df_data_dst_ip_first['ceventdigest'] = 'HTTP_可疑外联_假冒域名'
        offline_df = pd.concat([df_data_new_domain, df_data_ip_new, df_data_dst_ip_first])
        if len(offline_df) == 0:
            return []
        offline_df['csrcipinner'] = offline_df['csrcip'].apply(lambda x: is_private_ip(x))
        offline_df['cdstipinner'] = offline_df['cdstip'].apply(lambda x: is_private_ip(x))
        offline_df = offline_df[HTTP_Suspicous]
        offline_df['idstport'] = offline_df['idstport'].astype(int)
        offline_result = offline_df.to_dict('records')
        return offline_result
