import re
import math
import pandas as pd
from collections import Counter
from pyod.models.copod import COPOD
from .base import BaseDetector


class StringDetector(BaseDetector):

    def __init__(self):
        self.data = None
        self.model = None

    def fit(self, data):
        self.data = data

        # 参数值少于20个不适合拟合基线
        if data.shape[0] < 20:
            return self

        # 抽取特征
        feature = self.compute_feature(data)
        # 构建异常检测算法
        self.model = COPOD(contamination=0.0001).fit(feature)

        return self

    def predict(self, data):
        if self.model is None:
            return 0  # 对照过少没有训练

        f = self.compute_feature(data)
        r = self.model.predict_proba(f)
        s = r[0][1]
        return s

    def compute_feature(self, data):
        """baseline_v中可能有None"""
        if isinstance(data, str) or isinstance(data, int) or isinstance(data, float):
            data = pd.Series(data=data)

        data = data.astype(str)

        # 是否包含数字
        f1 = data.map(lambda x: 1 if bool(re.search(r'\d', x)) else 0).values

        # 是否包含字母
        f2 = data.map(lambda x: 1 if bool(re.search(r'[a-zA-Z]', x)) else 0).values

        # 长度
        f3 = data.map(lambda x: len(x)).values

        # 熵值
        f4 = data.map(lambda x: self.compute_entropy(x)).values

        # 是否包含某些关键字
        f5 = data.map(lambda x: 1 if re.search("http|git|push|select", x) else 0).values

        # 纯小写字母
        f7 = data.map(lambda x: 1 if bool(re.match('^[a-z]+$', x)) else 0).values

        # 纯大写字母
        f8 = data.map(lambda x: 1 if bool(re.match('^[A-Z]+$', x)) else 0).values

        # 纯字母
        f9 = data.map(lambda x: 1 if bool(re.match('^[a-zA-Z]+$', x)) else 0).values

        # 数字+字母
        f10 = data.map(lambda x: 1 if bool(re.match('^[a-zA-Z0-9]+$', x)) else 0).values

        # base64
        f11 = data.map(lambda x: 1 if self.is_base64_code(x) else 0).values

        # 特殊字符
        f12 = data.map(lambda x: 1 if bool(re.findall('[@|{|}|-]', x)) else 0).values

        f = pd.DataFrame({
            "f1": f1, "f2": f2, "f3": f3, "f4": f4, "f5": f5, "f7": f7,
            "f8": f8, "f9": f9, "f10": f10, "f11": f11, "f12": f12
        })

        return f

    @staticmethod
    def compute_entropy(text):
        elements, length = Counter(text), len(text)
        return -sum(element / length * math.log(element / length, 2) for element in elements.values())

    @staticmethod
    def is_base64_code(s):
        """Check s is Base64.b64encode"""
        if not isinstance(s, str) or not s:
            return "params s not string or None"

        _base64_code = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I',
                        'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
                        'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a',
                        'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
                        'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's',
                        't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1',
                        '2', '3', '4', '5', '6', '7', '8', '9', '+',
                        '/', '=']
        _base64_code_set = set(_base64_code)  # 转为set增加in判断时候的效率
        # Check base64 OR codeCheck % 4
        code_fail = [i for i in s if i not in _base64_code_set]
        if code_fail or len(s) % 4 != 0:
            return False
        return True
