# -*- coding:utf-8 -*-
class ModelConfig(object):

    def __init__(self, log_level=None, time_offset=None, pangu_api_address=None, api_username=None, api_secret=None,
                 anomaly_beh_topic=None, log_path=None,
                 mysql_database=None, mysql_host=None, mysql_port=None, mysql_user=None, mysql_password=None,
                 kafka_host=None, kafka_mechanism=None, kafka_username=None, kafka_password=None
                 ):
        self._log_level = log_level
        self._time_offset = time_offset
        self._pangu_api_address = pangu_api_address
        self._api_username = api_username
        self._api_secret = api_secret
        self._anomaly_beh_topic = anomaly_beh_topic
        self._log_path = log_path
        self._mysql_database = mysql_database
        self._mysql_host = mysql_host
        self._mysql_port = mysql_port
        self._mysql_user = mysql_user
        self._mysql_password = mysql_password
        self._kafka_host = kafka_host
        self._kafka_mechanism = kafka_mechanism
        self._kafka_username = kafka_username
        self._kafka_password = kafka_password

    @property
    def log_level(self):
        return self._log_level

    @log_level.setter
    def log_level(self, log_level):
        if not log_level:
            raise ValueError('log_level不能为空')
        log_level = log_level.lower()
        if log_level not in {'debug', 'info', 'error', 'warning'}:
            raise ValueError(f'log_level的取值只能是[debug, info, error, warning], 然而log_level={log_level}')
        self._log_level = log_level

    @property
    def time_offset(self):
        return self._time_offset

    @time_offset.setter
    def time_offset(self, time_offset):
        self._time_offset = time_offset

    @property
    def pangu_api_address(self):
        return self._pangu_api_address

    @pangu_api_address.setter
    def pangu_api_address(self, pangu_api_address):
        self._pangu_api_address = pangu_api_address

    @property
    def api_username(self):
        return self._api_username

    @api_username.setter
    def api_username(self, api_username):
        self._api_username = api_username

    @property
    def api_secret(self):
        return self._api_secret

    @api_secret.setter
    def api_secret(self, api_secret):
        self._api_secret = api_secret

    @property
    def anomaly_beh_topic(self):
        return self._anomaly_beh_topic

    @anomaly_beh_topic.setter
    def anomaly_beh_topic(self, anomaly_beh_topic):
        self._anomaly_beh_topic = anomaly_beh_topic

    @property
    def log_path(self):
        return self._log_path

    @log_path.setter
    def log_path(self, log_path):
        self._log_path = log_path

    @property
    def mysql_database(self):
        return self._mysql_database

    @mysql_database.setter
    def mysql_database(self, mysql_database):
        self._mysql_database = mysql_database

    @property
    def mysql_host(self):
        return self._mysql_host

    @mysql_host.setter
    def mysql_host(self, mysql_host):
        self._mysql_host = mysql_host

    @property
    def mysql_port(self):
        return self._mysql_port

    @mysql_port.setter
    def mysql_port(self, mysql_port):
        self._mysql_port = mysql_port

    @property
    def mysql_user(self):
        return self._mysql_user

    @mysql_user.setter
    def mysql_user(self, mysql_user):
        self._mysql_user = mysql_user

    @property
    def mysql_password(self):
        return self._mysql_password

    @mysql_password.setter
    def mysql_password(self, mysql_password):
        self._mysql_password = mysql_password

    @property
    def kafka_host(self):
        return self._kafka_host

    @kafka_host.setter
    def kafka_host(self, kafka_host):
        self._kafka_host = kafka_host

    @property
    def kafka_mechanism(self):
        return self._kafka_mechanism

    @kafka_mechanism.setter
    def kafka_mechanism(self, kafka_mechanism):
        self._kafka_mechanism = kafka_mechanism

    @property
    def kafka_username(self):
        return self._kafka_username

    @kafka_username.setter
    def kafka_username(self, kafka_username):
        self._kafka_username = kafka_username

    @property
    def kafka_password(self):
        return self._kafka_password

    @kafka_password.setter
    def kafka_password(self, kafka_password):
        self._kafka_password = kafka_password

    def __str__(self):
        return f'log_level:{self.log_level}, time_offset: {self.time_offset}'
