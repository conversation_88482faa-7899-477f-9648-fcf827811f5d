# -*- coding:utf-8 -*-
import json
import os
from olmodel.models.BaseModel import BaseModel
import olmodel.utils.TimeUtil as TimeUtil
from olmodel.utils.FileUtil import FileUtil

acc_login_info_file_name = "user-account-login-info.txt"
SPLIT_CHAR = "-"


# 沉默账号登录，休眠账号登录---（离线）
class SilentAccountLogin(BaseModel):
    def __init__(self, ck_handler, model_config, ts_window=7, name="沉默账号登录", params='{"silentDays":30}', **kwargs):
        super().__init__(ck_handler)
        self._model_config = model_config
        self._ts_window = ts_window
        self._name = name
        self._params = params
        self._file_util = FileUtil()

    def get_data(self):

        # 检查过去一小时各账号的登录，是否是沉默一段时间后登录
        login_data_df = self.get_login_data()  # 需要有序

        if login_data_df.empty:
            return []

        earliest_login = dict()  # 登录时间最早记录
        latest_login_time = dict()  # 登录最大时间
        # pre_login_time = dict()  # 前一次登录时间
        for index, row in login_data_df.iterrows():
            cuseraccountname = row['cuseraccountname']
            cuseraccounttype = row['cuseraccounttype']
            if cuseraccountname is None or len(cuseraccountname) == 0 \
                    or cuseraccounttype is None or len(cuseraccounttype) == 0:
                continue
            try:
                loccurtimeNow = int(row['loccurtime'])
            except Exception as e:
                continue

            key = cuseraccountname + SPLIT_CHAR + cuseraccounttype
            #
            # if key in pre_login_time:
            #     if abs(loccurtimeNow - pre_login_time[key]) >= silent_time:
            #         offline_result.append(row.to_dict())
            #
            # pre_login_time[key] = loccurtimeNow

            if key in earliest_login:
                earliest_login_time = earliest_login[key]['loccurtime']
                if loccurtimeNow < earliest_login_time:
                    earliest_login[key] = row
            else:
                earliest_login[key] = row

            if key in latest_login_time:
                if loccurtimeNow > latest_login_time[key]:
                    latest_login_time[key] = loccurtimeNow
            else:
                latest_login_time[key] = loccurtimeNow

        offline_result = []
        silent_time = self.get_silent_time()
        # 此次检测登录最小时间与上一次检测登录最大时间相比
        acc_last_login_info = self.load_acc_latest_login_info()

        for key in earliest_login:
            if key in acc_last_login_info:
                loccurtimeNow = earliest_login[key]['loccurtime']
                last_login_info = acc_last_login_info[key]
                try:
                    last_time = int(last_login_info[2])
                except Exception as e:
                    continue
                if loccurtimeNow > last_time and loccurtimeNow - last_time >= silent_time:
                    offline_result.append(earliest_login[key].to_dict())

        # 更新账号的最大的登录时间, 并保存
        run_time = TimeUtil.get_now_timestamp()
        for key in latest_login_time:
            if key in acc_last_login_info:
                if latest_login_time[key] > int(acc_last_login_info[key][2]):
                    acc_last_login_info[key][2] = str(latest_login_time[key])
                    acc_last_login_info[key][3] = str(run_time)
            else:
                acc_name_type = key.split(SPLIT_CHAR)
                acc_last_login_info[key] = \
                    list([acc_name_type[0], acc_name_type[1], str(latest_login_time[key]), str(run_time)])

        # save acc latest login time info
        acc_latest_login_info_list = list()
        for key in acc_last_login_info:
            lst = acc_last_login_info[key]
            acc_latest_login_info_list.append(",".join(lst))

        if acc_latest_login_info_list:
            self._file_util.write(os.getcwd(), acc_login_info_file_name, "\n".join(acc_latest_login_info_list))

        return offline_result

    def run(self):
        return self.get_data()

    def load_acc_latest_login_info(self):

        file_dir = os.getcwd()
        file_path = os.path.join(file_dir, acc_login_info_file_name)

        lines = self._file_util.read_lines(file_path)

        if lines is None or len(lines) == 0:
            return dict()

        acc_latest_login_info = dict()
        for line in lines:
            if not line.strip():
                continue

            parts = line.split(",")
            if not len(parts) >= 4:
                continue

            cuseraccountname = parts[0].strip()
            cuseraccounttype = parts[1].strip()

            if cuseraccountname is None or len(cuseraccountname) == 0 \
                    or cuseraccounttype is None or len(cuseraccounttype) == 0:
                continue
            try:
                loccurtime = int(parts[2])
            except Exception as e:
                continue

            key = cuseraccountname + SPLIT_CHAR + cuseraccounttype
            acc_latest_login_info[key] = parts

        return acc_latest_login_info

    def get_login_data(self):
        ts_window = TimeUtil.get_now_timestamp() - 60 * 60 * 1000

        sql = """ 
            select cuseraccountname, cuseraccounttype, loccurtime, cuserid, csrcip, cdstip, '/login' as coperation, '1007885906163541233' as behaviorid
            from mat_distributed_1007885906163541233
            where loccurtime >= {ts_window}
                order by loccurtime desc
            """.format(
            ts_window=ts_window
        )
        self._logger.get_log().info(f'SilentAccountLogin algorithm. Executing sql:\n{sql}')

        df = self._ck_handler.get_data_func(sql)

        if df.empty:
            self._logger.get_log().info(f'SilentAccountLogin algorithm, login data is empty')

        return df

    def get_silent_time(self):
        # 默认沉默时间60天
        default_silent_days = 60
        default_silent_time = default_silent_days * 24 * 60 * 60 * 1000
        if self._params is None or len(self._params) == 0:
            self._logger.get_log() \
                .info(f'SilentAccountLogin algorithm, param is empty, use default silent days:{default_silent_days}')
            return default_silent_time
        try:
            param_dict = json.loads(self._params.strip())
        except Exception as e:
            self._logger.get_log().error(f'SilentAccountLogin algorithm, load param cause error:{e}')
            return default_silent_time

        self._logger.get_log().info(f'SilentAccountLogin algorithm, param:{param_dict}')

        if "silentDays" not in param_dict:
            return default_silent_time

        silent_days = param_dict['silentDays']
        if silent_days is None or len(str(silent_days).strip()) == 0:
            return default_silent_time

        try:
            silent_days_num = int(silent_days)
            if silent_days_num <= 0:
                self._logger.get_log().info(
                    f'SilentAccountLogin algorithm, param is invalid, use default silent days:{default_silent_days}')
                return default_silent_time
            return silent_days_num * 24 * 60 * 60 * 1000
        except ValueError:
            return default_silent_time

        return default_silent_time
