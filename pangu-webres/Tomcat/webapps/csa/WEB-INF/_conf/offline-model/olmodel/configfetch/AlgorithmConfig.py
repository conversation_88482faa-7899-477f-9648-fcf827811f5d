# -*- coding:utf-8 -*-
from olmodel.configfetch.AlgorithmParam import AlgorithmParam


class AlgorithmConfig(object):

    def __init__(self, algorithm, param_list):
        self._algorithm = algorithm
        self._algorithm_params = {}  # AlgorithmParam对象的实例集合
        for param in param_list:
            algparam = AlgorithmParam(param['name'], param['valueType'], param['value'])
            self._algorithm_params[param['name']] = algparam

    @property
    def algorithm(self):
        return self._algorithm

    @algorithm.setter
    def algorithm(self, algorithm):
        self._algorithm = algorithm

    @property
    def algorithm_params(self):
        return self._algorithm_params

    @algorithm_params.setter
    def algorithm_params(self, param_list):
        for param in param_list:
            algparam = AlgorithmParam(param['name'], param['valueType'], param['value'])
            self._algorithm_params[param['name']] = algparam

    def __str__(self):
        res = f'algorithm:{self.algorithm}, '
        for param in self.algorithm_params.values():
            res += param.__repr__()
            res += ' '
        return res
