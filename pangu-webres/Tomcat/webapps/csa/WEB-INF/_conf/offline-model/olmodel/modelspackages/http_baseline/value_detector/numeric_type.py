import numpy as np
from .base import BaseDetector
from .type_detector import is_numeric
from ..base_function import modify_sigmoid
from pyod.models.mad import MAD


class NumericDetector(BaseDetector):

    def __init__(self):

        self.values_counts = None
        self.model = None

    def fit(self, data, values_counts):
        if values_counts.shape[0] < 5:
            self.values_counts = values_counts
        else:
            data = data[data.map(lambda x: is_numeric(x))]
            data = data.map(lambda x: self.to_numeric(x)).values.reshape(-1, 1)
            self.model = MAD(contamination=0.01).fit(data)

        return self

    def predict(self, data):
        if is_numeric(data):
            if self.values_counts is not None:
                if data not in self.values_counts.index:
                    return 0.7
                else:
                    s = modify_sigmoid(self.values_counts[data], alpha=0.7)
                    return s
            else:
                n = self.to_numeric(data)  # 输入可能为字符串
                n = np.array([[n]])
                r = self.model.predict_proba(n)
                s = r[0][1]
                return s
        else:
            return 1

    @staticmethod
    def to_numeric(x):
        return float(x)
