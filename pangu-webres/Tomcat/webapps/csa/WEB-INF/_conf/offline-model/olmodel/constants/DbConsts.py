"""
Database Consts (Click House)
"""
# 盘古数据库目前部分的数值类型的字段
isNotNull_field = {'isrcport', 'idstport', 'iappprotocol'}

# 盘古数据库目前全部的string类型的字段
notEmpty_field = {
 'csrcip',
 'cdstip',
 'ceventname',
 'ceventtype',
 'coperation',
 'cobject',
 'csrcmac',
 'csrcname',
 'csrcregion',
 'csrclocation',
 'csrcgps',
 'cdstmac',
 'cdstname',
 'cdstregion',
 'cdstlocation',
 'cdstgps',
 'ceventdigest',
 'crequestmsg',
 'cuseraccountname',
 'cprogram',
 'ceventmsg',
 'ccollectorip',
 'cdevname',
 'cdevip',
 'cdevtype',
 'csrclogtype',
 'manufacturer',
 'devarea',
 'devlocation',
 'devgps',
 'cdomainsrcip',
 'cdomaindstip',
 'cdomaindevip',
 'cdomaincollectorip',
 'des_tenant',
 'dev_tenant',
 'dev_tenantsource',
 'cuseraccounttype',
 'cusername',
 'cuserid',
 'sensor_sn',
 'rec_id',
 'session_id',
 'ruleset_name',
 'rule_name',
 'policy_id',
 'comm_direction',
 'command_line',
 'db_name',
 'http_url',
 'method',
 'http_referer',
 'user_agent',
 'module_name',
 'attck_method',
 'uuid_client',
 'client_version',
 'policy_name',
 'cflowdir',
 'proc_name',
 'image_path',
 'injected_md5',
 'signature_name',
 'client_ip',
 'instance_name',
 'listen_port',
 'logtype_tag',
 'resource_account',
 'reviewer',
 'operation_content',
 'client_os',
 'client_software',
 'internet_account',
 'csendip',
 'creciveip',
 'mail_title',
 'cfilename',
 'cpu_core_count',
 'cpu_model',
 'disk_model',
 'disk_size',
 'os_version',
 'physical_memory',
 'memory_speed',
 'resolution_list',
 'soft_company',
 'software_product',
 'memory_slots',
 'adapter_list',
 'adapter_name',
 'asset_ip',
 'adapter_mac',
 'tunnel_info',
 'outer_name',
 'inner_name',
 'cert_local',
 'cert_remote',
 'virus_resp',
 'av_scan_start',
 'server_host',
 'command_code',
 'table_name',
 'payload',
 'http_host',
 'cookie',
 'crequesthead',
 'crequestbody',
 'cresponsehead',
 'cresponsebody',
 'virus_file_name',
 'input_if',
 'output_if',
 'file_name_pre',
 'reason',
 'user_group_name',
 'term_device',
 'action_name',
 'cpu_usage',
 'mem_usage',
 'disk_usage',
 'domain_name',
 'csrctip',
 'cdsttip',
 'proc_create_time',
 'peripheral_class',
 'peripheral_compatible_id',
 'peripheral_desc',
 'peripheral_friendly_name',
 'peripheral_hardware_id',
 'peripheral_instance_id',
 'peripheral_provider',
 'peripheral_service',
 'blasted_user',
 'remote_ip',
 'file_ext',
 'virus_name',
 'dial_in_ip',
 'login_user',
 'request_mime_types',
 'file_uuid',
 'secret_keyword',
 'server_ip',
 'leakage_method',
 'attack_chain_tag',
 'detail',
 'exhaustion_type',
 'failed_username',
 'successed_username',
 'attacked_ip',
 'service_name',
 'service_desc',
 'service_path',
 'nat_type',
 'csrctip_before',
 'message',
 'server_version',
 'real_ip',
 'request_url_version',
 'log_tag',
 'load_dlls',
 'parent_proc_name',
 'proc_desc',
 'run_user',
 'imphash',
 'user_full_name',
 'server_vip',
 'pool_ip',
 'virtual_link',
 'account_path',
 'software_version',
 'client_hostname',
 'conn_method',
 'client_user',
 'object_name',
 'sub_command',
 'monitor_version',
 'monitor_type',
 'monitor_ip',
 'currvaule',
 'threshold',
 'monitor_event_type',
 'topo_start_port',
 'topo_end_port',
 'attack_ip',
 'categories',
 'category',
 'eviltag',
 'evil_code_type',
 'families',
 'file_direct',
 'file_type',
 'http_host_main_domain_alexa_rank',
 'http_host_main_domain_rarity',
 'http_tags',
 'http_uri_no_parameter',
 'http_xff',
 'ioc',
 'native_type',
 'organizations',
 'password',
 'request_content_type',
 'request_cfilenames',
 'request_file_uids',
 'response_content_type',
 'response_cfilenames',
 'response_file_uids',
 'response_mime_types',
 'suspicious_addr',
 'suspicious_domain',
 'suspicious_url',
 'open_port_list',
 'resource_name',
 'out_link_list',
 'hotfix_list',
 'hotfix_name',
 'hotfix_msbullentin',
 'share_path',
 'share_printer',
 'share_ipc',
 'screen_saver_status',
 'saver_relogin',
 'illegal_port',
 'passwd_complexity',
 'weak_user',
 'software_list',
 'file_path',
 'cmd5',
 'host_mask',
 'win_user',
 'tx_user',
 'mail_body',
 'attachment_filenames',
 'file_path_pre',
 'copy_path',
 'group_name_tag',
 'printer_name',
 'watermark_tag',
 'prev_src_mac',
 'virus_path',
 'virus_md5',
 'che_result_static',
 'command_parameter',
 'is_tls',
 'is_webmail',
 'linputflag',
 'mail_bcc',
 'cccaddress',
 'mail_from_domain_name',
 'mail_header',
 'mail_last_reply',
 'mail_status',
 'slave_ip',
 'vul_code',
 'apptype',
 'vul_name',
 'vul_type',
 'attack_success',
 'raw_id',
 'attck_code',
 'coin_type',
 'file_hash',
 'file_hash256',
 'file_md5',
 'virus_type',
 'parent_process_path',
 'file_create_time',
 'cevi_dence',
 'snmp_community',
 'client_mac',
 'data_symbol',
 'algorithm_name',
 'scene_id',
 'scene_name',
 'result_label',
 'predict_msg',
 'native_file_system',
 'pipe_name',
 'server_name',
 'smb_resource_type',
 'smb_share_name',
 'smb_share_type',
 'smb_tree_path',
 'interface_name',
 'cfiletype',
 'file_owner',
 'right_change',
 'file_user',
 'cipher_alg',
 'computer_name',
 'connection',
 'message_content',
 'web_name',
 'tamper_type',
 'server_os',
 'server_os_distrib',
 'server_os_version',
 'server_os_architecture',
 'password_desc',
 'response_domain',
 'weak_asset_host',
 'autorun_name',
 'autorun_type',
 'software_desc',
 'software_install_date',
 'software_path',
 'comflicting_ip',
 'module_desc',
 'remote_account',
 'local_vpn_ip',
 'peer_vpn_ip',
 'dhcp_trans_id',
 'ip_assigned',
 'http_keyword',
 'data_content',
 'engine_version',
 'cdstdevtype',
 'cdstmask',
 'cnexthop',
 'csrcdevtype',
 'csrcmask',
 'ctitle',
 'tunnel_parents',
 'raw_cmd',
 'mount_point',
 'operation_guid',
 'resource_class',
 'sequence_id',
 'av_policy',
 'ib_policy',
 'ips_policy',
 'addr_name',
 'public_addr',
 'pc_policy',
 'service_object',
 'time_control',
 'compression_alg',
 'host_key',
 'host_key_alg',
 'server_software',
 'ssh_kex_alg',
 'ssh_mac_alg',
 'ssh_version',
 'send_im_user',
 'recive_im_user',
 'chatacter',
 'dns_ip',
 'cert_id',
 'cert_serial',
 'cert_version',
 'cipher_suite',
 'client_ssl_issuer',
 'client_ssl_subject',
 'next_protocol',
 'ssl_curve',
 'ssl_issuer',
 'ssl_ja3',
 'ssl_ja3s',
 'ssl_last_alert',
 'ssl_subject',
 'ssl_version',
 'domain_cname',
 'remote_ip_array',
 'url_cate_name',
 'route_table',
 'user_pre_name',
 'user_pre_full_name',
 'cert_count',
 'cert_permanent',
 'cert_type',
 'client_dig_product_id',
 'client_language',
 'encryption_level',
 'encryption_method',
 'requested_color_depth',
 'security_protocol',
 'channel_dst_ip',
 'channel_src_ip',
 'mime_type',
 'reply_code',
 'trans_mode',
 'out_links',
 'src_table',
 'dst_table',
 'sql_command',
 'server_vport',
 'reverse_domain_name',
 'command_line_type',
 'port_nums',
 'http_title',
 'endpoint_name',
 'operation_name',
 'sender',
 'reg_item',
 'reg_key',
 'reg_value',
 'last_attack_time',
 'src_fonts',
 'src_render',
 'src_has_unity',
 'src_language',
 'src_timezone',
 'src_support',
 'src_cpu_concurrency',
 'src_screen_resolution',
 'check_virus_method',
 'file_origin',
 'finder',
 'login_time',
 'logout_time',
 'session_count',
 'login_id',
 'workstation_name',
 'browser_version',
 'key_length',
 'account_domain',
 'monitor_item_name',
 'monitor_item_brief',
 'dns_additional',
 'dns_authoritative_servers',
 'dns_main_name',
 'dns_main_name_type',
 'dns_qry_class_name',
 'dns_qry_name',
 'dns_qry_type_name',
 'dns_response_answers',
 'dns_response_ip_address',
 'dns_response_name',
 'dns_response_ttls',
 'csrcprovincename',
 'csrccityname',
 'cdstprovincename',
 'cdstcityname',
 'devprovincename',
 'devcityname',
 'dst_ip_pDns_name',
 'dst_ip_pDns_main_name',
 'dst_ip_pDns_main_name_alexa_rank',
 'keyboard_type',
 'keyboard_layout',
 'system_info',
 'ip_info',
 'parse_name'
}
