offline-model:
  - algorithm-type: offline  # offline
    configs:
      - algorithm: firstAppearance
        algorithmCn: 首次出现
        description: 首次出现
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: baseline_time
            description: 基线时长（天）
            valueType: int
            defaultValue: 14
            value: 14
            valueIndex: 2
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 3
      - algorithm: mimic
        algorithmCn: 模仿
        description: 模仿
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: target
            description: 被模仿的字符串
            valueType: string
            defaultValue: Google,Twitter,Github
            value: Google,Twitter,Github
            valueIndex: 2
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 3
      - algorithm: sensitiveInformation
        algorithmCn: 敏感信息
        description: 敏感信息
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: target
            description: 敏感内容
            valueType: string
            defaultValue: xxx
            value: xxx
            valueIndex: 2
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 3
      - algorithm: randomString
        algorithmCn: 随机字符串
        description: 随机字符串
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: name
            description: 名称（自定义：dns_qry_name 或 http_host 或 server_name）
            valueType: string
            defaultValue: dns_qry_name
            value: dns_qry_name
            valueIndex: 2
          - name: level
            description: 域名等级
            valueType: string
            defaultValue: 二级域名
            value: 二级域名
            valueIndex: 3
      - algorithm: content
        algorithmCn: 内容
        description: 内容
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 2
      - algorithm: recommendation
        algorithmCn: 推荐
        description: 推荐
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 1440
            value: 1440
            valueIndex: 1
          - name: object_type
            description: 选择推荐的对象（ip或user）
            valueType: string
            defaultValue: ip
            value: ip
            valueIndex: 2
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: 风险智能推荐
            value: 风险智能推荐
            valueIndex: 3
      - algorithm: outlier
        algorithmCn: 离群点
        description: 离群点
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 2
      - algorithm: accumulation
        algorithmCn: 累积
        description: 累积
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: thresh
            description: 阈值
            valueType: float
            defaultValue: 0.90
            value: 0.90
            valueIndex: 2
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 3
      - algorithm: beacon
        algorithmCn: 心跳式访问
        description: 心跳式访问
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: type
            description: 类型（TCP/UDP/DNS/HTTP/HTTPs...）
            valueType: string
            defaultValue: HTTP
            value: HTTP
            valueIndex: 2
      - algorithm: IPTag
        algorithmCn: IP标签模型
        description: IP标签模型
        params:
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 1
      - algorithm: SSLSuspiciousDetection
        algorithmCn: SSL_可疑加密外联
        description: SSL_可疑加密外联
        params:
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 1
      - algorithm: HTTPSuspiciousDetection
        algorithmCn: HTTP_可疑外联
        description: HTTP_可疑外联
        params:
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 1
      - algorithm: SessionBaselineModel
        algorithmCn: 会话基线模型
        description: 会话基线模型
        params:
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 1
      - algorithm: WebAppSuspiciousDetection
        algorithmCn: HTTP_访问企业web应用的可疑流量
        description: WebAppSuspiciousDetection
        params:
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 1
      - algorithm: OperationalBaseline
        algorithmCn: 业务基线
        description: OperationalBaseline
        params:
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 1
      - algorithm: others
        algorithmCn: 其他
        description: 其他
        params:
          - name: ts_window
            description: 时间窗口大小（分钟）
            valueType: int
            defaultValue: 60
            value: 60
            valueIndex: 1
          - name: name
            description: 名称（自定义）
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 2
          - name: params
            description: 自定义参数(json格式)
            valueType: string
            defaultValue: Any
            value: Any
            valueIndex: 3
