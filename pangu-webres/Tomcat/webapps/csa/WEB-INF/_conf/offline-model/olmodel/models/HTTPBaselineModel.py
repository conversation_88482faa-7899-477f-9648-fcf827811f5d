import os
import site
import json
import joblib
import datetime
import pandas as pd
from olmodel.models.BaseModel import BaseModel


class HTTPBaselineModel(BaseModel):

    def __init__(self, ck_handler, model_config, name="Any", **kwargs):
        super().__init__(ck_handler)
        self._model_config = model_config
        self._name = name
        installation_directory = os.popen('syscmd --path').read().strip().lstrip("install path : ")
        self.store_dir = os.path.join(
            installation_directory, "webserver", "webapps", "xdr", "WEB-INF", "_conf", "alert-verify-model",
            "aimodel", "model-lib", "http_baseline_model"
        )

        self.event_table_1 = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime('%y%m%d')
        self.event_table_2 = (datetime.datetime.now() + datetime.timedelta(days=-2)).strftime('%y%m%d')

        # 配置离线模型自制的库
        ol_model_packages = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "modelspackages"
        )
        site.addsitedir(ol_model_packages)

        http_baseline = __import__("http_baseline")
        self.model_map = {
            "http_url": http_baseline.URLAnomaly,
            "crequesthead": http_baseline.RequestHead,
            "crequestbody": http_baseline.RequestBody,
            "cresponsehead": http_baseline.ResponseHead,
            "cresponsebody": http_baseline.ResponseBody
        }

    def get_host_port(self, ck=True):
        """获取需要建模的host-port"""
        # 筛选满足http日志数量的host
        if ck:
            sql_host = """
                select http_host, idstport, count() as c
                from {event_table_1}
                where csrclogtype = '/security/IDS/HTTP' and notEmpty(http_host)
                group by http_host, idstport 
                having c > 300
            """.format(
                event_table_1="event20" + self.event_table_1,
            )
            data_host = self._ck_handler.get_data_func(sql_host)
        else:
            data_host = pd.DataFrame({
                "http_host": [],
                "idstport": []
            })
        return data_host

    def run(self):
        # 分字段构建基线
        fields = ["http_url", "crequesthead", "crequestbody", "cresponsehead", "cresponsebody"]

        data_host = self.get_host_port(ck=True)

        for index, row in data_host.iterrows():
            http_host = row["http_host"]
            idstport = row["idstport"]

            self._logger.get_log().info(f'开始拟合{http_host}:{idstport}基线')
            # 基线存储
            host_store_dir = os.path.join(self.store_dir, f"{http_host}_{idstport}")
            if not os.path.exists(host_store_dir):
                os.makedirs(host_store_dir)
            try:
                # 查询近两天
                data = self.get_baseline_data(
                    http_host, idstport, ck=True, file_path=None
                )

                for field in fields:
                    self._logger.get_log().info(f"拟合{http_host}:{idstport}的{field}基线")
                    model = self.model_map[field]
                    model_fitted = model(http_host=http_host, idstport=idstport).fit(data, field)

                    # body的基线需要url的path
                    if field == "http_url":
                        data.insert(loc=0, column="http_url_path", value=model_fitted.baseline_http_url_path)

                    joblib.dump(model_fitted, os.path.join(host_store_dir, f"{field}.model"))
                    self._logger.get_log().info(f"{http_host}:{idstport}的{field}基线已拟合并保存。")
            except Exception as e:
                self._logger.get_log().info(f"{http_host}:{idstport}的基线由于{e}原因，没有拟合成功，请关注。")

        return []

    def get_baseline_data(self, http_host, idstport, ck=True, file_path=None):
        """根据host和目的port来查询基线数据，查询csrcip, cdstip, idstport, http_host, http_url, crequesthead, crequestbody,
        cresponsehead, cresponsebody
        """
        if ck:
            sql = """
            select csrcip, cdstip, idstport, http_host, http_url, crequesthead, 
                crequestbody, cresponsehead, cresponsebody
            from {event_table_1}
            where csrclogtype = '/security/IDS/HTTP'
            and http_host = '{http_host}' and idstport = {idstport}
            limit 30000
            union all
            select csrcip, cdstip, idstport, http_host, http_url, crequesthead,
                crequestbody, cresponsehead, cresponsebody
            from {event_table_2}
            where csrclogtype = '/security/IDS/HTTP'
            and http_host = '{http_host}' and idstport = {idstport}
            limit 20000
            """.format(
                event_table_1="event20" + self.event_table_1,
                event_table_2="event20" + self.event_table_2,
                http_host=http_host,
                idstport=idstport
            )
            self._logger.get_log().info(f"执行查询{http_host}:{idstport}的基线数据，SQL语句：\n{sql}")
            data = self._ck_handler.get_data_func(sql)
        else:
            # 查询本地数据
            with open(file_path, 'r', encoding='utf-8') as f:
                data = f.readlines()[0]
                data = pd.DataFrame(json.loads(data))
                f.close()

        return data

    def predict(self, http_host, idstport, data, field):
        """
        HTTP基线模型预测API
        Parameters
        ----------
        http_host: 基线的host
        idstport: 基线的port
        data: 需要验证的数据
              1. 预测url是否是异常
                 data: 待检测的url
                 field: 字段名 http_url

              2. 检测请求头和响应头是否是异常的
                 data: List [[{"name": "Host", "value": "xxx"}, {"name": "User-Agent", "value": "xxx"}, ...], ...]
                 field: 字段名 crequesthead or cresponsehead

              2. 检测请求体和响应体是否是异常的，默认base64编码。
                 dict
                    请求头 -> {'http_url': '', 'crequestbody': ['b1', 'b2']}
                    响应头 -> {'http_url': '', 'cresponsebody' ['b1', 'b2']}
                 field: 字段名 crequestbody or cresponsebody

        field: 所属字段

        Returns
        -------
         异常分数: 越大越异常
        """
        host_store_dir = os.path.join(self.store_dir, f"{http_host}_{idstport}")

        if not os.path.exists(os.path.join(host_store_dir, f"{field}.model")):
            self._logger.get_log().info(f"{http_host}-{idstport}的{field}基线目前没有被拟合。")
            return -1

        model = joblib.load(os.path.join(host_store_dir, f"{field}.model"))
        s = model.predict(data, field)
        return s
