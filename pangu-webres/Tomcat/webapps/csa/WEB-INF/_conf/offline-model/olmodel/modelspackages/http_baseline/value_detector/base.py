from abc import ABC, abstractmethod


class BaseDetector(ABC):

    @abstractmethod
    def fit(self, *arg):
        """

        Parameters
        ----------
        data : pd.Series 基线参数值

        Returns
        -------

        """
        pass

    @abstractmethod
    def predict(self, data):
        """

        Parameters
        ----------
        data : 预测的参数值

        Returns
        -------

        """
        pass
