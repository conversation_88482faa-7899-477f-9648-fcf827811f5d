import numpy as np
import pandas as pd
import ipaddress
from typing import List, Dict
from scipy.spatial import ConvexHull, QhullError
from olmodel.models.constants import att_dic, spy_dic, malware_dic
from olmodel.models.miniball import get_bounding_ball


def get_subnet(ip_address, prefix_length):
    """
    计算给定 IP 地址和前缀长度的网段。

    参数:
    ip_address (str): IP 地址
    prefix_length (int): 前缀长度

    返回:
    str: 网段地址
    """
    network = ipaddress.ip_network(f"{ip_address}/{prefix_length}", strict=False)
    return str(network.network_address)


def map_event_to_being_value(cevent_type):
    perfix_type = cevent_type.split('/')[1]
    k = cevent_type.split('/')[-1]
    if perfix_type == 'att':
        if 'other' not in k:
            return att_dic[k]
        else:
            return ''
    elif perfix_type == 'spy':
        if 'other' not in k:
            return spy_dic[k]
        else:
            return ''
    elif perfix_type == 'malware':
        if 'other' not in k:
            return malware_dic[k]
        else:
            return ''
    else:
        return None


def map_event_to_tag_name(cevent_type):
    perfix_type = cevent_type.split('/')[1]
    if perfix_type == 'att':
        return '网络攻击'
    elif perfix_type == 'spy':
        return '信息刺探'
    elif perfix_type == 'malware':
        return '恶意程序'
    else:
        return None


def miniball(X: np.ndarray):
    """
    Calculates the center and radius of the minimum enclosing sphere.
    计算最小外接球的圆心和半径。

    Args:
        X (numpy.ndarray): 2D array of data points, with shape (n_samples, dimensions).
        X (numpy.ndarray): 数据点的二维数组，形状为 (n_samples, dimensions)。

    Returns:
        numpy.ndarray, float: Center of the minimum enclosing sphere, Radius of the minimum enclosing sphere.
        numpy.ndarray, float: 最小外接球的圆心，最小外接球的半径。
    """
    num_points, dimensions = X.shape
    if num_points < dimensions + 1:
        # raise ValueError(
        #     f"计算 {dimensions} 维空间的最小外接球需要至少 {dimensions + 1} 个点，但只提供了 {num_points} 个点。")
        print(f"计算 {dimensions} 维空间的最小外接球需要至少 2 个点，但只提供了 {num_points} 个点。")
    try:
        hull = ConvexHull(X)
        center = np.mean(hull.points[hull.vertices], axis=0)
        radius = np.max(np.sqrt(np.sum((hull.points[hull.vertices] - center) ** 2, axis=1)))
    except QhullError:
        center = np.mean(X, axis=0)
        center = np.array(center, dtype=float)
        X = np.array(X, dtype=float)
        radius = np.max(np.linalg.norm(X - center, axis=1))
    return center, radius


def count_groups_inside_spheres(df_data: pd.DataFrame, logger) -> Dict[str, int]:
    """
    计算圆球内的组
    示例输入
    df_data 'server_name' 都为 'bing.com'
    共有7组
    例：组1构成的圆球内其它组的数量：5
       组2构成的圆球内其它组的数量：2
       组3构成的圆球内其它组的数量：0
    res = [5, 2, 0, 0, 0, 0, 0]
    示例输入：
   df_data = pd.DataFrame(columns=['csrcip', 'cdstip', 'idstport', 'ds', 'rs', 'ss'],
                      data=[
                          ['1.1.1', '1.1.2', 443, '[1,2,3,4]', '[1,2,3,4]', '[1,2,3,4]'],
                          ['1.1.3', '1.1.4', 443, '[1,2,3,4]', '[1,2,3,4]', '[1,2,3,4]']
                      ])
    示例输出：
    {
        '组1': 5,
        '组2': 2,
        ...
    }
    """
    res_list = []
    count_groups_dic = {}
    unique_groups = df_data['group'].unique()
    total_group_count = len(unique_groups)
    logger.get_log().info(f"总共的组数： {total_group_count}")
    # 预先计算每个组的miniball
    group_miniball = {}
    for group in unique_groups:
        group_data = df_data[df_data['group'] == group][['lduration', 'lreceive', 'lsend']].values
        group_miniball[group] = get_bounding_ball(group_data)

    for group in unique_groups:
        center, radius = group_miniball[group]
        count = 0
        for other_group in unique_groups:
            if other_group != group:
                other_group_data = df_data[df_data['group'] == other_group][['lduration', 'lreceive', 'lsend']].values
                other_group_data = np.array(other_group_data, dtype=float)
                distance = np.linalg.norm(other_group_data - center, axis=1)
                if np.any(distance <= radius):
                    count += 1
        res_list.append(count)
        count_groups_dic[group] = count
        logger.get_log().info(f"组{group}构成的圆球内的其他组的数量： {count}")
    return count_groups_dic


def count_groups_inside_spheres_m1(df_data: pd.DataFrame, logger) -> Dict[str, int]:
    count_groups_dic = {}
    # 计算组-1相交的组的数量
    unique_groups = df_data['group'].unique()
    total_group_count = len(unique_groups)
    logger.get_log().info(f"总共的组数： {total_group_count}")
    group_data = df_data[df_data['group'] == -1][['lduration', 'lreceive', 'lsend']].values
    logger.get_log().info(f"组-1点的数量: {len(group_data)}")
    center, radius = get_bounding_ball(group_data)
    logger.get_log().info(f"组-1的圆球的圆心： {center}, 半径为： {radius}")

    count = 0
    for group in unique_groups:
        if group != -1:
            other_group_data = df_data[df_data['group'] == group][['lduration', 'lreceive', 'lsend']].values
            other_group_data = np.array(other_group_data, dtype=float)
            distance = np.linalg.norm(other_group_data - center, axis=1)
            if np.any(distance <= radius):
                count += 1
    logger.get_log().info(f"组-1构成的圆球内的其他组的数量： {count}")
    count_groups_dic[-1] = count
    return count_groups_dic