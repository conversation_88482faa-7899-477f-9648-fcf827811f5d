import base64


def Base64Shift2(sql):
    """
    对Base64加密后再移位
    Parameters
    ----------
    sql : 没有加密的sql字符串

    Returns
    -------
    encrypted : 加密后的字符串
    """
    encrypted = ''
    shift = 13
    base64_origin = base64.b64encode(sql.encode(encoding="utf8"))
    base64_origin_str = str(base64_origin, encoding='utf8')

    for i in base64_origin_str:
        if i.isalpha():
            ascii_value = ord(i)
            base = ord('A') if i.isupper() else ord('a')
            shifted_ascii_value = (ascii_value - base + shift) % 26 + base
            encrypted += chr(shifted_ascii_value)

        else:
            encrypted += i

    return encrypted


if __name__ == "__main__":
    print(Base64Shift2("Hello %$&^%*&*world! XZzcxy"))
