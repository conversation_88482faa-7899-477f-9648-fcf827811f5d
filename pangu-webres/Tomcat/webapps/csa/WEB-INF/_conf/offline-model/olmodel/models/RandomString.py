# -*- coding:utf-8 -*-
import os
import datetime
import joblib
from olmodel.models.BaseModel import BaseModel
from olmodel.modelspackages.randomstring.dga_model import DGAModel, get_second_level_domain_name, \
    get_three_level_domain_name, get_four_level_domain_name, get_domain_name
from olmodel.exceptions.ParamException import ParamException


class RandomString(BaseModel):

    def __init__(self, ck_handler, model_config, ts_window=60, level="二级域名", name="dns_qry_name", **kwargs):
        super().__init__(ck_handler)
        self._model_config = model_config
        self._ts_window = ts_window
        self._level = level
        self._name = name
        self._now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self._event_table = "event20" + datetime.datetime.now().strftime('%y%m%d')

        # self._now_datetime = "2022-06-23 10:42:16"
        # self._event_table = "event20220623"

        self._model = None

    def get_data(self):
        """获取待检测的数据
        """
        sql = ""

        if self._name == "dns_qry_name":
            sql = """
            select cuserid, csrcip, cdstip, idstport, csrcipinner, cdstipinner, iprotocol, iappprotocol, 
                dns_qry_name, dns_main_name
            from {event_table}
            where startsWith(ceventtype, '/behavior') 
              and multiMatchAny(dns_qry_name, ['[a-zA-Z]'])
              and eventdate > subtractMinutes(toDateTime('{now_datetime}'), {window_time})
            """.format(
                event_table=self._event_table,
                now_datetime=self._now_datetime,
                window_time=self._ts_window
            )
        elif self._name == "http_host":
            sql = """
            select cuserid, csrcip, cdstip, idstport, csrcipinner, cdstipinner, iprotocol, iappprotocol, 
                http_host, dns_main_name
            from {event_table}
            where startsWith(ceventtype, '/behavior') 
              and multiMatchAny(http_host, ['[a-zA-Z]'])
              and eventdate > subtractMinutes(toDateTime('{now_datetime}'), {window_time})
            """.format(
                event_table=self._event_table,
                now_datetime=self._now_datetime,
                window_time=self._ts_window
            )
        elif self._name == "server_name":
            sql = """
            select cuserid, csrcip, cdstip, idstport, csrcipinner, cdstipinner, iprotocol, iappprotocol, 
                server_name, dns_main_name
            from {event_table}
            where startsWith(ceventtype, '/behavior') 
              and multiMatchAny(server_name, ['[a-zA-Z]'])
              and eventdate > subtractMinutes(toDateTime('{now_datetime}'), {window_time})
            """.format(
                event_table=self._event_table,
                now_datetime=self._now_datetime,
                window_time=self._ts_window
            )

        self._logger.get_log().info(f'RandomString algorithm. Executing sql:\n{sql}')
        df = self._ck_handler.get_data_func(sql)
        return df

    def get_model(self):
        # 模型存储的位置
        model_filepath = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "modelspackages", "randomstring", "model.joblib"
        )
        if not os.path.exists(model_filepath):
            model = DGAModel().fit()
            joblib.dump(model, model_filepath)

        else:
            model = joblib.load(model_filepath)

        return model

    def predict(self, data):
        if self._level == "二级域名":
            function_ = get_second_level_domain_name
        elif self._level == "三级域名":
            function_ = get_three_level_domain_name
        elif self._level == "四级域名":
            function_ = get_four_level_domain_name
        else:
            raise ParamException(f'参数level是{self._level}，应该选择二级域名、三级域名或四级域名。')

        if self._name == "dns_qry_name":
            test_data = data.loc[:, ["dns_qry_name"]].rename(columns={"dns_qry_name": 'domain_name'}, inplace=False)
            test_data["domain"] = test_data["domain_name"].apply(lambda domain: function_(domain))
            labels = self._model.predict(test_data)
        elif self._name == "http_host":
            test_data = data.loc[:, ["http_host"]].rename(columns={"http_host": 'domain_name'}, inplace=False)
            test_data["domain"] = test_data["domain_name"].apply(lambda domain: function_(domain))
            labels = self._model.predict(test_data)
        elif self._name == "server_name":
            test_data = data.loc[:, ["server_name"]].rename(columns={"server_name": "domain_name"}, inplace=False)
            test_data["domain"] = test_data["domain_name"].apply(lambda domain: function_(domain))
            labels = self._model.predict(test_data)
        else:
            raise ParamException(f'参数name是{self._name}，应该选择dns_qry_name，http_host或server_name。')

        test_data_ = data.loc[:, ["dns_main_name"]].rename(columns={"dns_main_name": "domain_name"}, inplace=False)
        test_data_["domain"] = test_data_["domain_name"].apply(lambda domain: get_domain_name(domain))
        labels_ = self._model.predict(test_data_)

        self._logger.get_log().info(f"{self._name}字段，检测{self._level}是否是随机字符串，检测完成")

        return (labels == "dga") | (labels_ == "dga")

    def run(self):
        data = self.get_data()
        if len(data) == 0:
            self._logger.get_log().info("随机字符串算法，当前没有数据。")
            return []

        self._model = self.get_model()
        self._logger.get_log().info("读取模型正常")

        self._logger.get_log().info("进行预测")
        labels = self.predict(data)
        data = data[labels]

        offline_result = data.to_dict("records")

        return offline_result
