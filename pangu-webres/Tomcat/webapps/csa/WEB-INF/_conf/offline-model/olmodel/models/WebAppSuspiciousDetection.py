import datetime
from olmodel.models.BaseModel import BaseModel

"""
模型名称：HTTP_访问企业web应用的可疑流量
异常行为名称：
 HTTP_URL中存在被加密的内容 
 HTTP_请求头中存在被加密的内容
 HTTP_请求体中存在被加密的内容 
 HTTP_响应头中存在被加密的内容 
 HTTP_响应体中存在被加密的内容
"""


class WebAppSuspiciousDetection(BaseModel):
    def __init__(self, ck_handler, mysql_handler, model_config, name='webshell', ts_window=60,
                 **kwargs):
        super().__init__(ck_handler)
        self._ts_window = ts_window
        self._baseline_time = 14
        self._name = name
        self._time_offset = 0
        self._mysql_handler = mysql_handler
        self._model_config = model_config
        # get time (format: 220603)
        self.event_table = datetime.datetime.now().strftime('%y%m%d')
        self.event_table_yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%y%m%d')
        self._now_datetime = (datetime.datetime.now() + datetime.timedelta(minutes=self._time_offset)
                              ).strftime("%Y-%m-%d %H:%M:%S")

    def get_data(self):
        # sql = """
        # select csrcip, cdstip, csrcipinner, cdstipinner, http_host, idstport, _soh_, count(1) as n,
        # groupUniqArrayIf(http_url ,     match(splitByChar('?', http_url)[2] , {match_str1})) as e0,
        # groupUniqArrayIf(crequesthead , match(crequesthead , {match_str1})) as e1,
        # groupUniqArrayIf(crequestbody , match(crequestbody , {match_str1})) as e2,
        # groupUniqArrayIf(cresponsehead, match(cresponsehead, {match_str2})) as e3,
        # groupUniqArrayIf(cresponsebody, match(cresponsebody, {match_str2})) as e4,
        # length(e0) as l0, length(e1) as l1, length(e2) as l2, length(e3) as l3, length(e4) as l4
        # from
        # (
        #     SELECT toDateTime(loccurtime / 1000) as ts, toStartOfHour(ts) as _soh_,
        #      csrcip, cdstip, isrcport, idstport, csrcipinner, cdstipinner,
        #      iappprotocol,http_host, http_uri_no_parameter, http_url,
        #     crequesthead, crequestbody, cresponsehead, cresponsebody
        #     FROM {event_table}
        #     WHERE (1 = 1)
        #     AND (csrclogtype = '/security/IDS/HTTP')
        #     and cdstipinner = 1
        # )
        # group by csrcip, cdstip, csrcipinner, cdstipinner, http_host, idstport,_soh_
        # having l0>5 or l1>5 or l2>5 or l3>5 or l4>5
        # """.format(
        #     event_table="event20" + self.event_table,
        #     match_str1='\'[0-9a-zA-Z\-\+\_\=/]{50,}\'',
        #     match_str2='\'[0-9a-zA-Z\-\+\_\=/]{256,}\''
        # )
        # self._logger.get_log().info(f"HTTP_访问企业web应用的可疑流量: {sql}")
        # df_data = self._ck_handler.get_data_func(sql)
        return []

    def run(self):
        df_data = self.get_data()
        self._logger.get_log().info(f"HTTP_访问企业web应用的可疑流量 {df_data.shape}")
        if len(df_data) == 0:
            self._logger.get_log().info("HTTP_访问企业web应用的可疑流量 无数据")
            return []

        return []
