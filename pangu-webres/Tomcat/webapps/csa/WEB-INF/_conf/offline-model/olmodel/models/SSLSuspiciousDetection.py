import datetime
import pandas as pd
import numpy as np
import ast
from olmodel.models.utils import count_groups_inside_spheres, count_groups_inside_spheres_m1
from olmodel.models.constants import SSL_Suspicous
from sklearn.preprocessing import StandardScaler
from olmodel.models.BaseModel import BaseModel
import ipaddress


def is_private_ip(ip):
    """
    判断一个IP地址是否为内网IP地址。

    参数:
        ip (str): 要检查的IP地址。

    返回:
        int: 如果是内网IP地址返回1，外网地址返回2，否则返回0。
    """
    try:
        ip_obj = ipaddress.ip_address(ip)
        return 1 if ip_obj.is_private else 2
    except ValueError:
        return 0  # 无效的IP地址


# 每小时运行一次，第10分钟运行
class SSLSuspiciousDetection(BaseModel):
    def __init__(self, ck_handler, mysql_handler, model_config, name='encryptedtrafficdetection', ts_window=60,
                 **kwargs):
        super().__init__(ck_handler)
        self._ts_window = ts_window
        self._baseline_time = 14
        self._name = name
        self._time_offset = 0
        self._mysql_handler = mysql_handler
        # get time (format: 220603)
        self.event_table = datetime.datetime.now().strftime('%y%m%d')
        self.event_table_yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%y%m%d')
        self._now_datetime = (datetime.datetime.now() + datetime.timedelta(minutes=self._time_offset)
                              ).strftime("%Y-%m-%d %H:%M:%S")

    # SSL_可疑加密外联流量_假冒域名
    def get_fake_domain(self):
        sql_fake_domain = """
          select csrcip, cdstip, idstport, server_name, ssl_ja3,
           count(1) as n, max(ts) - min(ts) as ts_span, uniq(lsend) as su1, uniq(lreceive) as ru1, 
          groupArray(lduration) as ds, countIf(lduration<=5) as dn1,
          groupArray(lsend) as ss, countIf(lsend<1000) as sn1,
          groupArray(lreceive) as rs, countIf(lreceive<1000) as rn1
          from 
          (
              select ts, csrcip, cdstip, idstport, server_name, ssl_ja3, lduration, lsend, lreceive from 
              (
                  SELECT toDateTime(loccurtime/1000) as ts, csrcip, cdstip, idstport, iprotocol, iappprotocol, lduration/1000 as lduration, lsend, lreceive 	
                  FROM {event_table}
                  WHERE (1 = 1) 
                  AND (csrclogtype = '/security/IDS/Flow') 
                  AND ts <= now() AND ts  >= now() - toIntervalMinute(60)
                  AND (cdstipinner = 2) AND (csrcipinner = 1)
                  and iprotocol = 6
                  and (iappprotocol = 13 or idstport = 443)
              )as t1
              any left join 
              (
                  select csrcip, cdstip, idstport, server_name, ssl_ja3, 1 as f from 
                  (
                      select csrcip, cdstip, idstport, server_name, any(ssl_ja3) as ssl_ja3, count(1) as n, 
                      countIf(empty(ssl_issuer)) as n1, countIf(notEmpty(ssl_issuer) and ssl_issuer = ssl_subject) as n2, countIf(notEmpty(ssl_issuer) and ssl_issuer like '%s Encrypt%') as n3 from 
                      (
                          SELECT toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, server_name, dns_main_name, ssl_issuer, ssl_subject, ssl_ja3,
                          http_host_main_domain_alexa_rank, http_host_main_domain_rarity, 
                          dns_main_name,dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
                          FROM {event_table}
                          WHERE (1 = 1) 
                          AND (csrclogtype = '/security/IDS/SSL') 
                          AND (cdstipinner = 2) AND (csrcipinner = 1)
                          and notEmpty(server_name)
                          AND (http_host_main_domain_alexa_rank != '0') 
                          and cdstip not in 
                          (
                              select dst_ip as cdstip from cache_flow_log_view
                              where event_day >= today() - 14 and event_day < today() and dst_ip_inner_ip = 2 AND (src_log_type = '/security/IDS/SSL') 
                              group by dst_ip
                          )
                          and IPv4NumToStringClassC(IPv4StringToNumOrDefault(cdstip)) not in 
                          (
                              select IPv4NumToStringClassC(IPv4StringToNumOrDefault(ip)) from 
                              (
                                  select groupUniqArrayArrayMerge(mat_indic_1022537078354748657) as ips from mat_view_999784571044127150
                                  where viewdate >= today() - 14 and notEmpty(dns_main_name) 
                              )
                              array join ips as ip 
                              where notEmpty(ip)
                          )
                      )
                      group by csrcip, cdstip, idstport, server_name
                      having n = n1 or n2 > 0 or n3 > 0
                  )
              )as t2
              on t1.csrcip = t2.csrcip and t1.cdstip = t2.cdstip and t1.idstport = t2.idstport
              where f = 1
          )
          group by csrcip, cdstip, idstport, server_name,ssl_ja3
          having dn1/n > 0.7 and sn1/n < 0.5 and rn1/n < 0.5 and n>10
          """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"假冒域名sql:\n{sql_fake_domain}")
        df_data_fake_domain = self._ck_handler.get_data_func(sql_fake_domain)
        self._logger.get_log().info(f"假冒域名df: {df_data_fake_domain.shape}")
        return df_data_fake_domain

        # --https，罕见目的IP

    # SSL_可疑加密外联流量_少见IP
    def get_data_rare_dst_ip(self):
        sql_rare_dst_ip = """
          select csrcip, cdstip, idstport, server_name, ssl_ja3, count(1) as n, max(ts) - min(ts) as ts_span, uniq(lsend) as su1, uniq(lreceive) as ru1, 
          groupArray(lduration) as ds, countIf(lduration<=5) as dn1,
          groupArray(lsend) as ss, countIf(lsend<1000) as sn1,
          groupArray(lreceive) as rs,  countIf(lreceive<1000) as rn1
          from 
          (
              select ts, csrcip, cdstip, idstport, server_name, ssl_ja3, lduration/1000 as lduration, lsend, lreceive from 
              (
                  SELECT toDateTime(loccurtime/1000) as ts, csrcip, cdstip, idstport, iprotocol, iappprotocol, lduration/1000 as lduration, lsend, lreceive 	
                  FROM {event_table}
                  WHERE (1 = 1) 
                  AND (csrclogtype = '/security/IDS/Flow') 
                  AND ts <= now() AND ts  >= now() - toIntervalMinute(60) 
                  AND (cdstipinner = 2) AND (csrcipinner = 1)
                  and iprotocol = 6
                  and (iappprotocol = 13 or idstport = 443)
              )as t1
              any left join 
              (
                  select csrcip, cdstip, idstport, server_name, ssl_ja3, 1 as f from 
                  (
                      select csrcip, cdstip, idstport, any(server_name) as server_name, any(ssl_ja3) as ssl_ja3, count(1) as n from 
                      (
                          SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport,  iappprotocol, server_name, dns_main_name, ssl_issuer, ssl_subject, ssl_ja3, ssl_ja3s,
                          http_host_main_domain_alexa_rank, http_host_main_domain_rarity, 
                          dns_main_name,dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
                          FROM {event_table}
                          WHERE (1 = 1) 
                          AND (csrclogtype = '/security/IDS/SSL') 
                          AND (cdstipinner = 2) AND (csrcipinner = 1)
                          and idstport not in (853, 995, 993)
                          and (empty(server_name) or match(server_name, {match_str}))
                          and cdstip not in 
                          (
                              select dst_ip as cdstip from cache_flow_log_view
                              where event_day >= today() - 14 and event_day < today() and dst_ip_inner_ip = 2 AND (src_log_type = '/security/IDS/SSL') 
                              group by dst_ip
                          )
                      )
                      group by csrcip, cdstip, idstport
                      having n > 10
                  )
              )as t2
              on t1.csrcip = t2.csrcip and t1.cdstip = t2.cdstip and t1.idstport = t2.idstport
              where f = 1
          )
          group by csrcip, cdstip, idstport, server_name,ssl_ja3
          having dn1/n > 0.7 and sn1/n < 0.5 and rn1/n < 0.5 and n>10
          """.format(
            event_table="event20" + self.event_table,
            match_str='\'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\'',
        )
        self._logger.get_log().info(f"罕见目的ip:\n{sql_rare_dst_ip}")
        df_data_rare_dst_ip = self._ck_handler.get_data_func(sql_rare_dst_ip)
        self._logger.get_log().info(f"罕见目的ip: {df_data_rare_dst_ip.shape}")
        return df_data_rare_dst_ip

        # --https, 罕见域名

    # SSL_可疑加密外联流量_少见域名
    def get_data_rare_domain(self):
        sql_rare_domain = """
          select csrcip, cdstip, idstport, server_name, ssl_ja3, count(1) as n, 
          max(ts) - min(ts) as ts_span, uniq(lsend) as su1, uniq(lreceive) as ru1, 
          groupArray(lduration) as ds, countIf(lduration<=5) as dn1,
          groupArray(lsend) as ss, countIf(lsend<1000) as sn1,
          groupArray(lreceive) as rs,  countIf(lreceive<1000) as rn1
          from 
          (
              select ts, csrcip,  cdstip, idstport, server_name, ssl_ja3, lduration/1000 as lduration, lsend, lreceive from 
              (
                  SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, iprotocol, iappprotocol, lduration/1000 as lduration, lsend, lreceive 	
                  FROM {event_table}
                  WHERE (1 = 1) 
                  AND (csrclogtype = '/security/IDS/Flow') 
                  AND ts <= now() AND ts  >= now() - toIntervalMinute(60) 
                  AND (cdstipinner = 2) AND (csrcipinner = 1)
                  and iprotocol = 6
                  and (iappprotocol = 13 or idstport = 443)
              )as t1
              any left join 
              (

                  select csrcip, cdstip, idstport, server_name, ssl_ja3 from 
                  (
                      select csrcip, cdstip, idstport, server_name, ssl_ja3, count(1) as n from 
                      (
                          SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, iappprotocol, server_name, dns_main_name, ssl_issuer, ssl_subject, ssl_ja3, ssl_ja3s,
                          http_host_main_domain_alexa_rank,  http_host_main_domain_rarity, 
                          dns_main_name,dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
                          FROM {event_table_yesterday}
                          WHERE (1 = 1) 
                          AND (csrclogtype = '/security/IDS/SSL') 
                          AND (cdstipinner = 2) AND (csrcipinner = 1)
                          and idstport not in (853, 995, 993)
                          and notEmpty(server_name)
                          and not match(server_name, {match_str})
                          AND (http_host_main_domain_alexa_rank = '0') 
                          and dns_main_name not in 
                          (
                              select dns_main_name as md from mat_view_999784571044127150
                              where viewdate < today() and viewdate >= today() - 14 and notEmpty(dns_main_name) and dns_main_name_alexa_rank = 0
                              group by dns_main_name
                          )
                          and server_name not in 
                          (
                              select distinct server_name from 
                              (
                                  select server_name, uniq(csrcip) as u from 
                                  (
                                      select server_name, csrcip  FROM 
                                      {event_table_yesterday}
                                      WHERE (1 = 1) 
                                      AND (csrclogtype = '/security/IDS/SSL') 
                                      AND (cdstipinner = 2) AND (csrcipinner = 1)
                                      AND (http_host_main_domain_alexa_rank = '0') 
                                      and notEmpty(server_name)
                                  )
                                  group by server_name
                              )
                          )
                      )
                      group by csrcip, cdstip, idstport, server_name, ssl_ja3
                      having n > 10
                  )
              )as t2
              on t1.csrcip = t2.csrcip and t1.cdstip = t2.cdstip and t1.idstport = t2.idstport
              where notEmpty(server_name)
          )
          group by csrcip, cdstip, idstport, server_name,ssl_ja3
          having dn1/n > 0.7 and sn1/n < 0.5 and rn1/n < 0.5 and n>10
          """.format(
            event_table="event20" + self.event_table,
            event_table_yesterday="event20" + self.event_table_yesterday,
            match_str='\'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\'',
        )
        self._logger.get_log().info(f"罕见域名:\n{sql_rare_domain}")
        try:
            df_data_rare_domain = self._ck_handler.get_data_func(sql_rare_domain)
            self._logger.get_log().info(f"罕见域名: {df_data_rare_domain.shape}")
        except Exception as e:
            self._logger.get_log().info(f"{e}")
            return pd.DataFrame()
        return df_data_rare_domain

    # SSL_可疑加密外联流量_云函数
    def get_data_cloud_func(self):
        sql_cloud_func = """
          select csrcip, cdstip, idstport, server_name, ssl_ja3, count(1) as n, max(ts) - min(ts) as ts_span, uniq(lsend) as su1, uniq(lreceive) as ru1, 
          groupArray(lduration) as ds, countIf(lduration<=5) as dn1,
          groupArray(lsend) as ss, countIf(lsend<1000) as sn1,
          groupArray(lreceive) as rs,  countIf(lreceive<1000) as rn1
          from 
          (
              select ts, csrcip, cdstip, idstport, server_name, ssl_ja3, lduration, lsend, lreceive from 
              (
                  SELECT toDateTime(loccurtime / 1000) as ts, csrcip,  cdstip, idstport, iprotocol, iappprotocol, lduration/1000 as lduration, lsend, lreceive 	
                  FROM {event_table_yes}
                  WHERE (1 = 1)
                  AND ts <= now() AND ts  >= now() - toIntervalMinute(60) 
                  AND (csrclogtype = '/security/IDS/Flow') 
                  AND (cdstipinner = 2) AND (csrcipinner = 1)
                  and iprotocol = 6
                  and (iappprotocol = 13 or idstport = 443)
              )as t1
              any left join 
              (
                  select csrcip, cdstip, idstport, server_name, ssl_ja3 from 
                  (
                      select csrcip, cdstip, idstport, server_name, ssl_ja3, count(1) as n from 
                      (
                          SELECT toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, iappprotocol, server_name, dns_main_name, ssl_issuer, ssl_subject, ssl_ja3, ssl_ja3s,
                          http_host_main_domain_alexa_rank,  http_host_main_domain_rarity, 
                          dns_main_name,dst_ip_pDns_main_name, dst_ip_pDns_main_name_rarity, dst_ip_rarity
                          FROM {event_table}
                          WHERE (1 = 1) 
                          AND (csrclogtype = '/security/IDS/SSL') 
                          AND (cdstipinner = 2) AND (csrcipinner = 1)
                          and idstport not in (853, 995, 993)
                          and notEmpty(server_name)
                          and not match(server_name, {match_str})
                          and (server_name like '%aliyuncs.com%' or server_name like '%apigw.tencentcs.com%')
                          and server_name not in 
                          (
                              select distinct server_name from 
                              (
                                  select server_name, uniq(csrcip) as u from 
                                  (
                                      select server_name, csrcip  FROM 
                                      {event_table_yes}
                                      WHERE (1 = 1) 
                                      AND (csrclogtype = '/security/IDS/SSL') 
                                      AND (cdstipinner = 2) AND (csrcipinner = 1)
                                      AND (http_host_main_domain_alexa_rank = '0') 
                                      and notEmpty(server_name)
                                  )
                                  group by server_name
                              )
                          )
                      )
                      group by csrcip, cdstip, idstport, server_name, ssl_ja3
                      having n > 10
                  )
              )as t2
              on t1.csrcip = t2.csrcip and t1.cdstip = t2.cdstip and t1.idstport = t2.idstport
              where notEmpty(server_name)
          )
          group by csrcip, cdstip, idstport, server_name,ssl_ja3
          having dn1/n > 0.7 and sn1/n < 0.5 and rn1/n < 0.5 and n>10
          """.format(
            event_table="event20" + self.event_table,
            event_table_yes="event20" + self.event_table_yesterday,
            # match_str='\'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\'',
            match_str='\'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\'',
        )
        self._logger.get_log().info(f"云函数:\n{sql_cloud_func}")
        try:
            df_data_cloud_func = self._ck_handler.get_data_func(sql_cloud_func)
            self._logger.get_log().info(f"云函数: {df_data_cloud_func.shape}")
        except Exception as e:
            self._logger.get_log().info(f"{e}")
            return pd.DataFrame()
        return df_data_cloud_func

    def run(self):

        df_fake_domain = self.get_fake_domain()
        df_fake_domain['ceventdigest'] = 'SSL_可疑加密外联流量_假冒域名'
        df_rare_dst_ip = self.get_data_rare_dst_ip()
        df_rare_dst_ip['ceventdigest'] = 'SSL_可疑加密外联流量_少见IP'
        df_rare_domain = self.get_data_rare_domain()
        df_rare_domain['ceventdigest'] = 'SSL_可疑加密外联流量_少见域名'
        df_cloud_func = self.get_data_cloud_func()
        df_cloud_func['ceventdigest'] = 'SSL_可疑加密外联流量_云函数'
        df_domain_fronting = self.domain_fronting()
        self._logger.get_log().info(f"知名域名的shape：{df_domain_fronting.shape}")
        df_domain_fronting['ceventdigest'] = 'SSL_可疑加密外联流量_知名域名'
        offline_df = pd.concat([df_fake_domain, df_rare_dst_ip, df_rare_domain, df_cloud_func, df_domain_fronting])
        if len(offline_df) == 0:
            return []
        # offline_df['csrcipinner'] = offline_df['csrcip'].apply(lambda x: is_private_ip(x))
        # offline_df['cdstipinner'] = offline_df['cdstip'].apply(lambda x: is_private_ip(x))
        # 内网地址: 1 外网地址: 2
        offline_df['csrcipinner'] = 1
        offline_df['cdstipinner'] = 2
        offline_df = offline_df[SSL_Suspicous]
        offline_df['idstport'] = offline_df['idstport'].astype(int)
        res_df = self.lateral_movement(offline_df)
        offline_result = res_df.to_dict('records')
        self._logger.get_log().info(f"SSL可疑加密外联输出的结果为{offline_result}")
        return offline_result

    def lateral_movement(self, df_data):
        csrcip_tuple = tuple(df_data['csrcip'])
        sql = """
        SELECT element from (select csrcip as element , count(1) as num 
        from alert_behavior 
        where 1=1
        and update_datetime >= today()-1
        and 
        (
        alert_type in ('g69o5aeASMhnWbylmt49J', 't9cb7d6lDaA529wZq2ew4') or 
        ceventname like '%横向移动%'
        )
        and csrcip in {csrcip_list}
        group by csrcip) 
        WHERE num > 1
        """.format(
            csrcip_list=csrcip_tuple
        )
        self._logger.get_log().info(f"执行sql{sql}")
        element_df = self._ck_handler.get_data_func(sql)
        if len(element_df) == 0:
            self._logger.get_log().info("执行sql获取到的element为空")
            return []
        element_list = element_df['element'].to_list()
        self._logger.get_log().info(f"获取到的element为{element_list}")
        filtered_df = df_data[df_data['csrcip'].isin(element_list)]
        return filtered_df

    def domain_fronting(self):
        """
        域前置场景
        Returns
        -------

        """
        sql = """
           SELECT
            csrcip, cdstip, idstport, server_name, ssl_ja3, count(1) AS n,
            max(ts) - min(ts) AS ts_span, toDate(max(ts)) as d,
            uniq(lsend) AS su1, uniq(lreceive) AS ru1,
            groupArray(lduration) AS ds, countIf(lduration <= 5) AS dn1,
            groupArray(lsend) AS ss,countIf(lsend < 1000) AS sn1,
            groupArray(lreceive) AS rs, countIf(lreceive < 1000) AS rn1
        FROM
        (
            SELECT
                ts, csrcip, cdstip, idstport, server_name, ssl_ja3,
                lduration / 1000 AS lduration, lsend, lreceive
            FROM
            (
                SELECT
                    toDateTime(loccurtime / 1000) AS ts, csrcip, cdstip, idstport, lduration,  lsend, lreceive
                FROM {event_table}
                WHERE
                    (1 = 1)
                    AND (csrclogtype = '/security/IDS/Flow')
                    AND (cdstipinner = 2)
                    AND (csrcipinner = 1)
                    AND idstport IN (443)
            ) AS t1
            ANY LEFT JOIN
            (
                SELECT
                    csrcip, cdstip, idstport, server_name, ssl_ja3
                FROM
                (
                    SELECT
                        csrcip, cdstip, idstport, server_name,  http_host_main_domain_alexa_rank,
                        ssl_ja3, count(1) AS n,
                        countIf(notEmpty(ssl_subject)) AS n1,
                        countIf(notEmpty(ssl_issuer) AND ssl_issuer != ssl_subject) AS n2,
                        countIf(notEmpty(ssl_issuer) AND ssl_issuer = ssl_subject) AS n3
                    FROM
                    (
                        SELECT
                            toDateTime(loccurtime / 1000) AS ts,
                            csrcip, cdstip, idstport, server_name,  http_host_main_domain_alexa_rank,
                            ssl_issuer, ssl_subject, ssl_ja3
                        FROM {event_table}
                        WHERE
                            (1 = 1)
                            AND (csrclogtype = '/security/IDS/SSL')
                            AND (cdstipinner = 2)
                            AND (csrcipinner = 1)
                            AND (http_host_main_domain_alexa_rank != '0')
                            AND cdstip NOT IN ('*************', '*************')
                            AND notEmpty(server_name)
                            AND cdstip IN
                            (
                                SELECT DISTINCT ip FROM
                                (
                                    WITH
                                        splitByChar(',', dns_response_answers) AS parts,
                                        arrayFilter(x -> match(x, '\\d+\\.\\d+\\.\\d+\\.\\d+'), parts) AS ips,
                                        indexOf(parts, arrayElement(ips, 1)) AS first_ip_index,
                                        arraySlice(parts, 1, first_ip_index - 1) AS domains_before_first_ip,
                                        arrayElement(arrayReverse(domains_before_first_ip), 1) AS last_domain_before_first_ip,
                                        splitByChar('.', last_domain_before_first_ip) AS reversed_domain_parts,
                                        length(reversed_domain_parts) AS lh
                                    SELECT
                                        csrcip,
                                        dns_main_name,
                                        dns_qry_name,
                                        cdstip,
                                        concat(reversed_domain_parts[lh-1], '.', reversed_domain_parts[lh]) AS top_level_domain,
                                        ips,
                                        dns_response_answers
                                    FROM {event_table}
                                    WHERE
                                        (1 = 1)
                                        AND (csrclogtype = '/security/IDS/DNS')
                                        AND notEmpty(dns_response_answers)
                                        AND (
                                            lower(top_level_domain) LIKE 'aicdn%' OR
                                            lower(top_level_domain) LIKE 'alikunlun%' OR
                                            lower(top_level_domain) LIKE 'cdn%' OR
                                            lower(top_level_domain) LIKE 'china%' OR
                                            lower(top_level_domain) LIKE 'dns%' OR
                                            lower(top_level_domain) LIKE 'ks%' OR
                                            lower(top_level_domain) LIKE 'kunlun%' OR
                                            lower(top_level_domain) LIKE 'qcloud%' OR
                                            lower(top_level_domain) LIKE 'qiniu%' OR
                                            lower(top_level_domain) LIKE 'queniu%' OR
                                            lower(top_level_domain) LIKE 'tdns%' OR
                                            lower(top_level_domain) LIKE 'queniu%' OR
                                            lower(top_level_domain) LIKE 'queniu%' OR
                                            lower(top_level_domain) LIKE 'queniu%' OR
                                            lower(top_level_domain) IN (
                                                'aliyuncs.com', 'b-cdn.net', 'baishangeek.cn', 'bsccdn.com', 'bscedge.com',
                                                'bsclink.cn', 'bsgslb.cn', 'bsgslb.com', 'cloudcsp.com', 'ctdns.cn', 'dayugslb.com',
                                                'fastly.net', 'jomodns.com', 'kjykcdn.com', 'qianxun.com', 'qnydns.com', 'qtlcdn.com',
                                                'wsglb0.com', 'wscdns.com', 'wsdvs.com', 'fastcdn.com', 'ccgslb.com.cn'
                                            )
                                        )
                                )
                                ARRAY JOIN ips AS ip
                            )
                    )
                    GROUP BY
                        csrcip, cdstip, idstport, server_name, http_host_main_domain_alexa_rank, ssl_ja3
                    HAVING n1 >= 1 AND n2 > 0 AND n3 = 0
                )
            ) AS t2
            ON t1.csrcip = t2.csrcip AND t1.cdstip = t2.cdstip AND t1.idstport = t2.idstport
            WHERE notEmpty(server_name)
        )
        GROUP BY
            csrcip, cdstip, idstport, server_name, ssl_ja3
        HAVING
            dn1 / n > 0.7
            AND sn1 / n < 0.5
            AND rn1 / n < 0.5
            AND n > 10
        """.format(
            event_table="event20" + self.event_table,
            # event_table="event20" + '240625'
        )

        self._logger.get_log().info(sql)
        df_data_suspicious = self._ck_handler.get_data_func(sql)
        self._logger.get_log().info(f"知名域名的df_data_suspicious shape = {df_data_suspicious.shape}")
        if len(df_data_suspicious) == 0:
            return pd.DataFrame()

        self.server_name_tuple = tuple(df_data_suspicious['server_name'].unique())
        self._logger.get_log().info(self.server_name_tuple)

        today = datetime.datetime.now()
        yesterday = today - datetime.timedelta(days=1)
        # today = "240625"
        event_list = [(yesterday - datetime.timedelta(days=i)).strftime('%y%m%d') for i in range(7)]
        # event_list = ['240624']
        for day in event_list:
            sql_history = """
            select ts, csrcip, cdstip, idstport, server_name, ssl_ja3,  
            lduration/1000 as lduration, lsend, lreceive, toDate(ts) as d
            from
            (
                SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, lduration, lsend, lreceive
                FROM {event_table}
                WHERE (1 = 1)
                AND (csrclogtype = '/security/IDS/Flow')
                AND (cdstipinner = 2) AND (csrcipinner = 1)
                and idstport in (443)
            )as t1
            any left join
            (
                select csrcip, cdstip, idstport, server_name, ssl_ja3 from
                (
                    select csrcip, cdstip, idstport, server_name, http_host_main_domain_alexa_rank,ssl_ja3,
                    count(1) as n, countIf(notEmpty(ssl_subject)) as n1,
                    countIf(notEmpty(ssl_issuer) and ssl_issuer != ssl_subject) as n2,
                    countIf(notEmpty(ssl_issuer) and ssl_issuer = ssl_subject) as n3 from
                    (
                        SELECT  toDateTime(loccurtime / 1000) as ts, csrcip, cdstip, idstport, server_name,
                        http_host_main_domain_alexa_rank,ssl_issuer, ssl_subject, ssl_ja3
                        FROM {event_table}
                        WHERE (1 = 1)
                        AND (csrclogtype = '/security/IDS/SSL')
                        AND (cdstipinner = 2) AND (csrcipinner = 1)
                        and server_name in {server_name_tuple}
                    )
                    group by  csrcip, cdstip, idstport, server_name, http_host_main_domain_alexa_rank, ssl_ja3
                    having n1>=1 and n2>0 and n3 = 0
                )
            )as t2
            on t1.csrcip = t2.csrcip and t1.cdstip = t2.cdstip and t1.idstport = t2.idstport
            where notEmpty(server_name)
    """.format(
                server_name_tuple=self.server_name_tuple,
                event_table="event20" + day,
            )
            self._logger.get_log().info(sql_history)
            try:
                df_data = self._ck_handler.get_data_func(sql_history)
                df_data.append(df_data)
            except Exception as e:
                self._logger.get_log().error(f"Error processing day {day}: {e}")
                continue
        self._logger.get_log().info(f"历史7天的df_data_history shape = {df_data.shape}")
        if len(df_data) == 0:
            self._logger.get_log().info(f"应历史7天的数据为空")
            return pd.DataFrame()
        res_df = pd.DataFrame(columns=['server_name', 'ssl_ja3', 'idstport', 'lduration', 'd', 'cdstip',
                                       'csrcip', 'lsend', 'lreceive', 'group'])
        # for sn in self.server_name_tuple:
        for index, row in df_data_suspicious.iterrows():
            sn = row['server_name']
            self._logger.get_log().info(f"待检测server_name：{sn}")
            df_data_obs = pd.DataFrame(row).T
            # df_data_obs = df_data_suspicious[df_data_suspicious['server_name'] == sn].copy()
            df_data_obs[['lduration', 'lreceive', 'lsend']] = df_data_obs[['ds', 'rs', 'ss']].applymap(ast.literal_eval)
            df_data_obs['group'] = -1
            # 展开特定的列
            df_expanded = df_data_obs.explode(['lduration', 'lreceive', 'lsend'])
            if df_expanded.shape[0] < 2:
                self._logger.get_log(f"server_name: {sn}的点的数量<2，跳过该server_name")
                continue
            all_cols = ['server_name', 'ssl_ja3', 'idstport', 'lduration', 'd', 'cdstip',
                        'csrcip', 'lsend', 'lreceive', 'group']
            q_cols = ['lduration', 'lreceive', 'lsend']
            quantiles1 = df_expanded['lduration'].quantile([0.05, 0.95])
            quantiles2 = df_expanded['lreceive'].quantile([0.05, 0.95])
            quantiles3 = df_expanded['lsend'].quantile([0.05, 0.95])
            quantiles = pd.concat([quantiles1, quantiles2, quantiles3], axis=1)
            df_expanded = df_expanded[
                (df_expanded['lduration'] >= quantiles.loc[0.05, 'lduration']) & (
                        df_expanded['lduration'] <= quantiles.loc[0.95, 'lduration']) &
                (df_expanded['lreceive'] >= quantiles.loc[0.05, 'lreceive']) & (
                        df_expanded['lreceive'] <= quantiles.loc[0.95, 'lreceive']) &
                (df_expanded['lsend'] >= quantiles.loc[0.05, 'lsend']) & (
                        df_expanded['lsend'] <= quantiles.loc[0.95, 'lsend'])
                ]
            df_data_his = df_data[df_data['server_name'] == sn].copy()
            df_data_his['group'] = df_data_his.groupby(['csrcip', 'cdstip', 'idstport', 'ssl_ja3', 'd']).ngroup()
            df_data_his = df_data_his.groupby('group').filter(lambda x: len(x) >= 5)
            # 计算每列的5th和95th百分位数
            quantiles2 = df_data_his[q_cols].quantile([0.05, 0.95])

            # 过滤数据，只保留在5th到95th百分位数之间的值
            df_data_his = df_data_his[
                (df_data_his['lduration'] >= quantiles2.loc[0.05, 'lduration']) & (
                        df_data_his['lduration'] <= quantiles2.loc[0.95, 'lduration']) &
                (df_data_his['lreceive'] >= quantiles2.loc[0.05, 'lreceive']) & (
                        df_data_his['lreceive'] <= quantiles2.loc[0.95, 'lreceive']) &
                (df_data_his['lsend'] >= quantiles2.loc[0.05, 'lsend']) & (
                        df_data_his['lsend'] <= quantiles2.loc[0.95, 'lsend'])
                ]

            df_all = pd.concat([df_expanded[all_cols], df_data_his[all_cols]])
            scaler = StandardScaler()
            df_all = df_all.dropna()
            df_all[['lduration', 'lreceive', 'lsend']] = scaler.fit_transform(
                df_all[['lduration', 'lreceive', 'lsend']])
            try:
                count_groups_dic = count_groups_inside_spheres_m1(df_all, logger=self._logger)
            except Exception as e:
                self._logger.get_log().info(f"{e}")
                count_groups_dic = {-1: -1}
            count_groups_values = list(count_groups_dic.values())
            count_groups_values = np.array(count_groups_values).reshape(-1, 1)
            # labels 1 if values < 3 else 0
            labels = np.where((count_groups_values >= 0) & (count_groups_values < 3), 1, 0)
            suspicious_groups = [group for group, label in zip(count_groups_dic.keys(), labels) if label != 0]
            if suspicious_groups:
                suspicious_df = df_data_obs[df_data_obs['group'].isin(suspicious_groups)][all_cols]
            else:
                # suspicious_df = pd.DataFrame(columns=df_expanded.columns)
                suspicious_df = pd.DataFrame(columns=all_cols)
            res_df = pd.concat([suspicious_df, res_df])
        return res_df
