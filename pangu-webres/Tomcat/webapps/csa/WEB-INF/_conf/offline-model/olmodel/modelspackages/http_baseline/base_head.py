import numpy as np
from .base_anomaly import HTTPBaseline
from .base_function import modify_sigmoid
from .value_baseline import ValueBaseline


class HeadBase(HTTPBaseline):

    def __init__(self, http_host, idstport):
        super().__init__(http_host, idstport)

        self.keys = None
        self.all_keys = set()
        self.value_detector = {}

    def fit(self, data, field):
        head_data = data.loc[:, field]
        parse_data = head_data.apply(lambda x: self.parse(x))
        self.keys = parse_data.apply(lambda x: set(x.keys()))

        keys_uniq = self.keys.drop_duplicates()
        for k in keys_uniq:
            self.all_keys = self.all_keys.union(k)

        for k in self.all_keys:
            value_data = parse_data.apply(lambda x: x.get(k, np.nan)).dropna()
            if value_data.empty:
                continue

            model = ValueBaseline(field=field, key=k).fit(value_data)
            self.value_detector[k] = model

        return self

    @staticmethod
    def parse(x):
        if x in ["null", ""]:
            return {}

        r = {}
        x = eval(x)
        for i in x:
            r[i["name"]] = i["value"]
        return r

    def predict(self, data, field):
        """
        检测请求头和响应头是否是异常的
        :param data: List [[{"name": "Host", "value": "xxx"}, {"name": "User-Agent", "value": "xxx"}, ...], ...]
        :param field: 字段名 crequesthead or cresponsehead
        :return: 异常分数
        """
        args_keys_anomaly_score = 0
        args_values_anomaly_score = 0

        for i in data:
            d = {}
            for ii in i:
                d[ii["name"]] = ii["value"]

            args_keys = d.keys()
            baseline_args_keys = self.keys.apply(
                lambda x: len(set(args_keys) - x)
            )
            args_keys_anomaly_score += modify_sigmoid((baseline_args_keys == 0).sum())

            for k in d:
                v = d[k]
                m = self.value_detector.get(k, None)

                if m is not None:
                    args_values_anomaly_score += m.predict(v)
                else:
                    pass   # 历史基线中没有训练这个k对应的基线

        return args_keys_anomaly_score + args_values_anomaly_score
