# -*- coding:utf-8 -*-

"""
 @Description: 全局变量管理模块 目前只管理log_level(日志级别)
 @Time: 2020/11/16 11:32 上午
 @Author: XiangJingjing
"""
# 默认的log_level为info
_global_dict = {'log_level': 'debug'}


def _init():  # 初始化
    global _global_dict
    _global_dict = {}


def set_value(key, value):
    """ 定义一个全局变量 """
    _global_dict[key] = value


def get_value(key):
    """ 获得一个全局变量,不存在则返回None"""
    return _global_dict.get(key)
