import unittest
from olmodel.models.BaseTestCase import BaseTestCase
from olmodel.models.ResignedEmployeeLogin import ResignedEmployeeLogin


class ResignedEmployeeLoginTestCase(unittest.TestCase):

    def __init__(self, *args, **kwargs):
        super(ResignedEmployeeLoginTestCase, self).__init__(*args, **kwargs)
        btc = BaseTestCase()
        self._test_case = ResignedEmployeeLogin(btc._ck_handler, btc._mysql_handler, None)

    def test_get_data(self):
        self._test_case.get_data()


if __name__ == '__main__':
    unittest.main()
