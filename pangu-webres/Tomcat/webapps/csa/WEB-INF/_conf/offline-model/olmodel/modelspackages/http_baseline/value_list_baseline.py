"""参数为List类型"""


class ListValueBaseline:

    def __init__(self, field, key):
        """
        用于对参数的参数值属于列表进行拟合基线
        Parameters
        ----------
        field : str 参数来源的字段
        key : str 参数
        """
        self.field = field
        self.key = key
        self.list_len_range = None

    def fit(self, data):
        v_ = data.map(lambda x: len(x) if isinstance(x, list) else 0)
        max_len = max(v_)
        self.list_len_range = {"max": max_len, "min": None}
        return self

    def predict(self, data):
        if isinstance(data, list):
            if len(data) > self.list_len_range["max"]:
                return 0.5
            else:
                return 0
        else:
            return 1
