# -*- coding:utf-8 -*-
# import os
# import copy
# import numpy as np
# import pandas as pd
# from datetime import datetime, timezone, timedelta
from olmodel.exceptions.ParamException import ParamException
from olmodel.models.BaseModel import BaseModel
# from olmodel.modelspackages.outlier.models.pot import POT
from olmodel.sinks.KafkaSender import KafkaSender


class Recommendation(BaseModel):

    def __init__(self, ck_handler, model_config, ts_window=1440, object_type="ip", name="风险智能推荐", **kwargs):
        super().__init__(ck_handler)
        self._model_config = model_config
        self._ts_window = ts_window
        self._object_type = object_type
        self._name = name

    def get_data(self):
        sql = ""

        if self._object_type == "ip":
            sql = """
            select csrcip as ip, '' as userId, 'ip' as ueType, round(max(riskScore), 0) as riskScore, 
                max(riskLevel) as riskLevel
            from
                (
                select distinct csrcip, 100 as riskScore, 3 as riskLevel
                from pangu.alert
                where timeCreated >= toUnixTimestamp(subtractHours(now(), 24)) * 1000
                and direction in (1, 2)
                and (alertType1 = 'EqUIzUrCGMYqHkmgdy7gY' or compromiseState = 3)
                union all
                select csrcip, riskScore, 3 as riskLevel
                from
                    (
                    select csrcip, max(ieventlevel) as threat_score, 80 + (threat_score/100) * 20 as riskScore
                    from pangu.mat_distributed_1018892992406103281
                    where win_start >= subtractHours(now(), 24)
                      and csrcipinner = 1
                      and ieventlevel = 5
                    group by csrcip
                    )
                union all
                select csrcip, riskScore, riskLevel
                from (
                    with tb as (
                        select num_win, csrcip, uniq(a) as n, max(threat_score) as threat_score from
                        (
                        select ceil((now() - toDateTime(timeCreated/1000)) / (24 * 60 * 60)) as num_win, csrcip, title as a, priority * 10 as threat_score
                        from pangu.alert
                        where eventdate >= today() - 14
                        and direction in (1, 2)
                        union all
                        select ceil((now() - win_start) / (24 * 60 * 60)) as num_win, csrcip, ceventname as a, ieventlevel * 10 as threat_score
                        from pangu.mat_distributed_1018892992406103281
                        where viewdate >= today() - 14
                        and csrcipinner = 1
                        )
                        where notEmpty(a) and notEmpty(csrcip)
                        group by num_win, csrcip
                    )
                    select csrcip, threat_score, n, hist_arr, length(hist_arr) as hist_length, arrayResize(hist_arr, 13, 0) as hist_padding_arr,
                        arrayReduce('stddevSampStable', hist_padding_arr) as hist_std, arrayReduce('avg', hist_padding_arr) as hist_avg,
                        arrayReduce('median', hist_padding_arr) as hist_median, 
                        if(n - hist_median > 100, 100, n - hist_median) as mutation_degree, 
                        multiIf((n <= 2), 0,
                                (n <= 5), (threat_score/100) * 50,
                                (n > hist_avg +  3 * hist_std and n >= 8), 80 + (mutation_degree/100) * 20,
                                50 + (threat_score/100) * 30) as riskScore,
                        multiIf((n <= 2), 0,
                                (n <= 5), 1,
                                (n > hist_avg +  3 * hist_std and n >= 8), 3,
                                2) as riskLevel
                    from
                        (
                        select csrcip, threat_score, n
                        from tb
                        where num_win = 1
                        ) as tb1
                        full outer join
                        (
                        select csrcip, groupArray(n) as hist_arr
                        from tb
                        where num_win > 1 and num_win < 15
                        group by csrcip
                        ) as tb2
                        using csrcip
                    )
                )
            group by csrcip
            """

        elif self._object_type == "user":
            sql = """
            select cuserid as userId, '' as ip, 'user' as ueType, round(max(riskScore), 0) as riskScore, 
                max(riskLevel) as riskLevel
            from
                (
                select distinct cuserId as cuserid, 100 as riskScore, 3 as riskLevel
                from pangu.alert
                where timeCreated >= toUnixTimestamp(subtractHours(now(), 24)) * 1000
                and priority >= 4
                and direction <> 0 and isNotNull(direction)
                and notEmpty(cuserId)
                union all
                select cuserid, riskScore, 3 as riskLevel
                from
                    (
                    select cuserid, max(ieventlevel) as threat_score, 80 + (threat_score/100) * 20 as riskScore
                    from pangu.mat_distributed_1018892992406103281
                    where win_start >= subtractHours(now(), 24)
                    and notEmpty(cuserid)
                    and ieventlevel = 5
                    group by cuserid
                    )
                union all
                select cuserid, riskScore, riskLevel
                from (
                    with tb as (
                        select num_win, cuserid, uniq(a) as n, max(threat_score) as threat_score from
                        (
                            select ceil((now() - toDateTime(timeCreated/1000)) / (24 * 60 * 60)) as num_win, cuserId as cuserid, title as a, priority * 10 as threat_score
                            from pangu.alert
                            where eventdate >= today() - 14
                            and direction <> 0 and isNotNull(direction)
                            and notEmpty(cuserId)
                            union all
                            select ceil((now() - win_start) / (24 * 60 * 60)) as num_win, cuserid, ceventname as a, ieventlevel * 10 as threat_score
                            from pangu.mat_distributed_1018892992406103281
                            where viewdate >= today() - 14
                            and notEmpty(cuserid)
                        )
                        where notEmpty(a)
                        group by num_win, cuserid
                        )
                        select cuserid, threat_score, n, hist_arr, length(hist_arr) as hist_length, arrayResize(hist_arr, 13, 0) as hist_padding_arr,
                            arrayReduce('stddevSampStable', hist_padding_arr) as hist_std, arrayReduce('avg', hist_padding_arr) as hist_avg,
                            arrayReduce('median', hist_padding_arr) as hist_median, 
                            if(n - hist_median > 100, 100, n - hist_median) as mutation_degree,
                            multiIf((n <= 2), 0,
                                    (n <= 5), (threat_score/100) * 50,
                                    (n > hist_avg +  3 * hist_std and n >= 8), 80 + (mutation_degree/100) * 20,
                                    50 + (threat_score/100) * 30) as riskScore,
                            multiIf((n <= 2), 0,
                                    (n <= 5), 1,
                                    (n > hist_avg +  3 * hist_std and n >= 8), 3,
                                    2) as riskLevel
                    from
                        (
                        select cuserid, threat_score, n
                        from tb
                        where num_win = 1
                        ) as tb1
                        full outer join
                        (
                        select cuserid, groupArray(n) as hist_arr
                        from tb
                        where num_win > 1 and num_win < 15
                        group by cuserid
                        ) as tb2
                        using cuserid
                    )
                )
            group by cuserid
            """

        self._logger.get_log().info(f'Recommendation algorithm. Executing sql:\n{sql}')
        df = self._ck_handler.get_data_func(sql)
        return df

    def detect_ip(self):
        data = self.get_data()
        if len(data) == 0:
            self._logger.get_log().info(f"此次调用没有关于ip的数据，无需发送kafka")
            return []

        return data

    def detect_user(self):
        data = self.get_data()
        if len(data) == 0:
            self._logger.get_log().info(f'此次调用没有关于user的数据，无需发送kafka')
            return []

        return data

    def run(self):

        if self._object_type not in ["ip", "user"]:
            raise ParamException(f"检测对象是{self._object_type}，必须是ip或user")

        if self._object_type == "ip":
            data = self.detect_ip()
        else:
            data = self.detect_user()

        if len(data) != 0:
            to_kafka = data.to_dict("records")
        else:
            return []

        # only for test (manual)
        # to_kafka = [
        #     {"ip": "*******", "userId": "", "ueType": "ip", "riskScore": 50, "riskLevel": 1},
        #     {"ip": "*******", "userId": "", "ueType": "ip", "riskScore": 90, "riskLevel": 3}
        # ]

        kafka_topic = "ueba-ue-abnormal-risk"
        kafka_security_protocol = 'SASL_PLAINTEXT'
        kafka_sender = KafkaSender(
            bootstrap_server=self._model_config.kafka_host,
            topic=kafka_topic,
            kafka_security_protocol=kafka_security_protocol,
            sasl_mechanism=self._model_config.kafka_mechanism,
            sasl_plain_username=self._model_config.kafka_username,
            sasl_plain_password=self._model_config.kafka_password
        )
        kafka_sender.send_batch(to_kafka)
        self._logger.get_log().info(f"成功通过kafka发送{len(to_kafka)}关于{self._object_type}数据，topic：{kafka_topic}")
        kafka_sender.close()

        return []
