import unittest
from olmodel.dbhandler.MySqlHandler import MySqlHandler


class MysqlHandlerTestCase(unittest.TestCase):

    def test_select_all(self):
        sql = "select user_account from asap_ueba_user where resignation_status = 5 or resignation_status = 6"

        host = "127.0.0.1"
        port = 3308
        user = "ENC(VyBwRDS6wCNxy8+jG1UqDA==)"  # tsoc
        password = "ENC(tfRtRIRuy+qWsedzn85D2GDmfnhM+5lENH450cjm0X0=)"  # fake.PWD.fool.4.U
        database = "tsoc"

        mysql_handler = MySqlHandler(database, host, port, user, password)

        res = mysql_handler.select_all(sql)

        user_account_set = set()

        for value in iter(res):
            print(value[0])
            user_account_set.add(value[0])


if __name__ == '__main__': unittest.main()
