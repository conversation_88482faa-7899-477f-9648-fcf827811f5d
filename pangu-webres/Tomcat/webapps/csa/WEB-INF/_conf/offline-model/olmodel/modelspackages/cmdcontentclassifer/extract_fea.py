import re, time
from olmodel.modelspackages.cmdcontentclassifer.wordquery.max_forward import compute_forward
from olmodel.modelspackages.cmdcontentclassifer.stringrandom.string_random import StrRandFeatureExtractor


def feature_vector(content):

    strRand = StrRandFeatureExtractor()

    content_tmp = content
    con_subhex_len = len(hexpattern(content_tmp)) if len(hexpattern(content_tmp)) > 0 else 1

    timepat_list, content_tmp = timepattern(content)
    timepat_strlen = sum([len(i) for i in timepat_list])

    ippat_list, content_tmp = ippattern(content_tmp)
    ippat_strlen = sum([len(i) for i in ippat_list])

    content_tmp = hexpattern(content_tmp)

    digit_list = getdigit(content_tmp)
    digit_strlen = sum([len(i) for i in digit_list])

    ascii_list = getascii(content_tmp)
    ascii_mix_list = [i for i in ascii_list if not i.isdigit()]

    word_len = 0
    for con in ascii_mix_list:
        score, word_cnt, words = compute_forward(con)
        word_len += sum([len(i) for i in words])

    timepat_pro = float(timepat_strlen) / con_subhex_len
    ippat_pro = float(ippat_strlen) / con_subhex_len
    digit_pro = float(digit_strlen) / con_subhex_len
    word_pro = float(word_len) / con_subhex_len

    if len(ascii_mix_list) <= 0:
        return [0 for i in range(18)]

    bigram_enwords_rate_mean, trigram_enwords_rate_mean, vowel_ratio_mean, digit_ratio_mean, unique_ratio_mean, gib_value_mean, cnt_pon_mean,  \
    bigram_enwords_rate_std, trigram_enwords_rate_std, vowel_ratio_std, digit_ratio_std, unique_ratio_std, gib_value_std, cnt_pon_std = strRand.run(ascii_mix_list)


    return [timepat_pro, ippat_pro, digit_pro, word_pro,
            bigram_enwords_rate_mean, trigram_enwords_rate_mean, vowel_ratio_mean, digit_ratio_mean, unique_ratio_mean, gib_value_mean, cnt_pon_mean,
            bigram_enwords_rate_std, trigram_enwords_rate_std, vowel_ratio_std, digit_ratio_std, unique_ratio_std, gib_value_std, cnt_pon_std]

def timepattern(content):
    regex = [r"((?:20|19)\d{2}[-/]\d{1,2}[-/]\d{1,2})",
             r"(\d{1,2}[-/]\d{1,2}[-/](?:20|19)\d{2})",
             r"(\d{2}:\d{2}:\d{2})"]
    time_list = []
    for reg in regex:
        tim_lis = re.findall(reg, content)
        if tim_lis:
            time_list.extend(tim_lis)
        content = re.sub(reg, ' ', content)

    return time_list, content

def ippattern(content):
    ip_list = []
    ipv4_re = re.findall(r"\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b", content)
    content = re.sub(r"\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b", '', content)
    ip_list.extend(ipv4_re)
    return ip_list, content

def hexpattern(content):
    return re.sub('\\\\x[0-9a-f][0-9a-f]', '', content)

def getascii(strs):
    return re.findall(r"([a-zA-Z0-9]{3,})", strs)


def getdigit(strs):
    return re.findall(r"([0-9]\d+)", strs)

def getdata(path):
    with open(path, 'rb') as f:
        return repr(f.read())[2:-1]


def run(path):
    data = getdata(path)
    feature = feature_vector(data)
    # print(feature)
    # print(len(feature))





if __name__ == "__main__":
    path = '../commandfile/systeminfo/systeminfo_1626333381273.txt'
    # data = getdata(path)
    # ip_list = gettime(data)
    strs = 'jsie josfe kfoeksl jfi8e jdi 21/4/2003 ifdls 2/3/1901'
    data = run(path)