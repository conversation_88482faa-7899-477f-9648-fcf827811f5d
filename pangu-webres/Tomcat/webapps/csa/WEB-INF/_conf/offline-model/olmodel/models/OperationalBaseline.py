import json
import datetime
import base64
import os.path
import pandas as pd
from olmodel.models.BaseModel import BaseModel


class OperationalBaseline(BaseModel):
    def __init__(self, ck_handler, mysql_handler, model_config, name='FlasePositive', ts_window=60,
                 **kwargs):
        super().__init__(ck_handler)
        self._ts_window = ts_window
        self._baseline_time = 14
        self._name = name
        self._time_offset = 0
        self._mysql_handler = mysql_handler
        self._model_config = model_config
        # get time (format: 220603)
        self.event_table = datetime.datetime.now().strftime('%y%m%d')
        self.event_table_yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%y%m%d')
        self._now_datetime = (datetime.datetime.now() + datetime.timedelta(minutes=self._time_offset)
                              ).strftime("%Y-%m-%d %H:%M:%S")

    def get_data(self):
        # sql
        sql_sql = """
        select csrcipinner, cdstipinner, '*' as csrcip, cdstip, idstport, '*' as ceventname 
        from 
            (
            select csrcipinner, cdstipinner, cdstip, idstport, http_uri_no_parameter, count(1) as n, uniq(csrcip) as u 
            from 
            (
                SELECT toDateTime(loccurtime / 1000) as ts, csrcipinner, cdstipinner, csrcip, cdstip, 
                isrcport, idstport, http_host, http_uri_no_parameter, http_url
                FROM {event_table}
                WHERE (1 = 1) 
                AND ts < now() AND ts  > now() - toIntervalMinute(5)
                AND (csrclogtype = '/security/IDS/HTTP') 
                and cdstipinner = 1
                and crequestbody like '%select%from%'
            )
            group by csrcipinner, cdstipinner, cdstip, idstport, http_uri_no_parameter
            having n >= 10
        )
        """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"{sql_sql}")
        df_data_sql = self._ck_handler.get_data_func(sql_sql)
        self._logger.get_log().info(f"获取到数据:{df_data_sql.shape}")

        sql_FTP = """
        select  csrcipinner, cdstipinner, csrcip, cdstip, idstport, '*' as ceventname
        from 
        (
            select  toDateTime(loccurtime / 1000) as ts, csrclogtype, ceventname, cdevip, csrcipinner, 
            cdstipinner, csrcip, cdstip, isrcport, idstport, iappprotocol, lsend, lreceive
            from {event_table}
            where 1=1
            AND (csrclogtype = '/security/IDS/Flow') 
            and csrcip || '-' || cdstip in 
            (
                select csrcip || '-' || cdstip from 
                (
                    select csrcip, cdstip from 
                    (
                        SELECT toDateTime(loccurtime / 1000) as ts, csrclogtype, ceventname,  csrcip, cdstip, isrcport, 
                        idstport, iappprotocol, lsend, lreceive
                        FROM {event_table}
                        WHERE (1 = 1) 
                        AND ts < now() AND ts  > now() - toIntervalMinute(5)
                        AND (csrclogtype = '/security/IDS/Flow') 
                        and iappprotocol = 21
                    )
                    group by csrcip, cdstip
                )
            )
            and iappprotocol in (-1, 21)
            and idstport > 1024
            and (lreceive > 0 or lsend > 0)
        )
        """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"{sql_FTP}")
        df_data_FTP = self._ck_handler.get_data_func(sql_FTP)
        self._logger.get_log().info(f"提取到数据:{df_data_FTP.shape}")

        sql_context = """
        select  csrcipinner, cdstipinner, csrcip, cdstip, idstport, '*' as ceventname 
        from 
        (
            select csrcipinner, cdstipinner, csrcip, cdstip, idstport, count(1) as n 
            from 
            (
                SELECT  toDateTime(loccurtime / 1000) as ts, csrcipinner, cdstipinner, csrcip, cdstip, isrcport, idstport, iappprotocol
                FROM {event_table}
                WHERE (1 = 1) 
                AND ts < now() AND ts  > now() - toIntervalMinute(5)
                AND (csrclogtype = '/security/IDS/HTTP') 
                and csrcip || '-' || cdstip || '-' || toString(idstport) in 
                (
                    select csrcip || '-' || cdstip || '-' || toString(idstport) from 
                    (
                        select csrcip, cdstip, idstport, groupUniqArray(ceventname) 
                        from {event_table}
                        where 1=1
                        and (csrclogtype = '/security/IDS/Alert') 
                        and startsWith(ceventtype , '/att')
                        and cdstipinner = 1
                        group by csrcip, cdstip, idstport
                    )
                )
            )
            group by csrcipinner, cdstipinner, csrcip, cdstip, idstport
            having n >= 100
        )
        """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"{sql_context}")
        df_data_context = self._ck_handler.get_data_func(sql_context)
        self._logger.get_log().info(f"提取到{df_data_context.shape}")

        # 恶意软件
        sql_malware = """
        select  csrcipinner, cdstipinner, csrcip, '*' as cdstip, '*' as idstport, ceventname from 
        (
            select ceventname, csrcipinner, cdstipinner, csrcip from 
            (
                SELECT win_start, ceventname, csrcipinner, cdstipinner, csrcip, cdstip, idstport
                FROM mat_view_1083279529822135328
                WHERE (1 = 1) 
                AND viewdate >= today()-1 
                AND win_start < now() - toIntervalMinute(30)
                and csrcipinner = 1
                and ceventname like '%恶意软件_JA3哈希%'
            )
            group by ceventname, csrcipinner, cdstipinner, csrcip
        )
        """
        self._logger.get_log().info(f"{sql_malware}")
        df_data_malware = self._ck_handler.get_data_func(sql_malware)
        self._logger.get_log().info(f"提取到{df_data_malware.shape}")

        sql_mu_ip = """
        select  csrcipinner, cdstipinner, '*' as csrcip, cdstip, idstport, ceventname from 
        (
            select ceventname, csrcipinner, cdstipinner, cdstip, idstport, uniq(csrcip) as nsrcips, countMerge(mat_indic_1083281934307566624) as n from 
            (
                SELECT  win_start, ceventname, csrcipinner, cdstipinner, csrcip, cdstip, idstport, mat_indic_1083281934307566624
                FROM mat_view_1083279529822135328
                WHERE (1 = 1) 
                AND viewdate >= today()-1 
                and cdstipinner = 1
                and startsWith(ceventtype , '/att')
                and ceventtype not in ('/att/pwd', '/att/tunnel', '/att/threat')
            )
            group by ceventname, csrcipinner, cdstipinner, cdstip, idstport
            having nsrcips >= 5 or n >= 50
        )
        """
        self._logger.get_log().info(f"{sql_mu_ip}")
        df_data_mu_ip = self._ck_handler.get_data_func(sql_mu_ip)
        self._logger.get_log().info(f"提取到{df_data_mu_ip.shape}")

        # 单个连接上有大量数据传输
        sql_data_trans = """
        select  csrcipinner, cdstipinner, csrcip, cdstip, idstport, '*' as ceventname from 
        (
            select csrcipinner, cdstipinner, csrcip, cdstip, idstport from 
            (
                SELECT toDateTime(loccurtime / 1000) as ts, csrcipinner, cdstipinner, 
                csrcip, cdstip, isrcport, idstport, iappprotocol, lsend, lreceive
                FROM {event_table}
                WHERE (1 = 1) 
                AND (csrclogtype = '/security/IDS/Flow') 
                and (lsend > 10*1024*1024 or lreceive > 10*1024*1024)
                and csrcip || '-' || cdstip || '-' || toString(idstport) in 
                (
                    select csrcip || '-' || cdstip || '-' || toString(idstport) from 
                    (
                        select csrcip, cdstip, idstport, groupUniqArray(ceventname) 
                        from {event_table}
                        where 1=1
                        and (csrclogtype = '/security/IDS/Alert') 
                        and startsWith(ceventtype , '/att')
                        and cdstipinner = 1
                        group by csrcip, cdstip, idstport
                    )
                )
            )
            group by csrcipinner, cdstipinner, csrcip, cdstip, idstport
        )
        """.format(
            event_table="event20" + self.event_table,
        )
        self._logger.get_log().info(f"{sql_data_trans}")
        df_data_trans = self._ck_handler.get_data_func(sql_data_trans)
        self._logger.get_log().info(f"提取到{df_data_trans.shape}")

        df_data_all = pd.concat(
            [df_data_sql, df_data_FTP, df_data_context, df_data_mu_ip, df_data_trans, df_data_malware]
        )
        self._logger.get_log().info(f"总计提取到数据:{df_data_all.shape}")
        return df_data_all

    def run(self):
        df_data = self.get_data()
        if len(df_data) == 0:
            self._logger.get_log().info(f"FalsePositive无数据")
            return []
        self.write2file(df_data)
        return []

    def write2file(self, df_data: pd.DataFrame):
        """
        源IP：srcIp
        目的IP：dstIp
        目的端口：dstPort
        源IP内外网：srcIpIsInner
        目的IP内外网：dstIpIsInner
        日志名称：eventName
        """
        rename_dic = {'csrcip': 'srcIp', 'cdstip': 'dstIp', 'idstport': 'dstPort',
                      'csrcipinner': 'srcIpIsInner', 'cdstipinner': 'dstIpIsInner', 'ceventname': 'eventName'}
        df_data.rename(columns=rename_dic, inplace=True)
        file_path = os.path.join(
            eval(base64.b64decode("b3MucGF0aC5kaXJuYW1lKG9zLnBhdGguZGlybmFtZShvcy5wYXRoLmRpcm5hbWUob3MucGF0aC5kaXJuYW1lKF9fZmlsZV9fKSkpKQ==").decode('utf-8')),
            base64.b64decode("YWxlcnQtdmVyaWZ5LW1vZGVs").decode('utf-8'),
            base64.b64decode("RmFsc2VQb3NpdGl2ZQ==").decode('utf-8'),
            base64.b64decode("ZmFsc2Vwb3NpdGl2ZS5qc29u").decode('utf-8')
        )
        # decode('utf-8')
        self._logger.get_log().info(f"文件路径为{file_path}")
        backup_file_path = file_path + '.bak'

        if os.path.exists(file_path):
            os.remove(file_path)
            self._logger.get_log().info("已存在文件，删除")
        df_data_dic = df_data.to_dict(orient='record')
        with open(backup_file_path, 'w', encoding='utf-8') as f:
            json.dump(df_data_dic, f, ensure_ascii=False)

        os.rename(backup_file_path, file_path)
        self._logger.get_log().info("文件写入成功")
