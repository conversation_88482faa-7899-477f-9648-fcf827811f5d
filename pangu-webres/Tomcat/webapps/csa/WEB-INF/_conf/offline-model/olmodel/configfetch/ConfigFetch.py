# -*- coding:utf-8 -*-
import codecs
import yaml
# import traceback
import os
# import sys
import json
from olmodel.configfetch.AlgorithmConfig import AlgorithmConfig
from olmodel.configfetch.ModelConfig import ModelConfig
from olmodel.exceptions.ParamException import ParamException
from olmodel.utils.ArgCheck import algorithm_args_check
from olmodel.utils.Logger import Logger


logger = Logger(__name__)


def file_path_valid(filepath):
    """判断文件路径是否有效"""
    if filepath and os.path.exists(filepath):
        return True
    return False


def fetch_model_config(filepath):
    if not file_path_valid(filepath):
        raise FileNotFoundError(f'程序相关配置文件不存在, 文件路径:{filepath}')

    func_map = {
        'log_level': 'model_config_obj.log_level',
        'time_offset': 'model_config_obj.time_offset',
        'pangu_api_address': 'model_config_obj.pangu_api_address',
        'api_username': 'model_config_obj.api_username',
        'api_secret': 'model_config_obj.api_secret',
        "anomaly_beh_topic": 'model_config_obj.anomaly_beh_topic',
        'log_path': 'model_config_obj.log_path',
        'mysql_database': 'model_config_obj.mysql_database',
        'mysql_host': 'model_config_obj.mysql_host',
        'mysql_port': 'model_config_obj.mysql_port',
        'mysql_user': 'model_config_obj.mysql_user',
        'mysql_password': 'model_config_obj.mysql_password',
        'kafka_host': 'model_config_obj.kafka_host',
        'kafka_mechanism': 'model_config_obj.kafka_mechanism',
        'kafka_username': 'model_config_obj.kafka_username',
        'kafka_password': 'model_config_obj.kafka_password'
    }

    model_config_obj = ModelConfig()
    with codecs.open(filepath, 'r', encoding='utf-8') as f:
        content = yaml.safe_load(f)
        if 'configs' not in content:
            raise ParamException(f'离线模型配置文件格式错误，必须包含模块：configs，文件路径：{filepath}')
        for item in content['configs']:
            name = item['name']
            value = item['value']
            if name not in func_map:
                raise ParamException(f'离线模型配置文件格式错误，无效参数：{name}，文件路径：{filepath}')
            exec(f'{func_map[name]} = \'{value}\'')
    return model_config_obj


def fetch_task_config_json(json_file_path):
    """
    获取程序入口的配置文件中的参数
    :param json_file_path:
    :return: dict类型的参数
    """
    if not file_path_valid(json_file_path):
        logger.get_log().error(f'程序入口传入的json文件不存在，文件路径：{json_file_path}')
        raise FileNotFoundError(f'程序入口传入的json文件不存在，文件路径：{json_file_path}')

    with codecs.open(json_file_path, 'r', encoding='utf-8') as f:
        result = json.load(f)
        return result


def fetch_algorithm_config_yaml(yaml_file_path):
    """获取算法默认参数"""
    algorithm_lib = {}
    with codecs.open(yaml_file_path, 'r', encoding='utf-8') as f:
        content = yaml.safe_load(f)
        if 'offline-model' not in content:
            raise ParamException(f'算法配置文件格式错误，必须包含模块：offline-model，文件路径：{yaml_file_path}')
        for item in content['offline-model']:
            for j in item['configs']:
                algConf = AlgorithmConfig(j['algorithm'], j['params'])
                algorithm_lib[j['algorithm']] = algConf

    return algorithm_lib


def fetch_algorithm_params(alg_name, algorithm_lib):
    if alg_name not in algorithm_lib:
        raise ParamException(f'算法名称不存在，算法名称：{alg_name}')

    result = {}
    algConfig = algorithm_lib.get(alg_name)
    for p_name, algParam in algConfig.algorithm_params.items():
        result[p_name] = algParam.value

    return result


def fetch_check_algparams(conf_file_path, alg_name, default_algorithm_lib):
    if not conf_file_path or not os.path.exists(conf_file_path):
        logger.get_log().warning(f'算法参数配置文件不存在，文件路径：{conf_file_path}')
        return None
    algorithm_lib = fetch_algorithm_config_yaml(conf_file_path)
    user_alg_params = fetch_algorithm_params(alg_name, algorithm_lib)
    result = algorithm_args_check(alg_name, user_alg_params, default_algorithm_lib)
    return result
