import os
from olmodel.utils.Logger import Logger


class FileUtil(object):
    def __init__(self):
        self._logger = Logger(__name__)

    def read_lines(self, file_path):

        if not os.path.exists(file_path):
            self._logger.get_log().info(f'FileUtil read lines, file not exist, file path: {file_path}')
            return list()

        with open(file_path, "r") as fp:
            lines = fp.readlines()

        self._logger.get_log().info(f'FileUtil read lines success, file path: {file_path}')

        return lines

    def write_lines(self, file_dir, file_name, content):
        if not os.path.isdir(file_dir):
            os.makedirs(file_dir)

        # e.g. /file_dir/file_name
        fn = os.path.join(file_dir, file_name)
        self._logger.get_log().info(f'FileUtil ready to write lines, file path: {fn}')
        with open(fn, "w") as fp:
            fp.writelines(content)

        return

    def write(self, file_dir, file_name, content):
        if not os.path.isdir(file_dir):
            os.makedirs(file_dir)

        # e.g. /file_dir/file_name
        fn = os.path.join(file_dir, file_name)
        self._logger.get_log().info(f'FileUtil ready to write, file path: {fn}')
        with open(fn, "w") as fp:
            fp.write(content)

        return
