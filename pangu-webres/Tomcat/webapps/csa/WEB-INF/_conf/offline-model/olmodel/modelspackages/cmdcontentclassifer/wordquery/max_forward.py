# -*-coding:utf-8 -*-

from olmodel.modelspackages.cmdcontentclassifer.wordquery.vocab import words_dict


def remove_non_alpha(line):
    # 要保留非字母的位置，要不然会拼接成错误单词
    chars = []
    for i in line:
        if not i.isalpha():
            chars.append('*')
        else:
            chars.append(i)
    chars = ''.join(chars)
    while '**' in chars:
        chars = chars.replace('**', '*')
    return chars.strip('*')


def compute_forward(line_origin, max_length=14, min_len=3):
    if not line_origin:
        return 0, 0, 0
    # line = ''.join([i for i in line if i.isalpha()])
    line = remove_non_alpha(line_origin.lower())
    # print(line)
    my_stack = []
    not_in_vocab = []
    while line != '':
        tryWord = line[:max_length]
        tryWord = tryWord.strip('*')
        # if tryWord == 'T':
        # print(1, tryWord)
        if len(tryWord) <= 1:
            break
        while tryWord and len(tryWord) not in words_dict or tryWord not in words_dict[len(tryWord)]:
            # print(tryWord)
            tryWord = tryWord[: len(tryWord) - 1]
            # print(2, tryWord)

            if len(tryWord) < min_len:
                # if tryWord == 'T':
                #     print(tryWord)
                if tryWord.isalpha():
                    # print(3, tryWord)
                    not_in_vocab.append(tryWord)
                break
        # if tryWord == 'T':
        #     print(tryWord)
        if tryWord not in not_in_vocab and tryWord.isalpha():
            my_stack.append(tryWord)
        line = line[len(tryWord):]
        if len(line) < 1:
            # my_stack.append(tryWord)
            break

    # print()
    # print(len(my_stack), len(not_in_vocab), my_stack)
    # word_cnt = len(my_stack) + len(not_in_vocab)
    # score = len(my_stack) / word_cnt if word_cnt else 1
    score = sum([len(i) for i in my_stack]) / len(line_origin) if line_origin else 0

    # print(my_stack)
    # print(score)
    return score, len(my_stack), my_stack


if __name__ == '__main__':
    # a = 'bug-ajaxLoadAssignedTo-259-.html'.lower()
    # a = 'shCore'.lower()
    # a = 'zh-cn.default'.lower()
    # a = 'raw'
    # a = 'lservlet;jsessionid=64b4140fb5fb6d11a5356979bfaf28d67418686fc09be607906512917c8849f0.e34QbxiRbN8Na40Ma3uRbxqTbNeTe0'
    # # a = ''.join([i for i in a if i.isalpha()])
    # a = 'login.htm;jsessionid=85EEDEBE7436D9B77719A2DD6153890A'
    # a = 'fox(12-08-09-25-24)(1)'
    # a = 'bug-view-21311'
    # a = "userRole"
    # a = 'healthyOnly=false&namespaceId=public&clientIP=**********&serviceName=DEFAULT_GROUP@@vcloud-served-market-api&udpPort=59901&encoding=UTF-8'
    # a = 'importExcelTopo'
    # a = 'getEventBatch'
    # a = 'listClassifyTree'
    a = 'allow_portal_apps'
    a = compute_forward(a.lower())
    print(a)
