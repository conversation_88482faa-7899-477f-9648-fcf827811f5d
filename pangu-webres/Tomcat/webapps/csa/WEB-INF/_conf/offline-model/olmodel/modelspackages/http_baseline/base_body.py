import base64
import numpy as np
from furl import furl
from .base_anomaly import HTTPBaseline
from .value_baseline import ValueBaseline
from .value_html_baseline import HTMLBaseline
from .value_list_baseline import ListValueBaseline


class BodyBase(HTTPBaseline):

    def __init__(self, http_host, idstport):
        super().__init__(http_host, idstport)
        self.path_counts = None
        self.parse_fun = None  # 在子类中定义
        self.parse_html = None
        self.path_detector = {}
        self.path_body_type = {}

    def fit(self, data, field):
        """
        用于拟合请求头的基线
        :param data: 根据host:port查询出来的基线数据
        :param field: 检测的字段
        :return: self
        """
        body_data = data.loc[:, ["http_url_path", field]]

        self.path_counts = body_data["http_url_path"].value_counts()
        b = self.path_counts[self.path_counts > 20]  # 不同path的数据格式不一样

        for p, d in body_data.groupby("http_url_path"):

            if p not in b.index:  # 此path的日志少于20条
                continue

            d = d[d[field] != ""]

            if d.shape[0] == 0:  # path对应的请求头全为空
                continue

            # k-v 参数名-参数值模型
            value_detector = {}
            body = d[field]

            for i in self.parse_fun:

                parse_data = body.apply(lambda x: self.parse_fun.get(i)(x))
                parse_data = parse_data[parse_data != {}]  # 返回{}说明不属于此类型
                r = parse_data.shape[0] / body.shape[0]

                if r < 0.5:
                    continue  # 此类型比例少于一半
                else:
                    keys = parse_data.apply(lambda x: set(x.keys()))
                    keys_uniq = keys.drop_duplicates()

                    all_keys = set()
                    for k in keys_uniq:
                        all_keys = all_keys.union(k)

                    for k in all_keys:
                        value_data = parse_data.apply(lambda x: x.get(k, np.nan)).dropna()
                        m = self.compute_values_baseline(field, k, value_data)
                        if m is not None:
                            value_detector[k] = m

                    self.path_body_type[p] = i
                    self.path_detector[p] = value_detector
                    break

            if self.path_body_type.get(p, None) is None and field == "cresponsebody":
                html_data = body.apply(lambda x: self.parse_html(x))
                html_data = html_data[html_data != ""]  # 返回""表示无内容或者不是html文件
                r = html_data.shape[0] / body.shape[0]
                if r > 0.5:
                    self.path_body_type[p] = "html"
                    m = HTMLBaseline(field=field, key="html_content").fit(html_data)
                    self.path_detector[p] = m

            if self.path_body_type.get(p, None) is None:
                # body没有判断出来类型，当成纯文本处理
                self.path_body_type[p] = "text"
                m = ValueBaseline(field=field, key="total").fit(body)
                self.path_detector[p] = m

        return self

    def compute_values_baseline(self, field,  k, value_data):

        if value_data.empty:
            return None

        value_type_str_num = value_data.map(
            lambda x: isinstance(x, str) or isinstance(x, int) or isinstance(x, float)
        )
        value_type_dict = value_data.map(lambda x: isinstance(x, dict))
        value_type_list = value_data.map(lambda x: isinstance(x, list))

        if value_type_str_num.any():
            model = ValueBaseline(field=field, key=k).fit(value_data)
            return model

        elif value_type_dict.any():
            d = {}

            keys_uniq = value_data.apply(lambda x: set(x.keys())).drop_duplicates()
            all_keys = set()
            for k_0 in keys_uniq:
                all_keys = all_keys.union(k_0)

            for k_r in all_keys:
                v_d = value_data.apply(lambda x: x.get(k_r, np.nan)).dropna()
                m = self.compute_values_baseline(field, k_r, v_d)
                d[k_r] = m

            return d

        elif value_type_list.any():
            model = ListValueBaseline(field=field, key=k).fit(value_data)
            return model

        return None

    def predict(self, data, field):
        """
        检测请求体和响应体是否是异常的，默认base64编码。
        :param data: dict
        请求头 -> {'http_url': '', 'crequestbody': ['b1', 'b2']}
        响应头 -> {'http_url': '', 'cresponsebody' ['b1', 'b2']}
        :param field: 字段名 crequestbody or cresponsebody
        :return: 异常分数
        """
        anomaly_score = 0

        http_url = data["http_url"]

        f = furl(http_url)
        http_url_path = f.path.__str__()
        body_type = self.path_body_type.get(http_url_path, None)

        for b in data[field]:
            # 告警中body进行编码了
            body = base64.b64decode(b).decode('utf-8', errors='ignore')

            if body_type == "text":  # 历史数据body不属于json, xml, k_v, html中的一种
                m = self.path_detector.get(http_url_path, None)
                s = m.predict(body)
                anomaly_score += s

            elif body_type == "html":
                m = self.path_detector.get(http_url_path, None)
                s = m.predict(self.parse_html(body))  # 获取html文件内容
                anomaly_score += s

            elif body_type in ["json", "xml", "k_v"]:
                parse_data = self.parse_fun.get(body_type)(body)
                m_d = self.path_detector.get(http_url_path, None)
                anomaly_score += self.compute_values_recursion(parse_data, m_d)

        return anomaly_score

    def compute_values_recursion(self, d, m_d):
        if m_d is None:
            return 0
        anomaly_score = 0
        for k in d:
            v = d.get(k, None)
            m = m_d.get(k, None)
            if isinstance(v, str) or isinstance(v, int) or isinstance(v, float) or isinstance(v, list):
                if m is None:  # 基线数据里没有此参数
                    pass
                else:
                    s = m.predict(v)
                    anomaly_score += s
            elif isinstance(v, dict):
                s = self.compute_values_recursion(v, m)
                anomaly_score += s

        return anomaly_score
