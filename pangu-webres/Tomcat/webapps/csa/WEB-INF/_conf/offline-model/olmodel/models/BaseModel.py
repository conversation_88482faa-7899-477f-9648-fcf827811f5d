# -*- coding:utf-8 -*-
import abc
from olmodel.utils.Logger import Logger


class BaseModel(object, metaclass=abc.ABCMeta):

    def __init__(self, ck_handler):
        self._ck_handler = ck_handler
        self._logger = Logger(__name__)

    def init_params(self, alg_params):
        for pname in alg_params:
            func_str = f'self._{pname}=alg_params[\'{pname}\']'
            exec(func_str)

    def get_algorithm_params(self):
        result = []
        for key, value in self.__dict__.items():
            if key not in ('_logger', ):
                result.append(f'{key}: {value}')
        return ', '.join(result)

    @abc.abstractmethod
    def run(self):
        pass
