#logging.conf
###############################################

[loggers]
keys=root,cvs,command

[logger_root]
level=DEBUG
handlers=stream

[logger_cvs]
handlers=stream,filert
level=NOTSET
#Level	Numeric value
#CRITICAL	50
#ERROR	40
#WARNING	30
#INFO	20
#DEBUG	10
#NOTSET	0
qualname=cvs
propagate=0

[logger_command]
handlers=filert
level=NOTSET
qualname=command
propagate=0

###############################################
[handlers]
keys=stream,filert
#,timedrt

[handler_stream]
class=StreamHandler
level=CRITICAL
formatter=complete
args=(sys.stdout,)

[handler_filert]
class=handlers.RotatingFileHandler
level=DEBUG
formatter=complete
args=(os.path.dirname(os.path.abspath(sys.argv[0]))+os.sep+'logs'+os.sep+'run.log', 'a', 5*1024*1024, 10,'utf-8')
#class logging.handlers.RotatingFileHandler(filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=0)

#[handler_timedrt]
#class=handlers.TimedRotatingFileHandler
#level=DEBUG
#formatter=complete
#args=('logs/check_script_datert.log', 'd', 1 , 10 )
##class TimedRotatingFileHandler(filename, when='h', interval=1, backupCount=0, encoding=None, delay=False, utc=False)


#[handler_http]
#class=handlers.HTTPHandler
#level=ERROR
#formatter=simple
#args=('localhost:8087', '/api/v1.0/log', 'POST')
###############################################

[formatters]
keys=complete,simple

[formatter_complete]
format=%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s
datefmt=

[formatter_simple]
format=%(levelname)-8s %(message)s
datefmt=
