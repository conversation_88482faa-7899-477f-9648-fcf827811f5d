select
	alert_id
	, count() as count
	, arrayStringConcat(groupArray(lids), '####') as lids
	, arrayStringConcat(groupArray(csrcip), ',') as csrcip, arrayStringConcat(groupArray(csrcipinner), ',') as csrc<PERSON>inner, arrayStringConcat(groupArray(csrcprovincename), ',') as csrcprovincename, arrayStringConcat(groupArray(csrcregion), ',') as csrcregion, arrayStringConcat(groupArray(csrclocation), ',') as csrclocation
	, arrayStringConcat(groupArray(cdstip), ',') as cdstip, arrayStringConcat(groupArray(idstport), ',') as idstport, arrayStringConcat(groupArray(cdstipinner), ',') as cdstipinner
	, arrayStringConcat(groupArray(iprotocol), ',') as iprotocol, arrayStringConcat(groupArray(iappprotocol), ',') as iappprotocol
	, arrayStringConcat(groupArray(ceventname), ',') as ceventname, arrayStringConcat(groupArray(csrclogtype), ',') as csrclogtype, arrayStringConcat(groupArray(ceventtype), ',') as ceventtype
	, arrayStringConcat(groupArray(cuseraccountname), ',') as cuseraccountname, arrayStringConcat(groupArray(cuseraccounttype), ',') as cuseraccounttype, arrayStringConcat(groupArray(cuserid), ',') as cuserid
	, arrayStringConcat(groupArray(dns_qry_name), ',') as dns_qry_name, arrayStringConcat(groupArray(dns_main_name), ',') as dns_main_name
	, arrayStringConcat(groupArray(http_host), ',') as http_host, arrayStringConcat(groupArray(http_url), '####') as http_url
	, arrayStringConcat(groupArray(server_name), ',') as server_name, arrayStringConcat(groupArray(ssl_subject), ',') as ssl_subject
	, arrayStringConcat(groupArray(ext_infos), '####') as ext_infos
	, arrayStringConcat(groupArray(loccurtime), '####') as loccurtimes
	, arrayStringConcat(groupArray(proc_id), ',') as proc_id
	, arrayStringConcat(groupArray(proc_name), ',') as proc_name
	, arrayStringConcat(groupArray(image_path), ',') as image_path
	, arrayStringConcat(groupArray(parent_proc_id), ',') as parent_proc_id
	, arrayStringConcat(groupArray(parent_proc_name), ',') as parent_proc_name
	, arrayStringConcat(groupArray(parent_process_path), ',') as parent_process_path
	, arrayStringConcat(groupArray(file_md5), ',') as file_md5
	, arrayStringConcat(groupArray(ifilesize), ',') as ifilesize
	, arrayStringConcat(groupArray(client_ip), ',') as client_ip
	, alert_title, alert_csrcip, alert_srcPort, alert_cdstip, alert_dstPort, alert_attck, alert_type, alert_type1, alert_type2, alert_priority
from
(
    with ${my_current_ms} as my_current_ms, (my_current_ms - ${edr_waiting_ms} - ${alert_win_ms}) as alert_win_start, (my_current_ms - ${edr_waiting_ms}) as alert_win_end, alert_win_start as edr_win_start
	select alert_behavior.*, edr.*
	from
	(
		select
			alert_id
			, alert_title, alert_csrcip, alert_srcPort, alert_cdstip, alert_dstPort, alert_attck, alert_type, alert_type1, alert_type2, alert_priority
			, count() as behavior_num
			, arrayStringConcat(groupArray(lid), ',') as lids
			, arrayStringConcat(groupArray(loccurtime), ',') as loccurtime
			, csrcip, csrcipinner, csrcprovincename, csrcregion, csrclocation, cdstip, idstport, cdstipinner, iprotocol, iappprotocol
			, ceventname, csrclogtype, ceventtype
			, cuseraccountname, cuseraccounttype, cuserid
			, proc_id, file_md5
			, dns_qry_name, dns_main_name
			, http_host, http_url
			, server_name, ssl_subject
			, arrayStringConcat(groupArray(ext_info), '###') as ext_infos
		from
		(
		    select
			    event.*, alerttb.*, alerttb.lid as lid
		    from
		    (
		            select lid
						, csrcip, csrcipinner, csrcprovincename, csrcregion, csrclocation
						, cdstip, idstport, cdstipinner
						, iprotocol, iappprotocol
						, ceventname, csrclogtype, ceventtype
						, cuseraccountname, cuseraccounttype, cuserid
						, proc_id, file_md5
						, dns_qry_name, dns_main_name
						, http_host, http_url
						, server_name, ssl_subject
						, toJSONString(map('crequesthead', base64Encode(crequesthead), 'crequestbody', base64Encode(crequestbody), 'cresponsehead', base64Encode(cresponsehead), 'cresponsebody', base64Encode(cresponsebody), 'payload', base64Encode(payload), 'dnsResponseAnswers', base64Encode(dns_response_answers), 'ioc', base64Encode(ioc))) as ext_info
						, loccurtime
		            from ${event_tablename}
		            where lid in
		            (
		                select toInt64(arrayJoin(splitByChar(',', splitByChar(';', eventIds)[1]))) as lid from alert_raw
						where eventdate = today() and timeCreated between alert_win_start and alert_win_end
		            )
		    )as event
		    right outer join
		    (
		            select alert_raw_id, lid, alert_id, alertRawId, alert_title, alert_csrcip, alert_srcPort, alert_cdstip, alert_dstPort, alert_attck, alert_type, alert_type1, alert_type2, alert_priority
		            from
		            (
		                select
		                	id as alert_raw_id
		                	, toInt64(arrayJoin(splitByChar(',', splitByChar(';', eventIds)[1]))) as lid
		                from alert_raw
						where eventdate = today()
							and timeCreated between alert_win_start and alert_win_end
		            )as alertrawtb
		            inner join
		            (
		                select alertId as alert_id, alertRawId from alert_relation
		                where alertRawId in
		                (
		                    select id from alert_raw
							where eventdate = today() and timeCreated between alert_win_start and alert_win_end
		                ) ${alert_ids_condition}
		            )as alertrelationtb on alertrawtb.alert_raw_id = alertrelationtb.alertRawId
		            left outer join
		            (
		            	select
		            		id
		                	, title as alert_title
		                	, csrcip as alert_csrcip, srcPort as alert_srcPort
		                	, cdstip as alert_cdstip, dstPort as alert_dstPort
		                	, attCk as alert_attck
		                	, alertType as alert_type
		                	, alertType1 as alert_type1
		                	, alertType2 as alert_type2
		                	, priority as alert_priority
		            	from alert
		            	where id in
		            	(
		            		select alertId as alert_id from alert_relation
			                where alertRawId in
			                (
			                    select id from alert_raw
								where eventdate = today() and timeCreated between alert_win_start and alert_win_end
			                ) ${alert_ids_condition}
		            	)
		            	group by id, title, csrcip, srcPort, cdstip, dstPort, attCk, alertType, alertType1, alertType2, priority
		            )as alerttmptb on alerttmptb.id = alertrelationtb.alert_id
		    )as alerttb
		    on event.lid = alerttb.lid
		)
		group by alert_id
			, alert_title, alert_csrcip, alert_srcPort, alert_cdstip, alert_dstPort, alert_attck, alert_type, alert_type1, alert_type2, alert_priority
			, csrcip, csrcipinner, csrcprovincename, csrcregion, csrclocation, cdstip, idstport, cdstipinner, iprotocol, iappprotocol
			, ceventname, csrclogtype, ceventtype
			, cuseraccountname, cuseraccounttype, cuserid
			, proc_id, file_md5
			, dns_qry_name, dns_main_name
			, http_host, http_url
			, server_name, ssl_subject
	) as alert_behavior
	left outer join
	(
		select
	        csrcip, cdstip, idstport
            , proc_id, proc_name, image_path, parent_proc_id, parent_proc_name, parent_process_path
            , file_md5, ifilesize, client_ip
		from ${event_tablename}
		where csrclogtype = '/security/Terminal/Network'
			and loccurtime between edr_win_start and my_current_ms
		group by csrcip, cdstip, idstport
            , proc_id, proc_name, image_path, parent_proc_id, parent_proc_name, parent_process_path
            , file_md5, ifilesize, client_ip
	)as edr on alert_behavior.csrcip = edr.csrcip and alert_behavior.cdstip = edr.cdstip and alert_behavior.idstport = edr.idstport
	where 1=1 ${filter_sql_condition}
)group by alert_id, alert_title, alert_csrcip, alert_srcPort, alert_cdstip, alert_dstPort, alert_attck, alert_type, alert_type1, alert_type2, alert_priority