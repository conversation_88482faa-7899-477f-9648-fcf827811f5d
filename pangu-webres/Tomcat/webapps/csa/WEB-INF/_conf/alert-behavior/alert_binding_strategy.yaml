sameBatchAlertSql: "
select * from mat_distributed_alert_behavior
where update_datetime global in
(
    select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1
    and alert_is_negative != 1
) and store = 1 and alert_is_negative != 1
"

firstBatchAlertSql: "
select * from mat_distributed_alert_behavior
where update_datetime global in
(
 	SELECT MIN(update_datetime) FROM
    mat_distributed_alert_behavior where store = 1 and alert_is_negative != 1
) and store = 1 and alert_is_negative != 1
"

multistepAttackSql1: "
SELECT * from (
    select tb1.*, tb2.incident_id as col_incident_id, tb2.behavior_id as col_behavior_id from
    (
        select *, splitByChar(',', a1) as a2, a2[1] as next_attck, a2[2] as m, a2[3] as t, a2[4] as p from
        (
            select *, arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes)) as loccurtime, multiIf(${condition_array}) as arr
            from mat_distributed_alert_behavior
            where update_datetime global in
            (
                select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
            ) and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
        ) array join arr as a1
    ) as tb1
    left outer join
    (
        with ta as (
			SELECT MAX(loccurtime) maxTime, MIN(loccurtime) minTime from (
				select toDateTime(arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes))/1000) as loccurtime from mat_distributed_alert_behavior
					where update_datetime global in
					(
						SELECT MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1 and alert_is_negative != 1 and notEmpty(csrcip) and notEmpty(cdstip)
					)
				)
		)
        select behavior_id, incident_id, alert_attck_group, csrcip, cdstip, arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes)) as loccurtime
        from mat_distributed_incident_behavior
        WHERE loccurtime > toDateTime((select minTime from ta) - INTERVAL ${timeInterval} MINUTE)
        union all
        select behavior_id, incident_id, alert_attck_group, csrcip, cdstip, arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes)) as loccurtime
        from mat_distributed_alert_behavior
        where update_datetime global in
        (
            select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
        ) and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
    ) as tb2
    on tb2.alert_attck_group = tb1.next_attck
    where tb2.loccurtime >= tb1.loccurtime and tb2.loccurtime < tb1.loccurtime + toInt64OrZero(tb1.m) and
    ((bitAnd(toInt64OrZero(tb1.t),1)>0 and tb1.csrcip = tb2.csrcip) or (bitAnd(toInt64OrZero(tb1.t),2)>0 and tb2.csrcip = tb1.cdstip) or (bitAnd(toInt64OrZero(tb1.t), 4)>0 and tb2.cdstip = tb1.cdstip))
) as tmp global inner join (SELECT distinct behavior_id from mat_distributed_incident_behavior where update_datetime > (now() - INTERVAL 1 HOUR)) as tmp2
on tmp.behavior_id = tmp2.behavior_id"

multistepAttackSql2: "
SELECT * from (
    select tb1.*, tb2.incident_id as col_incident_id, tb2.behavior_id as col_behavior_id from
    (
        select *, splitByChar(',', a1) as a2, a2[1] as pre_attck, a2[2] as m, a2[3] as t, a2[4] as p from
        (
            select *,arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes)) as loccurtime, multiIf(${condition_array}) as arr
            from mat_distributed_alert_behavior
            where update_datetime global in
            (
                select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
            ) and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
        ) array join arr as a1
    ) as tb1
    left outer join
    (
        with ta as (
			SELECT MAX(loccurtime) maxTime, MIN(loccurtime) minTime from (
				select toDateTime(arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes))/1000) as loccurtime from mat_distributed_alert_behavior
					where update_datetime global in
					(
						SELECT MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1 and alert_is_negative != 1 and notEmpty(csrcip) and notEmpty(cdstip)
					)
				)
		)
        select behavior_id, incident_id, alert_attck_group, csrcip, cdstip, arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes)) as loccurtime
        from mat_distributed_incident_behavior
        WHERE loccurtime > toDateTime((select minTime from ta) - INTERVAL ${timeInterval} MINUTE)
        union all
        select behavior_id,incident_id,alert_attck_group,csrcip,cdstip, arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes)) as loccurtime
        from mat_distributed_alert_behavior
        where update_datetime global in
        (
            select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
        ) and store = 1 and notEmpty(csrcip) and notEmpty(cdstip)
    ) as tb2
    on tb2.alert_attck_group = tb1.pre_attck
    where tb2.loccurtime <= tb1.loccurtime and tb2.loccurtime > tb1.loccurtime - toInt64OrZero(tb1.m) and
    ((bitAnd(toInt64OrZero(tb1.t),1)>0 and tb1.csrcip = tb2.csrcip) or (bitAnd(toInt64OrZero(tb1.t),2)>0 and tb2.cdstip = tb1.csrcip) or (bitAnd(toInt64OrZero(tb1.t),4)>0 and tb2.cdstip = tb1.cdstip))
) as tmp global inner join (SELECT distinct behavior_id from mat_distributed_incident_behavior where update_datetime > (now() - INTERVAL 1 HOUR)) as tmp2
on tmp.behavior_id = tmp2.behavior_id
"

attackFocusSql: "
with ta as (
SELECT MAX(loccurtime) maxTime, MIN(loccurtime) minTime from (
	select toDateTime(arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes))/1000) as loccurtime from mat_distributed_alert_behavior
		where update_datetime global in
		(
		    select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1
		)
	)
)
select toString(groupArray(tmp)) as colGroup, ${focusNames}, ${priority} as priority from
(
	select toString(behavior_id) || '-' || toString(incident_id) as tmp, ${focusNames}, toDateTime(arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes))/1000) as loccurtime from mat_distributed_alert_behavior
	where update_datetime global in
	(
	    select MIN(update_datetime) from mat_distributed_alert_behavior where update_datetime > toDateTime('${lastProcessTime}') and store = 1
	)
	and ${condition} and store = 1 and loccurtime > 0
	union all
	select toString(any(behavior_id)) || '-' || toString(incident_id) as tmp ,any(csrcip), any(loccurtime) from (
      select behavior_id,incident_id,csrcip, toDateTime(arrayMin(x -> toInt64OrZero(x), splitByChar(',', loccurtimes))/1000) as loccurtime from mat_distributed_incident_behavior
      where ${condition} and loccurtime > toDateTime((select minTime from ta) - INTERVAL ${time_win} MINUTE) and loccurtime <= (select maxTime from ta)
	) as tmp1 GROUP by incident_id
) group by ${focusNames} HAVING POSITION('-0' IN colGroup) > 0
"

incidentBehaviorNeedUpdate: "
select tb1.* from
(
select
   behavior_id ,alert_id ,incident_id ,alert_attck , alert_title,alert_priority ,alert_attck_group ,csrcip ,cdstip ,idstport ,ceventname, version
from
   mat_distributed_incident_behavior
where
   incident_id in (${incidentIds})
and alert_is_negative != 1 and store = 1
)as tb1
inner join
 (
   select
      behavior_id, max(version) version
   from
      mat_distributed_incident_behavior
   where incident_id in (${incidentIds})
      and alert_is_negative != 1 and store = 1
   group by
      behavior_id, incident_id)as tb2 on tb1.version = tb2.version and tb1.behavior_id = tb2.behavior_id
"

scoreForAlertPriority: "
select incident_id, sumIf(score, alert_priority=5) as scoreForAlertPriority5, sumIf(score, alert_priority=4) as scoreForAlertPriority4,
sumIf(score, alert_priority=3) as scoreForAlertPriority3, sumIf(score, alert_priority=2) as scoreForAlertPriority2, sumIf(score, alert_priority=1) as scoreForAlertPriority1,
scoreForAlertPriority5+scoreForAlertPriority4+scoreForAlertPriority3+scoreForAlertPriority2+scoreForAlertPriority1 as scoreForPriorityAlertSum
from
(
	select incident_id, csrcip, ceventname, alert_priority, max(score) as score from
	(
		select * from mat_distributed_incident_behavior ib2
		where 1=1
		and incident_id in (${incidentIds})
		and toString(incident_id) || '-' || toString(alert_id) || '-' || csrcip || '-' || ceventname global not in
		(
			select toString(incident_id) || '-' || toString(alert_id) || '-' || csrcip || '-' || ceventname from
			(
				select incident_id , alert_id,  csrcip ,ceventname, countIf(alert_is_negative=1) as c from mat_distributed_incident_behavior ib
				group by incident_id , alert_id, csrcip, ceventname
				having c >= 1
			)
		)
	)
	group by incident_id , csrcip, ceventname, alert_priority
)
group by incident_id
"
