<?xml version="1.0" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="2115" height="712" name="canvas-overlay" class="lf-canvas-overlay lf-drag-able" style="background-color:#ffffff" viewBox="0 0 2115 712"><g transform="matrix(1,0,0,1,423.71435546875,132.57144165039062)" style="transform: matrix(1, 0, 0, 1, -25.5, -25);"><g class="lf-base"><g><g class="lf-edge"><g><path d="M 107 364&#10;    C 207 364,&#10;    127 364,&#10;    227 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_bfe8f196-3338-4dae-91c4-847d38064b79)" marker-start="url(#marker-start-flow_bfe8f196-3338-4dae-91c4-847d38064b79)"/></g><g class="lf-edge-append"><path d="M 107 364&#10;    C 207 364,&#10;    127 364,&#10;    227 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_bfe8f196-3338-4dae-91c4-847d38064b79" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_bfe8f196-3338-4dae-91c4-847d38064b79" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 523 364&#10;    C 623 364,&#10;    543 364,&#10;    643 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_8b66bc44-4aac-4a08-b29d-63eeab53b42e)" marker-start="url(#marker-start-flow_8b66bc44-4aac-4a08-b29d-63eeab53b42e)"/></g><g class="lf-edge-append"><path d="M 523 364&#10;    C 623 364,&#10;    543 364,&#10;    643 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_8b66bc44-4aac-4a08-b29d-63eeab53b42e" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_8b66bc44-4aac-4a08-b29d-63eeab53b42e" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 756 364&#10;    C 856 364,&#10;    776 108,&#10;    876 108" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_96885417)" marker-start="url(#marker-start-flow_96885417)"/></g><g class="lf-edge-append"><path d="M 756 364&#10;    C 856 364,&#10;    776 108,&#10;    876 108" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="687.625" y="96.625" width="138.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="758" y="108" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="设备资产类型不明确">设备资产类型不明确</text></g></g><g><defs><marker id="marker-start-flow_96885417" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_96885417" refX="2" refY="0" overflow="visible" orient="-6.117638462194721" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1172 108&#10;    C 1272 108,&#10;    1222 108,&#10;    1322 108" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_7c44dc5a-eb13-4f7a-bc82-60c25bb26dbb)" marker-start="url(#marker-start-flow_7c44dc5a-eb13-4f7a-bc82-60c25bb26dbb)"/></g><g class="lf-edge-append"><path d="M 1172 108&#10;    C 1272 108,&#10;    1222 108,&#10;    1322 108" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_7c44dc5a-eb13-4f7a-bc82-60c25bb26dbb" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_7c44dc5a-eb13-4f7a-bc82-60c25bb26dbb" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 756 364&#10;    C 856 364,&#10;    776 364,&#10;    876 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_49729314)" marker-start="url(#marker-start-flow_49729314)"/></g><g class="lf-edge-append"><path d="M 756 364&#10;    C 856 364,&#10;    776 364,&#10;    876 364" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="804.625" y="352.625" width="48.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="830" y="364" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="服务器">服务器</text></g></g><g><defs><marker id="marker-start-flow_49729314" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_49729314" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1172 364&#10;    C 1272 364,&#10;    1192 364,&#10;    1292 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_c474a8f1-c3c1-49c0-ae63-7c8b12a972da)" marker-start="url(#marker-start-flow_c474a8f1-c3c1-49c0-ae63-7c8b12a972da)"/></g><g class="lf-edge-append"><path d="M 1172 364&#10;    C 1272 364,&#10;    1192 364,&#10;    1292 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_c474a8f1-c3c1-49c0-ae63-7c8b12a972da" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_c474a8f1-c3c1-49c0-ae63-7c8b12a972da" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1419 364&#10;    C 1519 364,&#10;    1439 364,&#10;    1539 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_08ba4467-f871-4fc4-be1c-3316e96cf33a)" marker-start="url(#marker-start-flow_08ba4467-f871-4fc4-be1c-3316e96cf33a)"/></g><g class="lf-edge-append"><path d="M 1419 364&#10;    C 1519 364,&#10;    1439 364,&#10;    1539 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_08ba4467-f871-4fc4-be1c-3316e96cf33a" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_08ba4467-f871-4fc4-be1c-3316e96cf33a" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1835 364&#10;    C 1935 364,&#10;    1855 364,&#10;    1955 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_14161204)" marker-start="url(#marker-start-flow_14161204)"/></g><g class="lf-edge-append"><path d="M 1835 364&#10;    C 1935 364,&#10;    1855 364,&#10;    1955 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_14161204" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_14161204" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 756 364&#10;    C 856 364,&#10;    776 620,&#10;    876 620" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_37512200)" marker-start="url(#marker-start-flow_37512200)"/></g><g class="lf-edge-append"><path d="M 756 364&#10;    C 856 364,&#10;    776 620,&#10;    876 620" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="824.125" y="608.625" width="33.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="842" y="620" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="终端">终端</text></g></g><g><defs><marker id="marker-start-flow_37512200" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_37512200" refX="2" refY="0" overflow="visible" orient="6.1176384621950195" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1172 620&#10;    C 1272 620,&#10;    1222 620,&#10;    1322 620" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_33187165)" marker-start="url(#marker-start-flow_33187165)"/></g><g class="lf-edge-append"><path d="M 1172 620&#10;    C 1272 620,&#10;    1222 620,&#10;    1322 620" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_33187165" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_33187165" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 38.5 346)"><rect x="0" y="0" width="67" height="32" stroke="#78768B" fill="#78768B" stroke-width="1" rx="3" ry="3" id="startEventId"/><g><svg x="6" y="6" width="1500" height="1500" viewBox="0 0 1500 1500"><path fill="#fff" d="M10.0022 0C4.50291 0 0 4.4738 0 10.0022C0 15.5016 4.47156 20.0022 10 20.0022C15.5284 20.0022 20 15.5307 20 10.0022C20.0022 4.50291 15.5307 0 10.0022 0ZM14.0573 10.4165L7.96462 13.9319C7.72503 14.0708 7.42723 13.8983 7.42723 13.6207V6.58755C7.42723 6.31213 7.72503 6.13972 7.96462 6.27631L14.0573 9.794C14.2947 9.93282 14.2947 10.2777 14.0573 10.4165Z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">开始</span></div></foreignObject></svg></g>g<circle cx="67" cy="32" r="8" fill="#ffffff" stroke-width="1.5" stroke="#4E5969"/><svg x="59" y="24" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#4E5969" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="开始">开始</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1953.5 346)"><rect x="0" y="0" width="67" height="32" stroke="#4E5969" fill="#4E5969" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M512 42.6496a469.3504 469.3504 0 1 1 0 938.7008A469.3504 469.3504 0 0 1 512 42.6496zM614.4 358.4H409.6a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h204.8a51.2 51.2 0 0 0 51.2-51.2V409.6a51.2 51.2 0 0 0-51.2-51.2z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">结束</span></div></foreignObject></svg></g>g</g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="结束">结束</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1320.5 90)"><rect x="0" y="0" width="67" height="32" stroke="#4E5969" fill="#4E5969" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M512 42.6496a469.3504 469.3504 0 1 1 0 938.7008A469.3504 469.3504 0 0 1 512 42.6496zM614.4 358.4H409.6a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h204.8a51.2 51.2 0 0 0 51.2-51.2V409.6a51.2 51.2 0 0 0-51.2-51.2z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">结束</span></div></foreignObject></svg></g>g</g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="结束(2)">结束(2)</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 224 294)"><rect x="0" y="0" width="296" height="136" fill="#E2F9F7" stroke="#CBE2E0" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#79BCB8" d="M762.3168 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664zM261.632 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#79BCB8" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">APP</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#79BCB8"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#79BCB8" d="M512 864.711111a56.888889 56.888889 0 1 0 0-113.777778 56.888889 56.888889 0 0 0 0 113.777778zM512 132.437333a42.666667 42.666667 0 0 1 42.268444 36.864l0.398223 5.802667v459.889778a42.666667 42.666667 0 0 1-84.935111 5.802666l-0.398223-5.802666V175.104a42.666667 42.666667 0 0 1 42.666667-42.666667z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="查询资产" style="color: rgb(121, 188, 184);">查询资产</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="查询资产">查询资产</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="查询资产">查询资产</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1290.5 346)"><rect x="0" y="0" width="127" height="32" stroke="#78768B" fill="#78768B" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M772.2496 443.7504c85.76 0 145.152 68.7616 149.1456 160.7168L921.6 614.4v298.6496a42.6496 42.6496 0 0 1-84.992 5.376l-0.3584-5.376V614.4c0-53.0432-26.4704-85.3504-64-85.3504-35.328 0-60.8256 28.6208-63.6928 76.1856l-0.3072 9.1648v128a42.6496 42.6496 0 0 1-10.8032 28.416l-4.5568 4.352-128 106.7008a42.6496 42.6496 0 0 1-58.7776-61.5936l4.1984-3.9936 112.5888-93.8496V614.4c0-93.5424 56.4736-165.5296 140.288-170.3936l9.0624-0.256zM260.2496 358.4c85.76 0 145.152 68.8128 149.1456 160.768l0.2048 9.8816v65.3824l112.64 93.8496c16.5888 13.824 20.0192 37.5808 8.8576 55.3984l-3.3792 4.7104a42.7008 42.7008 0 0 1-55.3984 8.8576l-4.7104-3.3792-128-106.7008a42.6496 42.6496 0 0 1-14.848-26.5216L324.1984 614.4v-85.3504c0-53.0432-26.4704-85.2992-64-85.2992-35.328 0-60.8256 28.5696-63.6928 76.1856l-0.3072 9.1136v384a42.6496 42.6496 0 0 1-84.992 5.376l-0.3072-5.376v-384C110.9504 432.1792 171.4688 358.4 260.2496 358.4z m512-256a149.3504 149.3504 0 1 1 0 298.6496 149.3504 149.3504 0 0 1 0-298.6496zM238.9504 59.7504a128 128 0 1 1 0 256 128 128 0 0 1 0-256z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="96" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="87" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">人工审核</span></div></foreignObject></svg></g>g<circle cx="127" cy="32" r="8" fill="#ffffff" stroke-width="1.5" stroke="#4E5969"/><svg x="119" y="24" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#4E5969" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="人工审核">人工审核</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 641.5 346)"><rect x="0" y="0" width="113" height="32" stroke="#78768B" fill="#78768B" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M456.192 59.4432c33.3312-33.28 87.3472-33.28 120.6784 0l392.192 392.192c33.3824 33.3312 33.3824 87.3984 0 120.7296l-392.192 392.192c-33.28 33.28-87.3472 33.28-120.6784 0l-392.192-392.192a85.3504 85.3504 0 0 1 0-120.7296z m56.7296 229.2736a42.6496 42.6496 0 0 0-42.6496 42.6496L470.2208 469.504H332.1856l-5.3248 0.3584a42.6496 42.6496 0 0 0 5.3248 84.992l138.0352-0.0512v138.0864l0.3584 5.376a42.6496 42.6496 0 0 0 84.992-5.376v-138.0864h138.0864l5.3248-0.3072a42.6496 42.6496 0 0 0-5.3248-84.992H555.52V331.3664l-0.3072-5.3248a42.6496 42.6496 0 0 0-42.3424-37.3248z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="82" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="72" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">判断</span></div></foreignObject></svg></g>g<circle cx="113" cy="32" r="8" fill="#ffffff" stroke-width="1.5" stroke="#4E5969"/><svg x="105" y="24" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#4E5969" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="判断(3)">判断(3)</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 873 38)"><rect x="0" y="0" width="296" height="136" fill="#E2F9F7" stroke="#CBE2E0" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#79BCB8" d="M762.3168 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664zM261.632 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#79BCB8" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">APP</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#79BCB8"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#79BCB8" d="M512 864.711111a56.888889 56.888889 0 1 0 0-113.777778 56.888889 56.888889 0 0 0 0 113.777778zM512 132.437333a42.666667 42.666667 0 0 1 42.268444 36.864l0.398223 5.802667v459.889778a42.666667 42.666667 0 0 1-84.935111 5.802666l-0.398223-5.802666V175.104a42.666667 42.666667 0 0 1 42.666667-42.666667z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="发送飞书" style="color: rgb(121, 188, 184);">发送飞书</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="主要给指定的用户发送飞书消息">主要给指定的用户发送飞书消息</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="发送飞书">发送飞书</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 873 294)"><rect x="0" y="0" width="296" height="136" fill="#E2F9F7" stroke="#CBE2E0" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#79BCB8" d="M762.3168 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664zM261.632 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#79BCB8" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">APP</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#79BCB8"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#79BCB8" d="M512 864.711111a56.888889 56.888889 0 1 0 0-113.777778 56.888889 56.888889 0 0 0 0 113.777778zM512 132.437333a42.666667 42.666667 0 0 1 42.268444 36.864l0.398223 5.802667v459.889778a42.666667 42.666667 0 0 1-84.935111 5.802666l-0.398223-5.802666V175.104a42.666667 42.666667 0 0 1 42.666667-42.666667z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="发送飞书" style="color: rgb(121, 188, 184);">发送飞书</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="主要给指定的用户发送飞书消息">主要给指定的用户发送飞书消息</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="发送飞书">发送飞书</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1536 294)"><rect x="0" y="0" width="296" height="136" fill="#EBEBEB" stroke="#E2E1E1" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#666666" d="M964.2496 827.7504c0 45.2608-35.9424 81.3568-80.64 84.992l-7.5264 0.3072H147.968c-45.568 0-83.968-33.6896-87.8592-77.824l-0.3072-7.4752V145.0496c0-45.2096 35.9424-81.3568 80.64-84.992l7.5264-0.3072H876.032c45.568 0 83.968 33.6896 87.8592 77.824l0.3072 7.4752v682.7008z m-85.2992-426.7008H145.0496v426.7008l0.256-0.3072 2.56 0.3072h728.2176l2.56-0.3072 0.3072 0.3072V401.0496z m-640-213.2992a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m145.0496 0a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m148.48 0a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#666666" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#666666" width="296" height="3"/></svg><svg x="211" y="0"><path fill="#666666" d="M0 0H82C83.6569 0 85 1.34315 85 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="211" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="63" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">子剧本</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#666666"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#666666" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="IP封禁及观察封禁记录更新子剧本" style="color: rgb(102, 102, 102);">IP封禁及观察封禁记录更新子剧本</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="IP封禁及观察封禁记录更新子剧本">IP封禁及观察封禁记录更新子剧本</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="IP封禁及观察封禁记录更新子剧本">IP封禁及观察封禁记录更新子剧本</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 873 550)"><rect x="0" y="0" width="296" height="136" fill="#EBEBEB" stroke="#E2E1E1" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#666666" d="M964.2496 827.7504c0 45.2608-35.9424 81.3568-80.64 84.992l-7.5264 0.3072H147.968c-45.568 0-83.968-33.6896-87.8592-77.824l-0.3072-7.4752V145.0496c0-45.2096 35.9424-81.3568 80.64-84.992l7.5264-0.3072H876.032c45.568 0 83.968 33.6896 87.8592 77.824l0.3072 7.4752v682.7008z m-85.2992-426.7008H145.0496v426.7008l0.256-0.3072 2.56 0.3072h728.2176l2.56-0.3072 0.3072 0.3072V401.0496z m-640-213.2992a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m145.0496 0a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m148.48 0a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#666666" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#666666" width="296" height="3"/></svg><svg x="211" y="0"><path fill="#666666" d="M0 0H82C83.6569 0 85 1.34315 85 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="211" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="63" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">子剧本</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#666666"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#666666" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="IP封禁及观察封禁记录更新子剧本" style="color: rgb(102, 102, 102);">IP封禁及观察封禁记录更新子剧本</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="IP封禁及观察封禁记录更新子剧本">IP封禁及观察封禁记录更新子剧本</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="IP封禁及观察封禁记录更新子剧本">IP封禁及观察封禁记录更新子剧本</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1320.5 602)"><rect x="0" y="0" width="67" height="32" stroke="#4E5969" fill="#4E5969" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M512 42.6496a469.3504 469.3504 0 1 1 0 938.7008A469.3504 469.3504 0 0 1 512 42.6496zM614.4 358.4H409.6a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h204.8a51.2 51.2 0 0 0 51.2-51.2V409.6a51.2 51.2 0 0 0-51.2-51.2z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">结束</span></div></foreignObject></svg></g>g</g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="结束">结束</text></g></g></g></g></g></g></svg>