<?xml version="1.0" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="2101" height="660" name="canvas-overlay" class="lf-canvas-overlay lf-drag-able" style="background-color:#ffffff" viewBox="0 0 2101 660"><g transform="matrix(1,0,0,1,423.71435546875,-71.42855834960938)" style="transform: matrix(1, 0, 0, 1, -25.5, -25);"><g class="lf-base"><g><g class="lf-edge"><g><path d="M 1821 108&#10;    C 1921 108,&#10;    1841 108,&#10;    1941 108" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_46544297)" marker-start="url(#marker-start-flow_46544297)"/></g><g class="lf-edge-append"><path d="M 1821 108&#10;    C 1921 108,&#10;    1841 108,&#10;    1941 108" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_46544297" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_46544297" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1405 364&#10;    C 1505 364,&#10;    1425 108,&#10;    1525 108" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_83207931)" marker-start="url(#marker-start-flow_83207931)"/></g><g class="lf-edge-append"><path d="M 1405 364&#10;    C 1505 364,&#10;    1425 108,&#10;    1525 108" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="1434.125" y="96.625" width="63.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="1467" y="108" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="阻断成功">阻断成功</text></g></g><g><defs><marker id="marker-start-flow_83207931" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_83207931" refX="2" refY="0" overflow="visible" orient="-6.117638462194721" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1821 364&#10;    C 1921 364,&#10;    1841 364,&#10;    1941 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_23042304)" marker-start="url(#marker-start-flow_23042304)"/></g><g class="lf-edge-append"><path d="M 1821 364&#10;    C 1921 364,&#10;    1841 364,&#10;    1941 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_23042304" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_23042304" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1405 364&#10;    C 1505 364,&#10;    1425 364,&#10;    1525 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_41511899)" marker-start="url(#marker-start-flow_41511899)"/></g><g class="lf-edge-append"><path d="M 1405 364&#10;    C 1505 364,&#10;    1425 364,&#10;    1525 364" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="1434.125" y="352.625" width="63.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="1467" y="364" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="阻断失败">阻断失败</text></g></g><g><defs><marker id="marker-start-flow_41511899" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_41511899" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 1172 364&#10;    C 1272 364,&#10;    1192 364,&#10;    1292 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_15785063)" marker-start="url(#marker-start-flow_15785063)"/></g><g class="lf-edge-append"><path d="M 1172 364&#10;    C 1272 364,&#10;    1192 364,&#10;    1292 364" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_15785063" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_15785063" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 756 568&#10;    C 856 568,&#10;    890.5 568,&#10;    990.5 568" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_12832971)" marker-start="url(#marker-start-flow_12832971)"/></g><g class="lf-edge-append"><path d="M 756 568&#10;    C 856 568,&#10;    890.5 568,&#10;    990.5 568" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="919.125" y="556.625" width="48.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="944.5" y="568" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="未命中">未命中</text></g></g><g><defs><marker id="marker-start-flow_12832971" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_12832971" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 756 568&#10;    C 856 568,&#10;    776 364,&#10;    876 364" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_50194112)" marker-start="url(#marker-start-flow_50194112)"/></g><g class="lf-edge-append"><path d="M 756 568&#10;    C 856 568,&#10;    776 364,&#10;    876 364" stroke-width="10" stroke="transparent" fill="none"/></g><g><g class="lf-line-text"><rect fill="#FFFFFF" x="824.125" y="352.625" width="33.75" height="20.75" class="lf-basic-shape" radius=""/><text text-anchor="middle" dominant-baseline="middle" x="842" y="364" fill="#8892C0" class="lf-element-text" text-width="100" overflowMode="default" font-size="15" value="命中">命中</text></g></g><g><defs><marker id="marker-start-flow_50194112" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_50194112" refX="2" refY="0" overflow="visible" orient="-4.881756296009467" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 523 568&#10;    C 623 568,&#10;    543 568,&#10;    643 568" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_80442383)" marker-start="url(#marker-start-flow_80442383)"/></g><g class="lf-edge-append"><path d="M 523 568&#10;    C 623 568,&#10;    543 568,&#10;    643 568" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_80442383" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_80442383" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g><g class="lf-edge"><g><path d="M 107 568&#10;    C 207 568,&#10;    127 568,&#10;    227 568" stroke="#1F2644" stroke-width="1" fill="none" marker-end="url(#marker-end-flow_37958679)" marker-start="url(#marker-start-flow_37958679)"/></g><g class="lf-edge-append"><path d="M 107 568&#10;    C 207 568,&#10;    127 568,&#10;    227 568" stroke-width="10" stroke="transparent" fill="none"/></g><g/><g><defs><marker id="marker-start-flow_37958679" refX="-2" refY="0" overflow="visible" orient="auto" markerUnits="userSpaceOnUse"><path/></marker><marker id="marker-end-flow_37958679" refX="2" refY="0" overflow="visible" orient="0" markerUnits="userSpaceOnUse"><path stroke="#1F2644" fill="#1F2644" stroke-width="1" transform="rotate(180)" d="M 0 0 L 10 -5 L 10 5 Z"/></marker></defs></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1939.5 90)"><rect x="0" y="0" width="67" height="32" stroke="#4E5969" fill="#4E5969" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M512 42.6496a469.3504 469.3504 0 1 1 0 938.7008A469.3504 469.3504 0 0 1 512 42.6496zM614.4 358.4H409.6a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h204.8a51.2 51.2 0 0 0 51.2-51.2V409.6a51.2 51.2 0 0 0-51.2-51.2z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">结束</span></div></foreignObject></svg></g>g</g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="结束">结束</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1522 38)"><rect x="0" y="0" width="296" height="136" fill="#E2F9F7" stroke="#CBE2E0" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#79BCB8" d="M762.3168 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664zM261.632 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#79BCB8" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">APP</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#79BCB8"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#79BCB8" d="M512 864.711111a56.888889 56.888889 0 1 0 0-113.777778 56.888889 56.888889 0 0 0 0 113.777778zM512 132.437333a42.666667 42.666667 0 0 1 42.268444 36.864l0.398223 5.802667v459.889778a42.666667 42.666667 0 0 1-84.935111 5.802666l-0.398223-5.802666V175.104a42.666667 42.666667 0 0 1 42.666667-42.666667z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="发送邮件" style="color: rgb(121, 188, 184);">发送邮件</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="主要给指定的用户发送邮件消息">主要给指定的用户发送邮件消息</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="发送邮件">发送邮件</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1290.5 346)"><rect x="0" y="0" width="113" height="32" stroke="#78768B" fill="#78768B" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M456.192 59.4432c33.3312-33.28 87.3472-33.28 120.6784 0l392.192 392.192c33.3824 33.3312 33.3824 87.3984 0 120.7296l-392.192 392.192c-33.28 33.28-87.3472 33.28-120.6784 0l-392.192-392.192a85.3504 85.3504 0 0 1 0-120.7296z m56.7296 229.2736a42.6496 42.6496 0 0 0-42.6496 42.6496L470.2208 469.504H332.1856l-5.3248 0.3584a42.6496 42.6496 0 0 0 5.3248 84.992l138.0352-0.0512v138.0864l0.3584 5.376a42.6496 42.6496 0 0 0 84.992-5.376v-138.0864h138.0864l5.3248-0.3072a42.6496 42.6496 0 0 0-5.3248-84.992H555.52V331.3664l-0.3072-5.3248a42.6496 42.6496 0 0 0-42.3424-37.3248z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="82" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="72" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">判断</span></div></foreignObject></svg></g>g<circle cx="113" cy="32" r="8" fill="#ffffff" stroke-width="1.5" stroke="#4E5969"/><svg x="105" y="24" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#4E5969" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="判断">判断</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1522 294)"><rect x="0" y="0" width="296" height="136" fill="#E2F9F7" stroke="#CBE2E0" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#79BCB8" d="M762.3168 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664zM261.632 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#79BCB8" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">APP</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#79BCB8"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#79BCB8" d="M512 864.711111a56.888889 56.888889 0 1 0 0-113.777778 56.888889 56.888889 0 0 0 0 113.777778zM512 132.437333a42.666667 42.666667 0 0 1 42.268444 36.864l0.398223 5.802667v459.889778a42.666667 42.666667 0 0 1-84.935111 5.802666l-0.398223-5.802666V175.104a42.666667 42.666667 0 0 1 42.666667-42.666667z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="发送飞书" style="color: rgb(121, 188, 184);">发送飞书</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="主要给指定的用户发送飞书消息">主要给指定的用户发送飞书消息</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="发送飞书">发送飞书</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 1939.5 346)"><rect x="0" y="0" width="67" height="32" stroke="#4E5969" fill="#4E5969" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M512 42.6496a469.3504 469.3504 0 1 1 0 938.7008A469.3504 469.3504 0 0 1 512 42.6496zM614.4 358.4H409.6a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h204.8a51.2 51.2 0 0 0 51.2-51.2V409.6a51.2 51.2 0 0 0-51.2-51.2z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">结束</span></div></foreignObject></svg></g>g</g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="结束">结束</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 873 294)"><rect x="0" y="0" width="296" height="136" fill="#E2F9F7" stroke="#CBE2E0" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#79BCB8" d="M762.3168 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664zM261.632 64a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z m0 500.6336a197.6832 197.6832 0 1 0 0 395.3664 197.6832 197.6832 0 0 0 0-395.3664z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#79BCB8" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#79BCB8" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">APP</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#79BCB8"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#79BCB8" d="M512 864.711111a56.888889 56.888889 0 1 0 0-113.777778 56.888889 56.888889 0 0 0 0 113.777778zM512 132.437333a42.666667 42.666667 0 0 1 42.268444 36.864l0.398223 5.802667v459.889778a42.666667 42.666667 0 0 1-84.935111 5.802666l-0.398223-5.802666V175.104a42.666667 42.666667 0 0 1 42.666667-42.666667z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="旁路阻断" style="color: rgb(121, 188, 184);">旁路阻断</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="旁路阻断">旁路阻断</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="旁路阻断">旁路阻断</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 641.5 550)"><rect x="0" y="0" width="113" height="32" stroke="#78768B" fill="#78768B" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M456.192 59.4432c33.3312-33.28 87.3472-33.28 120.6784 0l392.192 392.192c33.3824 33.3312 33.3824 87.3984 0 120.7296l-392.192 392.192c-33.28 33.28-87.3472 33.28-120.6784 0l-392.192-392.192a85.3504 85.3504 0 0 1 0-120.7296z m56.7296 229.2736a42.6496 42.6496 0 0 0-42.6496 42.6496L470.2208 469.504H332.1856l-5.3248 0.3584a42.6496 42.6496 0 0 0 5.3248 84.992l138.0352-0.0512v138.0864l0.3584 5.376a42.6496 42.6496 0 0 0 84.992-5.376v-138.0864h138.0864l5.3248-0.3072a42.6496 42.6496 0 0 0-5.3248-84.992H555.52V331.3664l-0.3072-5.3248a42.6496 42.6496 0 0 0-42.3424-37.3248z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="82" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="72" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">判断</span></div></foreignObject></svg></g>g<circle cx="113" cy="32" r="8" fill="#ffffff" stroke-width="1.5" stroke="#4E5969"/><svg x="105" y="24" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#4E5969" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="判断">判断</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 988.5 550)"><rect x="0" y="0" width="67" height="32" stroke="#4E5969" fill="#4E5969" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="6" y="6" width="32" height="32" viewBox="0 0 1500 1500"><path fill="#fff" d="M512 42.6496a469.3504 469.3504 0 1 1 0 938.7008A469.3504 469.3504 0 0 1 512 42.6496zM614.4 358.4H409.6a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h204.8a51.2 51.2 0 0 0 51.2-51.2V409.6a51.2 51.2 0 0 0-51.2-51.2z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">结束</span></div></foreignObject></svg></g>g</g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="结束">结束</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 224 498)"><rect x="0" y="0" width="296" height="136" fill="#D8DCFF" stroke="#D4D7EF" stroke-width="1" rx="3" ry="3" id=""/><g><svg x="15" y="6" width="34" height="34" viewBox="0 0 1500 1500"><path fill="#8A91D4" d="M456.8576 417.792a42.7008 42.7008 0 0 1 3.7888 56.0128l-3.7888 4.352-55.1936 55.1936 88.9856 88.9856 55.1936-55.1936a42.6496 42.6496 0 0 1 64.1536 56.0128l-3.84 4.352-55.1424 55.1424 12.4928 12.4928a42.7008 42.7008 0 0 1 3.84 56.0128l-3.84 4.352-64 64c-59.904 59.904-174.4896 83.3024-261.632 26.9824L158.1056 926.208a42.6496 42.6496 0 0 1-64.1536-55.9616l3.84-4.352 79.6672-79.6672c-54.272-84.1216-34.5088-193.6384 20.736-255.1296l6.2464-6.5536 64-64a42.7008 42.7008 0 0 1 55.9616-3.84l4.4032 3.84 12.4416 12.4928L396.4928 417.792a42.6496 42.6496 0 0 1 60.3648 0z m469.2992-320a42.7008 42.7008 0 0 1 3.84 56.0128l-3.84 4.352-79.6672 79.6672c54.272 84.1216 34.5088 193.5872-20.6848 255.0784l-6.2976 6.6048-64 64a42.7008 42.7008 0 0 1-55.9616 3.84l-4.4032-3.84-234.6496-234.6496a42.7008 42.7008 0 0 1-3.84-56.0128l3.84-4.352 64-64c59.904-59.904 174.4896-83.3024 261.632-26.9824l79.7184-79.6672a42.6496 42.6496 0 0 1 60.3136 0z"/></svg><svg x="1" y="36"><rect fill="#ffffff" width="294" height="54" rx="3" ry="3"/></svg><svg x="1" y="50"><rect fill="#ffffff" width="294" height="82"/></svg><svg x="0" y="130"><rect fill="#8A91D4" width="296" height="6" rx="3" ry="3"/></svg><svg x="0" y="130"><rect fill="#8A91D4" width="296" height="3"/></svg><svg x="241" y="0"><path fill="#8A91D4" d="M0 0H52C53.6569 0 55 1.34315 55 3V20H25C18.7049 20 12.7771 17.0361 9 12L0 0Z"/></svg><svg x="241" y="0"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="15" y="0" width="34" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: white;">API</span></div></foreignObject></svg><circle cx="296" cy="136" r="8" fill="#ffffff" stroke-width="1.5" stroke="#8A91D4"/><svg x="288" y="128" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#8A91D4" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><svg x="45" y="6" width="195" height="24" xmlns="http://www.w3.org/2000/svg"><style>.title {
                    height:24px;
                    font-size: 16px;
                    font-weight:600;
                    line-height:24px;
                    max-width: 195px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="195" height="24"><div xmlns="http://www.w3.org/1999/xhtml"><span class="title" title="威胁情报命中次数" style="color: rgb(138, 145, 212);">威胁情报命中次数</span></div></foreignObject></svg><svg x="15" y="45" width="269" height="72" xmlns="http://www.w3.org/2000/svg"><style>.text {
                    height:72px;
                    font-size: 14px;
                    line-height:24px;
                    max-width: 269px;
                    font-weight:600;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                  }</style><foreignObject x="0" y="0" width="269" height="72"><div xmlns="http://www.w3.org/1999/xhtml"><span class="text" title="威胁情报命中次数">威胁情报命中次数</span></div></foreignObject></svg></g></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="威胁情报命中次数">威胁情报命中次数</text></g></g></g></g><g class="lf-node lf-node-default "><g class="lf-node-content"><g><g transform="matrix(1 0 0 1 38.5 550)"><rect x="0" y="0" width="67" height="32" stroke="#78768B" fill="#78768B" stroke-width="1" rx="3" ry="3" id="startEventId"/><g><svg x="6" y="6" width="1500" height="1500" viewBox="0 0 1500 1500"><path fill="#fff" d="M10.0022 0C4.50291 0 0 4.4738 0 10.0022C0 15.5016 4.47156 20.0022 10 20.0022C15.5284 20.0022 20 15.5307 20 10.0022C20.0022 4.50291 15.5307 0 10.0022 0ZM14.0573 10.4165L7.96462 13.9319C7.72503 14.0708 7.42723 13.8983 7.42723 13.6207V6.58755C7.42723 6.31213 7.72503 6.13972 7.96462 6.27631L14.0573 9.794C14.2947 9.93282 14.2947 10.2777 14.0573 10.4165Z"/></svg></g><g><svg x="30" y="1"><rect fill="#ffffff" width="36" height="30" rx="3" ry="3"/></svg><svg x="29" y="1"><rect fill="transparent" width="3" height="31"/></svg><svg x="36" y="6"><style>.tag {
                    display: block;
                    text-align: center;
                    height:20px;
                    font-size: 12px;
                    font-weight:400;
                    line-height:20px;
                  }</style><foreignObject x="0" y="0" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml"><span class="tag" style="color: rgb(136, 146, 192);">开始</span></div></foreignObject></svg></g>g<circle cx="67" cy="32" r="8" fill="#ffffff" stroke-width="1.5" stroke="#4E5969"/><svg x="59" y="24" width="16" height="16" viewBox="0 0 1024 1024"><path fill="#4E5969" d="M798.848 285.248a48 48 0 0 1 71.04 64.192l-4.736 5.312-426.688 407.232a48 48 0 0 1-60.416 4.8l-5.888-4.8-213.312-203.648a48 48 0 0 1 60.8-73.92l5.504 4.48 180.16 172.032 393.6-375.68z"/></svg></g><g><text text-anchor="middle" dominant-baseline="middle" x="-2000" y="-2000" fill="currentColor" class="lf-text-disabled" color="#000000" overflowMode="default" lineHeight="1.2" font-size="12" value="开始">开始</text></g></g></g></g></g></g></svg>