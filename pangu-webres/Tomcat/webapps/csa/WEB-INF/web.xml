<?xml version="1.0" encoding="UTF-8"?>
<web-app version="3.0" xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">

    <display-name>PANGU</display-name>

    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

    <session-config>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>

    <!-- <jsp-config>
        <jsp-property-group>
            <display-name>JSPConfiguration</display-name>
            <url-pattern>*.jsp</url-pattern>
            <page-encoding>UTF-8</page-encoding>
        </jsp-property-group>
        <jsp-property-group>
            <description>html encoding</description>
            <display-name>HTMLEncodingConfiguration</display-name>
            <url-pattern>*.html</url-pattern>
            <el-ignored>true</el-ignored>
            <page-encoding>UTF-8</page-encoding>
            <scripting-invalid>false</scripting-invalid>
        </jsp-property-group>
    </jsp-config> -->

    <security-constraint>
        <web-resource-collection>
            <web-resource-name>RestHttpMethods</web-resource-name>
            <url-pattern>/api/*</url-pattern>
            <http-method-omission>GET</http-method-omission>
            <http-method-omission>POST</http-method-omission>
            <http-method-omission>PUT</http-method-omission>
            <http-method-omission>DELETE</http-method-omission>
            <http-method-omission>OPTIONS</http-method-omission>
            <http-method-omission>PATCH</http-method-omission>
        </web-resource-collection>
        <web-resource-collection>
            <web-resource-name>AllowedHttpMethods</web-resource-name>
            <url-pattern>/*</url-pattern>
            <http-method-omission>GET</http-method-omission>
            <http-method-omission>POST</http-method-omission>
        </web-resource-collection>
        <auth-constraint/>
    </security-constraint>

    <error-page>
        <error-code>203</error-code>
        <location>/error/203.jsp</location>
    </error-page>
    <error-page>
        <error-code>401</error-code>
        <location>/login.jsp</location>
    </error-page>
    <error-page>
        <error-code>403</error-code>
        <location>/error/unauth.jsp</location>
    </error-page>
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.jsp</location>
    </error-page>
    <error-page>
        <error-code>420</error-code>
        <location>/error/cluster_timeout.jsp</location>
    </error-page>
    <error-page>
        <error-code>421</error-code>
        <location>/implic.jsp</location>
    </error-page>
    <error-page>
        <error-code>422</error-code>
        <location>/error/error.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/error/error.jsp</location>
    </error-page>

</web-app>
