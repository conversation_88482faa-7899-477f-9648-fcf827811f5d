# DEBUG_LEVEL=<debuglevel>
DEBUG_LEVEL="0"

#
# FW1 configuration settings
#
# FW1_LOGFILE=<Name of FW1-Logfilename>
FW1_LOGFILE="fw.log"

# FW1_OUTPUT=<files|logs>
FW1_OUTPUT="logs"

# FW1_TYPE=<ng|2000>
FW1_TYPE="ng"

# FW1_MODE=<audit|normal>
FW1_MODE="normal"

# ONLINE_MODE=<yes|no>
ONLINE_MODE="no"

# RESOLVE_MODE=<yes|no>
RESOLVE_MODE="no"

# SHOW_FIELDNAMES=<yes|no>
SHOW_FIELDNAMES="yes"

# RECORD_SEPARATOR=<char>
RECORD_SEPARATOR="|"

# DATEFORMAT=<cp|unix|std>
#   cp   = " 3Feb2004 14:15:16"
#   unix = "1051655431"
#   std  = "2004-02-03 14:15:16"
DATEFORMAT="std"

# LOGGING_CONFIGURATION=<screen|file|syslog|odbc>
# syslog mode is only Unix like Operating Systems, such as Linux, Solaris
LOGGING_CONFIGURATION=screen

# OUTPUT_FILE_PREFIX=<Path and Name of outputfile>
OUTPUT_FILE_PREFIX="fw1-loggrabber"

# OUTPUT_FILE_ROTATESIZE=<maximum size of outputfile in bytes>
OUTPUT_FILE_ROTATESIZE=1048576

# SYSLOG_FACILITY=<USER|LOCAL0|...|LOCAL7>
#SYSLOG_FACILITY="LOCAL1"

# ODBC_DSN=<dsn>
#ODBC_DSN=FW1-LOGGRABBER

# FW1_FILTER_RULE=<rule>
#FW1_FILTER_RULE="action=drop"

# AUDIT_FILTER_RULE=<rule>
#AUDIT_FILTER_RULE="action=accept"

# FIELDS=<field1;field2;...>
#FIELDS=loc;src;dst

