status = error

property.layoutPattern = %d <%X{traceId}> [%t] %-5p %c - %m%n

appender.console.type = Console
appender.console.name = console
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = ${layoutPattern}

appender.pangu.type = RollingFile
appender.pangu.name = appender_pangu
appender.pangu.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}pangu.log
appender.pangu.layout.type = PatternLayout
appender.pangu.layout.pattern = ${layoutPattern}
appender.pangu.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}pangu.log.%i
appender.pangu.policies.type = Policies
appender.pangu.policies.size.type = SizeBasedTriggeringPolicy
appender.pangu.policies.size.size = 20MB
appender.pangu.strategy.type = DefaultRolloverStrategy
appender.pangu.strategy.max = 5

appender.pangu_error.type = RollingFile
appender.pangu_error.name = appender_pangu_error
appender.pangu_error.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}pangu_error.log
appender.pangu_error.layout.type = PatternLayout
appender.pangu_error.layout.pattern = ${layoutPattern}
appender.pangu_error.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}pangu_error.log.%i
appender.pangu_error.policies.type = Policies
appender.pangu_error.policies.size.type = SizeBasedTriggeringPolicy
appender.pangu_error.policies.size.size = 5MB
appender.pangu_error.strategy.type = DefaultRolloverStrategy
appender.pangu_error.strategy.max = 1
appender.pangu_error.filter.threshold.type = ThresholdFilter
appender.pangu_error.filter.threshold.level = error

appender.pangu_serviceKit.type = RollingFile
appender.pangu_serviceKit.name = appender_pangu_serviceKit
appender.pangu_serviceKit.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}pangu_serviceKit.log
appender.pangu_serviceKit.layout.type = PatternLayout
appender.pangu_serviceKit.layout.pattern = ${layoutPattern}
appender.pangu_serviceKit.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}pangu_serviceKit.log.%i
appender.pangu_serviceKit.policies.type = Policies
appender.pangu_serviceKit.policies.size.type = SizeBasedTriggeringPolicy
appender.pangu_serviceKit.policies.size.size = 5MB
appender.pangu_serviceKit.strategy.type = DefaultRolloverStrategy
appender.pangu_serviceKit.strategy.max = 1
appender.pangu_serviceKit.filter.threshold.type = ThresholdFilter
appender.pangu_serviceKit.filter.threshold.level = error

appender.license.type = RollingFile
appender.license.name = appender_license
appender.license.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}visa.log
appender.license.layout.type = PatternLayout
appender.license.layout.pattern = %d %-5p - %m%n
appender.license.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}visa.log.%d{F}
appender.license.policies.type = Policies
appender.license.policies.time.type = TimeBasedTriggeringPolicy
appender.license.policies.time.interval = 1
appender.license.policies.time.modulate = true
appender.license.strategy.type = DefaultRolloverStrategy
appender.license.strategy.max = 1

# [if hasRack monitor]
#appender.monitorbatch.type = RollingFile
#appender.monitorbatch.name = appender_monitorbatch
#appender.monitorbatch.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}monitorBatchCreate.log
#appender.monitorbatch.layout.type = PatternLayout
#appender.monitorbatch.layout.pattern = ${layoutPattern}
#appender.monitorbatch.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}monitorBatchCreate.log.%i
#appender.monitorbatch.policies.type = Policies
#appender.monitorbatch.policies.size.type = SizeBasedTriggeringPolicy
#appender.monitorbatch.policies.size.size = 10MB
#appender.monitorbatch.strategy.type = DefaultRolloverStrategy
#appender.monitorbatch.strategy.max = 1

appender.monitor.type = RollingFile
appender.monitor.name = appender_monitor
appender.monitor.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}monitor.log
appender.monitor.layout.type = PatternLayout
appender.monitor.layout.pattern = ${layoutPattern}
appender.monitor.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}monitor.log.%i
appender.monitor.policies.type = Policies
appender.monitor.policies.size.type = SizeBasedTriggeringPolicy
appender.monitor.policies.size.size = 10MB
appender.monitor.strategy.type = DefaultRolloverStrategy
appender.monitor.strategy.max = 1

appender.monitor_cloud.type = RollingFile
appender.monitor_cloud.name = appender_monitor_cloud
appender.monitor_cloud.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}monitor_cloud.log
appender.monitor_cloud.layout.type = PatternLayout
appender.monitor_cloud.layout.pattern = ${layoutPattern}
appender.monitor_cloud.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}monitor_cloud.log.%i
appender.monitor_cloud.policies.type = Policies
appender.monitor_cloud.policies.size.type = SizeBasedTriggeringPolicy
appender.monitor_cloud.policies.size.size = 10MB
appender.monitor_cloud.strategy.type = DefaultRolloverStrategy
appender.monitor_cloud.strategy.max = 1
# [endif]

# [if hasRack rule]
appender.rulehandler.type = RollingFile
appender.rulehandler.name = appender_rulehandler
appender.rulehandler.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}rulehandler.log
appender.rulehandler.layout.type = PatternLayout
appender.rulehandler.layout.pattern = ${layoutPattern}
appender.rulehandler.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}rulehandler.log.%i
appender.rulehandler.policies.type = Policies
appender.rulehandler.policies.size.type = SizeBasedTriggeringPolicy
appender.rulehandler.policies.size.size = 2MB
appender.rulehandler.strategy.type = DefaultRolloverStrategy
appender.rulehandler.strategy.max = 1
# [endif]

# [if hasRack model-engine]
appender.modelListener.type = RollingFile
appender.modelListener.name = appender_modelListener
appender.modelListener.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}modelListener.log
appender.modelListener.layout.type = PatternLayout
appender.modelListener.layout.pattern = ${layoutPattern}
appender.modelListener.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}modelListener.log.%i
appender.modelListener.policies.type = Policies
appender.modelListener.policies.size.type = SizeBasedTriggeringPolicy
appender.modelListener.policies.size.size = 2MB
appender.modelListener.strategy.type = DefaultRolloverStrategy
appender.modelListener.strategy.max = 1
# [endif]

# [if hasRack orion]
appender.orion.type = RollingFile
appender.orion.name = appender_orion
appender.orion.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}orion.log
appender.orion.layout.type = PatternLayout
appender.orion.layout.pattern = ${layoutPattern}
appender.orion.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}orion.log.%i
appender.orion.policies.type = Policies
appender.orion.policies.size.type = SizeBasedTriggeringPolicy
appender.orion.policies.size.size = 10MB
appender.orion.strategy.type = DefaultRolloverStrategy
appender.orion.strategy.max = 3
# [endif]

# 存储 thbd 日志配置
appender.thbd_ds.type = RollingFile
appender.thbd_ds.name = appender_thbd_ds
appender.thbd_ds.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}thbd_ds.log
appender.thbd_ds.layout.type = PatternLayout
appender.thbd_ds.layout.pattern = %d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.thbd_ds.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}thbd_ds.log.%i
appender.thbd_ds.policies.type = Policies
appender.thbd_ds.policies.size.type = SizeBasedTriggeringPolicy
appender.thbd_ds.policies.size.size = 5MB
appender.thbd_ds.strategy.type = DefaultRolloverStrategy
appender.thbd_ds.strategy.max = 5


appender.thbd_ceres.type = RollingFile
appender.thbd_ceres.name = appender_thbd_ceres
appender.thbd_ceres.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}thbd_ceres.log
appender.thbd_ceres.layout.type = PatternLayout
appender.thbd_ceres.layout.pattern = %d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.thbd_ceres.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}thbd_ceres.log.%i
appender.thbd_ceres.policies.type = Policies
appender.thbd_ceres.policies.size.type = SizeBasedTriggeringPolicy
appender.thbd_ceres.policies.size.size = 5MB
appender.thbd_ceres.strategy.type = DefaultRolloverStrategy
appender.thbd_ceres.strategy.max = 5

appender.thbd.type = RollingFile
appender.thbd.name = appender_thbd
appender.thbd.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}thbd.log
appender.thbd.layout.type = PatternLayout
appender.thbd.layout.pattern = %d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.thbd.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}thbd.log.%i
appender.thbd.policies.type = Policies
appender.thbd.policies.size.type = SizeBasedTriggeringPolicy
appender.thbd.policies.size.size = 10MB
appender.thbd.strategy.type = DefaultRolloverStrategy
appender.thbd.strategy.max = 5

# [if hasRack guarantee]
appender.guarantee.type = RollingFile
appender.guarantee.name = appender_guarantee
appender.guarantee.filename=${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}guarantee.log
appender.guarantee.layout.type=PatternLayout
appender.guarantee.layout.pattern=%d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.guarantee.filePattern=${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}guarantee.log.%i
appender.guarantee.policies.type=Policies
appender.guarantee.policies.size.type=SizeBasedTriggeringPolicy
appender.guarantee.policies.size.size=5MB
appender.guarantee.strategy.type=DefaultRolloverStrategy
appender.guarantee.strategy.max=1
# [endif]
# [if hasRack threat_intelligence]
appender.ti.type=RollingFile
appender.ti.name=appender_threat_intelligence
appender.ti.filename=${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}threat_intelligence.log
appender.ti.layout.type=PatternLayout
appender.ti.layout.pattern=%d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.ti.filePattern=${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}threat_intelligence.log.%i
appender.ti.policies.type=Policies
appender.ti.policies.size.type=SizeBasedTriggeringPolicy
appender.ti.policies.size.size=5MB
appender.ti.strategy.type=DefaultRolloverStrategy
appender.ti.strategy.max=1
# [endif]
# [if hasRack website_monitor]
appender.websitemonitor.type=RollingFile
appender.websitemonitor.name=appender_websitemonitor
appender.websitemonitor.filename=${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}websitemonitor.log
appender.websitemonitor.layout.type=PatternLayout
appender.websitemonitor.layout.pattern=%d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.websitemonitor.filePattern=${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}websitemonitor.log.%i
appender.websitemonitor.policies.type=Policies
appender.websitemonitor.policies.size.type = SizeBasedTriggeringPolicy
appender.websitemonitor.policies.size.size = 5MB
appender.websitemonitor.strategy.type = DefaultRolloverStrategy
appender.websitemonitor.strategy.max = 1
# [endif]

appender.violation.type = RollingFile
appender.violation.name = appender_violation
appender.violation.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}violation.log
appender.violation.layout.type = PatternLayout
appender.violation.layout.pattern = ${layoutPattern}
appender.violation.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}violation.log.%i
appender.violation.policies.type = Policies
appender.violation.policies.size.type = SizeBasedTriggeringPolicy
appender.violation.policies.size.size = 10MB
appender.violation.strategy.type = DefaultRolloverStrategy
appender.violation.strategy.max = 1

# 独立存储kafka相关的日志
appender.kafka.type = RollingFile
appender.kafka.name = appender_kafka
appender.kafka.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}kafka_client.log
appender.kafka.layout.type = PatternLayout
appender.kafka.layout.pattern = %d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.kafka.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}kafka_client.log.%i
appender.kafka.policies.type = Policies
appender.kafka.policies.size.type = SizeBasedTriggeringPolicy
appender.kafka.policies.size.size = 5MB
appender.kafka.strategy.type = DefaultRolloverStrategy
appender.kafka.strategy.max = 5

#存储Tio-websocket相关日志
appender.tio.type = RollingFile
appender.tio.name = appender_tio
appender.tio.filename = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}tio.log
appender.tio.layout.type = PatternLayout
appender.tio.layout.pattern = %d [%t] %-5p %c - %maxLen{%msg}{3000}%n
appender.tio.filePattern = ${sys:catalina.base}${sys:file.separator}logs${sys:file.separator}tio.log.%i
appender.tio.policies.type = Policies
appender.tio.policies.size.type = SizeBasedTriggeringPolicy
appender.tio.policies.size.size = 5MB
appender.tio.strategy.type = DefaultRolloverStrategy
appender.tio.strategy.max = 5


rootLogger.level = info
rootLogger.appenderRef.console.ref = console
rootLogger.appenderRef.pangu.ref = appender_pangu
rootLogger.appenderRef.pangu_error.ref = appender_pangu_error


#请勿修改，否则影响产品发布
logger.ulisesbocchio.name=com.ulisesbocchio
logger.ulisesbocchio.level=warn

logger.captcha-plus.name=com.xingyuv.captcha
logger.captcha-plus.level=warn

logger.pangu_serviceKit.name = ServiceKit
logger.pangu_serviceKit.additivity = false
logger.pangu_serviceKit.level = info
logger.pangu_serviceKit.appenderRef.pangu_serviceKit.ref = appender_pangu_serviceKit

logger.license.name = com.venustech.taihe.pangu.vault.license
logger.license.additivity = false
logger.license.level = info
logger.license.appenderRef.license.ref = appender_license

# [if hasRack monitor]
#logger.monitorbatch.name =com.venustech.taihe.pangu.app.monitor.batch
#logger.monitorbatch.additivity = false
#logger.monitorbatch.level = info
#logger.monitorbatch.appenderRef.monitorbatch.ref = appender_monitorbatch

logger.monitor.name =com.venustech.taihe.pangu.app.monitor
logger.monitor.additivity = false
logger.monitor.level = info
logger.monitor.appenderRef.monitor.ref = appender_monitor

logger.monitor_cloud.name =com.venustech.taihe.pangu.app.cloud
logger.monitor_cloud.additivity = false
logger.monitor_cloud.level = info
logger.monitor_cloud.appenderRef.monitor.ref = appender_monitor_cloud
# [endif]

# [if hasRack rule]
logger.rulehandler.name = com.venustech.taihe.pangu.vault.correlation.rule.impl.handler.custom.RuleHandlerCustom
logger.rulehandler.additivity = false
logger.rulehandler.level = info
logger.rulehandler.appenderRef.console.ref = console
logger.rulehandler.appenderRef.rulehandler.ref = appender_rulehandler
# [endif]

# [if hasRack model-engine]
logger.modelListener.name = com.venustech.taihe.pangu.app.modelengine.listener
logger.modelListener.additivity = false
logger.modelListener.level = info
logger.modelListener.appenderRef.console.ref = console
logger.modelListener.appenderRef.modelListener.ref = appender_modelListener
# [endif]

# [if hasRack orion]
logger.orion.name = com.venustech.taihe.pangu.app.orion
logger.orion.additivity = false
logger.orion.level = info
logger.orion.appenderRef.console.ref = console
logger.orion.appenderRef.orion.ref = appender_orion

logger.orionVault.name = com.venustech.taihe.pangu.vault.orion
logger.orionVault.additivity = false
logger.orionVault.level = info
logger.orionVault.appenderRef.console.ref = console
logger.orionVault.appenderRef.orion.ref = appender_orion
# [endif]

# THBD 日志输出控制逻辑
logger.thbd.name = com.venustech.taihe.pangu.thbd
logger.thbd.additivity = false
logger.thbd.level = info
logger.thbd.appenderRef.console.ref = console
logger.thbd.appenderRef.thbd.ref = appender_thbd

logger.thbd_ds.name = com.venustech.taihe.pangu.thbd.ds
logger.thbd_ds.additivity = false
logger.thbd_ds.level = info
logger.thbd_ds.appenderRef.console.ref = console
logger.thbd_ds.appenderRef.thbd_ds.ref = appender_thbd_ds

logger.thbd_ceres.name = com.venustech.taihe.pangu.thbd.ceres
logger.thbd_ceres.additivity = false
logger.thbd_ceres.level = info
logger.thbd_ceres.appenderRef.console.ref = console
logger.thbd_ceres.appenderRef.thbd_ceres.ref = appender_thbd_ceres

logger.thbd_df.name = com.venustech.taihe.pangu.thbd.df
logger.thbd_df.additivity = false
logger.thbd_df.level = info
logger.thbd_df.appenderRef.console.ref = console
logger.thbd_df.appenderRef.thbd_ceres.ref = appender_thbd_ceres

logger.violation.name = com.venustech.taihe.pangu.foundation.mvc.filter.CivilViolationFilter
logger.violation.additivity = false
logger.violation.level = info
logger.violation.appenderRef.console.ref=console
logger.violation.appenderRef.violation.ref=appender_violation
# [if hasRack guarantee]
logger.guarantee.name=com.venustech.taihe.pangu.app.guarantee
logger.guarantee.additivity=false
logger.guarantee.level=info
logger.guarantee.appenderRef.console.ref=console
logger.guarantee.appenderRef.guarantee.ref=appender_guarantee
# [endif]
# [if hasRack threat_intelligence]
logger.ti.name=com.venustech.taihe.pangu.app.threat.intelligence
logger.ti.additivity=false
logger.ti.level=info
logger.ti.appenderRef.console.ref=console
logger.ti.appenderRef.ti.ref=appender_threat_intelligence
# [endif]
# [if hasRack website_monitor]
logger.websitemonitor.name=com.venustech.taihe.pangu.app.websitemonitor
logger.websitemonitor.additivity=false
logger.websitemonitor.level=info
logger.websitemonitor.appenderRef.console.ref=console
logger.websitemonitor.appenderRef.websitemonitor.ref=appender_websitemonitor
# [endif]

logger.kafka.name = org.apache.kafka
logger.kafka.additivity = false
logger.kafka.level = error
logger.kafka.appenderRef.console.ref = console
logger.kafka.appenderRef.kafka.ref = appender_kafka

logger.tio.name = org.tio
logger.tio.additivity = false
logger.tio.level = error
logger.tio.appenderRef.console.ref = console
logger.tio.appenderRef.tio.ref = appender_tio

#datacompute log
logger.datacompute.name=data.compute
logger.datacompute.level = info
logger.datacompute.appenderRef.api.ref = routingAppender
logger.datacompute.additivity = false
appender.routingAppender.type = Routing
appender.routingAppender.name = routingAppender
appender.routingAppender.routes.type = Routes
appender.routingAppender.routes.pattern = $${ctx:taskAppId},$${ctx:startTime},$${ctx:serviceType}
appender.routingAppender.routes.route1.type = Route
appender.routingAppender.routes.route1.FILE.type =File
appender.routingAppender.routes.route1.FILE.name =${ctx:taskAppId}
appender.routingAppender.routes.route1.FILE.fileName =  logs/${ctx:serviceType}/${ctx:startTime}/${ctx:taskAppId}.log
appender.routingAppender.routes.route1.FILE.layout.type = PatternLayout
appender.routingAppender.routes.route1.FILE.layout.pattern = %d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n


