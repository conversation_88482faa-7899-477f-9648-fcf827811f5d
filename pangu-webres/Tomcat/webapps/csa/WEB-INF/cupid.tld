<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN" "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">
<taglib>

	<tlib-version>1.0</tlib-version>
	<jsp-version>1.2</jsp-version>
	<short-name>cupid</short-name>
	<uri>http://www.venustech.com.com/tsoc/cupid</uri>

	<description><![CDATA[Custom tag library for this application]]></description>
	
	<tag>
		<name>setBundle</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.SetBundleTag</tag-class>
		
		<attribute>
			<name>var</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	
	<tag>
		<name>ifNotLogin</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.IfNotLogin</tag-class>
	</tag>

	<tag>
		<name>ifLogin</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.IfLogin</tag-class>
	</tag>
	<tag>
		<name>eventAttrGroup</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.EventAttrGroupTag</tag-class>
		<body-content>empty</body-content>
		
		<attribute>
			<name>list</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>plainCount</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>attrName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>attrDataToJsObj</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.AttrDataToJsObjTag</tag-class>
		<body-content>empty</body-content>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>attrDataMap</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	
	<tag>
		<name>eventAttrsToJsObj</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.EventAttrsToJsObjTag</tag-class>
		<body-content>empty</body-content>
		
		<attribute>
			<name>eventAttrsMap</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>

	<tag>
		<name>userName</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.UserNameTag</tag-class>
		<body-content>empty</body-content>
		
		<attribute>
			<name>userId</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	
	<tag>
		<name>current</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.CurrentTag</tag-class>
		<body-content>empty</body-content>
		
		<attribute>
			<name>ref</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>var</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	
	<tag>
		<name>formatDate</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.FormatDateTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateStyle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>timeStyle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pattern</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>timeZone</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>var</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	
	<tag>
		<name>setEscape</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.SetEscapeTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
		  <name>escapeXml</name>
		  <required>false</required>
		  <rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>

	<tag>
		<name>isExist</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.IsExist</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<name>var</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
		  <name>portalId</name>
		  <required>false</required>
		  <rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
		  <name>wicketId</name>
		  <required>false</required>
		  <rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>menuId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>isLicensed</name>
		<tag-class>com.venustech.taihe.pangu.foundation.mvc.taglib.IsLicensed</tag-class>
		<body-content>JSP</body-content>
		<attribute>
		  <name>var</name>
		  <required>true</required>
		  <rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
		  <name>module</name>
		  <required>true</required>
		  <rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>

	<function>
        <name>hasProperty</name>
        <function-class>com.venustech.taihe.pangu.foundation.mvc.taglib.FunctionTag</function-class>
        <function-signature>boolean hasProperty(java.lang.Object, java.lang.String)</function-signature>
    </function>
	
	<function>
		<name>isCurrentProduct</name>
		<function-class>com.venustech.taihe.pangu.foundation.mvc.taglib.FunctionTag</function-class>
		<function-signature>boolean isCurrentProduct(java.lang.Object)</function-signature>
	</function>
	
	<function>
		<name>equalsCurrent</name>
		<function-class>com.venustech.taihe.pangu.foundation.mvc.taglib.FunctionTag</function-class>
		<function-signature>boolean equalsCurrent(java.lang.String, java.lang.Object)</function-signature>
	</function>
	
	<function>
		<name>isNumber</name>
		<function-class>com.venustech.taihe.pangu.foundation.mvc.taglib.FunctionTag</function-class>
		<function-signature>boolean isNumber(java.lang.Object)</function-signature>
	</function>

</taglib>