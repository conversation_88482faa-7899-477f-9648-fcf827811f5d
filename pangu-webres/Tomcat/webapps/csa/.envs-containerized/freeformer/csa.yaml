jdk:
  enabled: true
  repository: pangu/openjdk

clickhouse:
  enabled: true
  repository: pangu/clickhouse
  resources:
    requests:
      memory: 21Gi
      cpu: 2
    limits:
      memory: 28Gi
      cpu: 6

es:
  enabled: true
  repository: pangu/elasticsearch
  resources:
    requests:
      memory: 12Gi
    limits:
      memory: 12Gi

kafka:
  enabled: true
  repository: pangu/kafka
  resources:
    requests:
      memory: 5Gi
    limits:
      memory: 5Gi

mariadb:
  enabled: true
  repository: pangu/mariadb
  resources:
    requests:
      memory: 1Gi
    limits:
      memory: 6Gi

redis:
  enabled: true
  repository: pangu/redis
  resources:
    requests:
      memory: 2Gi
    limits:
      memory: 2Gi

minio:
  enabled: false
  repository: pangu/minio
  resources:
    requests:
      memory: 1Gi
    limits:
      memory: 1Gi

neo4j:
  enabled: false
  repository: pangu/neo4j
  resources:
    requests:
      memory: 1Gi
    limits:
      memory: 1Gi

flink:
  enabled: true
  repository: pangu/flink
  resources:
    requests:
      memory: 8Gi
    limits:
      memory: 8Gi

server:
  enabled: true
  repository: pangu/tomcat
  resources:
    requests:
      memory: 8Gi
      cpu: 4
    limits:
      memory: 26Gi
      cpu: 10
  env:
  - name: JAVA_XMS
    value: "8192"
  - name: JAVA_XMX
    value: "8192"

