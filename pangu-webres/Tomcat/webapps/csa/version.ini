#产品名-自动化打包会用到
productname=TSOC-CSA

version.permit=3.0.
version.productcode=500
version.major=5
version.minor=3
version.patch=1
version.projectcode=
version.timestamp=20250718.1622

#完全客制化版本号，如设置将完全取代原版本号
version.customized=

#当前语言国家名称
language=zh_CN

#编译时间，制作安装包和升级包均需变更
updatetime=20250718.1622

## okhttp外部证书加载配置
https.cert.need=false

#依赖环境数量配置
instances.jdk=1
instances.server=1
instances.es=1
instances.mariadb=1
instances.clickhouse=1
#python变量兼容1.0版本thbrain-python-virtual-env制品 2.0中已弃用python使用py变量
instances.python=0
instances.py=1
instances.fea=0
instances.rasa=1
instances.kafka=1
instances.zk=1
instances.redis=1
instances.thbrain.algo=1
instances.thbrain.svc=1
instances.minio=0
instances.neo4j=1
instances.flink.manager.job=1
instances.flink.manager.task=0
instances.hadoop.node.name=0
instances.hadoop.node.data=0
instances.nacos=0
instances.chrome=1
instances.xfc.engine=0
instances.spark.node.master=0
instances.spark.node.slave=0
instances.hive=0
instances.seatunnel=1
instances.yarn.node.master=0
instances.yarn.node.slave=0
instances.thbrain.algo.pytorch=0
instances.thbrain.algo.tensorflow=0
instances.thbrain.svc.llm=0
instances.thbrain.llm.chatglm3_6b=0
instances.thbrain.llm.text2sql=0
instances.aikit=0
instances.thbrain.llm.taihe_security=0
instances.ragflow=0
instances.exporter.node=0
instances.prometheus=0

#服务名称配置
servicename.server=tsoc-csa
servicename.mariadb=tsoc-csa-db
servicename.es=tsoc-csa-es
servicename.clickhouse=tsoc-csa-ch
servicename.rasa=tsoc-csa-rasa
servicename.kafka=tsoc-csa-kafka
servicename.zk=tsoc-csa-zk
servicename.redis=tsoc-csa-redis
servicename.minio=tsoc-csa-minio
servicename.neo4j=tsoc-csa-neo4j
servicename.seatunnel=tsoc-csa-seatunnel
servicename.thbrain.svc=tsoc-csa-thbrain-svc
servicename.flink.manager.job=tsoc-csa-flink-job-server
servicename.flink.manager.task=tsoc-csa-flink-task-server
servicename.hadoop.node.name=tsoc-csa-hadoop-name-node
servicename.hadoop.node.data=tsoc-csa-hadoop-data-node
servicename.nacos=tsoc-csa-nacos
servicename.model.ad=tsoc-csa-ad-model
servicename.model.manager=tsoc-csa-model-manager
servicename.model.offline=tsoc-csa-offline-model
servicename.snail=tsoc-csa-snail
servicename.ueba.account=tsoc-csa-ueba-account
servicename.ueba.risk=tsoc-csa-ueba-risk
servicename.xfc.engine=tsoc-csa-xfc-engine
servicename.spark.node.master=tsoc-csa-spark-master-node
servicename.spark.node.slave=tsoc-csa-spark-slave-node
servicename.hive=tsoc-csa-hive
servicename.yarn.node.master=tsoc-csa-yarn-master-node
servicename.yarn.node.slave=tsoc-csa-yarn-slave-node
servicename.aikit=tsoc-csa-aikit
servicename.ragflow=tsoc-csa-ragflow
servicename.exporter.node=tsoc-csa-exporter-node
servicename.prometheus=tsoc-csa-prometheus


#安装后是否立即将服务注册为自启动
#仅对安装过程有效，安装完成后，运行过程中，服务是否启动需通过syscmd命令行设置和查询
service.flink.manager.job.auto=false
service.flink.manager.task.auto=false
service.hadoop.node.name.auto=false
service.hadoop.node.data.auto=false
#service.redis.auto=true
service.nacos.auto=true
service.spark.node.master.auto=false
service.spark.node.slave.auto=false
service.hive.auto=false
service.yarn.node.master.auto=false
service.yarn.node.slave.auto=false

#服务看门狗配置，不配置则默认为process模式
#service.flink.manager.job.watchdog=process/port/...
#service.flink.manager.job.watchdog.port=8080,8081/tcp,8082/udp,...
service.prometheus.pushgateway.enable=true

#各组件监测指标exporter配置，缺省地，组件默认启用exporter，只有需要禁用时才需要配置
exporter.es.disable=false
exporter.mariadb.disable=false
exporter.clickhouse.disable=false
exporter.fea.disable=false
exporter.rasa.disable=false
exporter.kafka.disable=false
exporter.zk.disable=false
exporter.redis.disable=false
exporter.minio.disable=false
exporter.neo4j.disable=false
exporter.flink.manager.disable=false
exporter.hadoop.node.disable=false
exporter.nacos.disable=false
exporter.xfc.engine.disable=false
exporter.spark.node.disable=false
exporter.hive.disable=false
exporter.seatunnel.disable=false
exporter.yarn.node.disable=false
exporter.thbrain.algo.disable=false
exporter.thbrain.svc.disable=false
exporter.thbrain.algo.pytorch.disable=false
exporter.thbrain.algo.tensorflow.disable=false
exporter.node.disable=false

#命令行配置
cmd.name=syscmd
cmd.name.root=syscmdroot

#文件系统配置
fs.user=panguadmin
fs.usergroup=panguadmin
fs.install.dirname=tsoc-csa

#端口配置
ports.server=8888
ports.server.ssl=8443
ports.server.ws=9326
ports.mariadb=3308
ports.es.http=8200-8300
ports.clickhouse.http=8123
ports.clickhouse.tcp=9000
ports.kafka=9093
ports.zk=2181
ports.minio=9527
ports.neo4j=7687
ports.exporter.node=9100
ports.exporter.mysql=9104
#防火墙开放端口配置
network.ports.allows={ports.server}/tcp,{ports.server.ssl}/tcp,80/tcp,8818/tcp,514/tcp,514/udp,8514/tcp,8514/udp,9514/tcp,9514/udp,9515/tcp,9515/udp,8123/tcp,3308/tcp,8200/tcp,9000/tcp,8890/tcp,8890/udp,8889/tcp,8889/udp,162/udp,6162/udp,8515/tcp,8515/udp,8516/tcp,8516/udp,9093/udp,9093/tcp,9999/tcp,9999/udp,5001/tcp,6124/tcp,30391/tcp,41523/tcp,50300-50304/tcp,8848/tcp,8848/udp,7848/tcp,7848/udp,9848/tcp,9848/udp,9849/tcp,9849/udp,9020/tcp,9020/udp,9326/tcp,9104/tcp,6379/tcp
#端口转发配置
network.ports.forward=514->9514/tcp,514->9514/udp,162->6162/udp
#gpt智能体小模型名称
modelname=

#manufactor.key=

#============== Contents Below Deprecated =============
#当前版本-自动化打包会用到
##version=3.0.20.0
#升级补丁版本号
##updateversion=

##CUPID=T_4,M_3,J_1,E_3
##WINGCMD=1.3.6.2
#正对硬件
#WINGOS=2.0

#操作系统位数(32/64)
##os=64
#数据库类型(mysql|oracle,打包的时候，自动生成包名)
##DB=mysql

#ES配置信息
#ES版本
##esversion=6.8
#ES节点数
##esservers=1
#ES数量锁，0-不锁定，可以修改ES数量，1-锁定，不可修改
##esnumlock=0
#kafka配置信息
##kafkas=0
#zookeeper配置信息
##zkservers=0
#clickhouse配置信息
##ckservers=0
#schema配置信息
##schemas=0
#ksql配置信息
##ksqls=0
#connector配置信息
##connectors=0

#主服务名称
##servername=
#数据库服务名称
##dbname=
#ES服务名称
##indexname=
#kafka服务名称
##kafkaname=
#zookeeper服务名称
##zkname=
#clickhouse服务名称
##ckname=
#schema服务名称
##schemaname=
#ksql服务名称
##ksqlname=
#connector服务名称
##connectname=

# 是否有服务配置顺序文件,0-有，1-无
##servicelist=0

# 是否支持FEA组件,0-无，1-有
##isfeaflag=1
#============== Contents Above Deprecated =============
