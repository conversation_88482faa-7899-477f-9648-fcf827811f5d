engine.local.ip=localhost

parse.regex.format.dir=${APP_CONF:c:/conf}/pattern
parse.regex.engine.primary=java
parse.regex.engine.secondary=

engine.collect.job.pool=com.venustech.taihe.pangu.thbrain.lib.core.schedule.JobThreadPool

engine.collect.almond.home=${ALMOND_HOME:c:/aiss/almond}
engine.collect.almond.url=http://localhost:18720/api
engine.collect.almond.workspace=${ALMOND_HOME:c:/aiss/almond}/workspace
engine.collect.almond.input.endpoint=tcp://127.0.0.1:18820
engine.collect.almond.output.endpoint=tcp://127.0.0.1:18820
engine.collect.almond.endpoint.type=omq
