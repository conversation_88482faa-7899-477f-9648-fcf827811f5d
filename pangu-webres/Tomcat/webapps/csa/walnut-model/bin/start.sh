#!/bin/bash
export LANG=zh_CN.UTF-8

#应用主目录
export APP_HOME=$(cd `dirname $0`/..; pwd)
export ALMOND_HOME=$(cd $APP_HOME/../almond-model; pwd)
export APP_NAME=walnut-model

#配置文件
export APP_CONF=$APP_HOME/conf
file_path=""
splitStr=","
for file_name in ${APP_CONF}/*.properties;
do
    temp_file=`basename $file_name`
    file_path+=$APP_CONF/$temp_file$splitStr
done
final=${file_path%,*}
export APP_CONF_FILES=$final

#临时目录
export APP_TMP=$APP_HOME/tmp
mkdir -p "$APP_TMP"

#Library
export APP_LIB=$APP_HOME/lib
export APP_LIB_NAME=pangu-thbrain-service-walnut
export APP_LIB_JAR=`ls ${APP_LIB}/${APP_LIB_NAME}-*.jar | tail -n 1`

#日志
export APP_LOG=$APP_HOME/log

#JAVA
export JAVA=$APP_HOME/jre/bin/java

#启动服务
$JAVA -Xmx12g -jar -Dspring.config.location=$APP_CONF_FILES -Djava.io.tmpdir="$APP_TMP" "$APP_LIB_JAR" 1>"$APP_LOG"/start.log 2>&1 &
