# coding=utf-8
import importlib
import importlib.util
import json
import os
import sys

import pandas
from loguru import logger


class PythonExecutor(object):

    def __init__(self, name, home, path, clazz, method, args):
        sys.path.insert(0, home)

        self.clazz = clazz
        self.path_full = home + os.sep + path

        os.chdir(home)
        if self.clazz is None:
            self.module = self.import_module_from_path(name, self.path_full)
            self.method = "self.module." + method
        else:
            self.module = __import__(path[0:-3] if path.endswith(".py") else path)
            self.instance = getattr(self.module, clazz)(*args)
            self.method = getattr(self.instance, method)

    def call(self, *args):
        if self.clazz is None:
            return eval(self.method)(*args)
        else:
            return self.method(*args)

    @staticmethod
    def import_module_from_path(name, path):
        module_spec = importlib.util.spec_from_file_location(name, path)
        module = importlib.util.module_from_spec(module_spec)
        module_spec.loader.exec_module(module)
        return module


if __name__ == "__main__":

    # 参数解析
    task_param_file = sys.argv[1]
    print(sys.argv)

    # 使用json解析参数
    with open(task_param_file, encoding="utf-8") as f:
        task_param = json.load(f, encoding="utf-8")

    # 日志
    log_file = task_param["logPath"]
    logger.remove()
    logger.add(log_file, format="{time} {level} {message}", backtrace=True, diagnose=True,
               level="DEBUG", rotation="100 MB", encoding="utf-8")

    # 预处理后的数据集
    dataset_dir = task_param["proceedDir"]
    logger.info("dataset dir is: {}", dataset_dir)
    dataset_train_proceed_dir = dataset_dir + os.sep + "trainProceed"
    dataset_test_proceed_dir = dataset_dir + os.sep + "testProceed"
    logger.info("train_proceed_dataset_dir is: {}", dataset_train_proceed_dir)
    logger.info("test_proceed_dataset_dir is: {}", dataset_test_proceed_dir)

    # 数据集目录（模型自带数据集、原始测试集、每次的训练集文件夹）
    dataset_workspace = task_param["datasetDir"]
    logger.info("dataset workspace is: {}", dataset_workspace)

    dataset_model = dataset_workspace + os.sep + "originDataset"
    dataset_test_dir = dataset_workspace + os.sep + "testDataset"
    dataset_train_dir = dataset_workspace + os.sep + "trainDataset"
    logger.info("dataset_test_dir is: {}", dataset_test_dir)

    # workspace
    workspace = task_param['executeDir']
    logger.info("workspace is: {}", workspace)

    # 训练集定义
    train_set_define = {'useTrainData': task_param['useTrainData'],
                        'trainDataRatio': task_param['trainDataRatio'],
                        'trainDataDistribution': task_param['trainDataDistribution']}
    logger.info("train_set_define is: {}", train_set_define)

    # 智能标注标记
    is_modelops = task_param.get("isModelops") if task_param.get("isModelops") is not None else False
    logger.info("is_modelops is: {}", is_modelops)

    # 计算图定义
    with open(workspace + os.sep + "computation_graph.json", encoding="utf-8") as f:
        graph_define = json.load(f, encoding="utf-8")
    logger.info("graph define is: {}", json.dumps(graph_define))

    try:#加载测试集数据 - 用于后续划分训练集
        dataset_test_all = None
        for filename in os.listdir(dataset_test_proceed_dir):
            dataset_file = os.path.join(dataset_test_proceed_dir, filename)

            if os.path.isfile(dataset_file):
                logger.info("loading dataset: {}", dataset_file)
                dataset = pandas.read_csv(dataset_file)
                if dataset_test_all is None:
                    dataset_test_all = dataset
                else:
                    dataset_test_all = pandas.concat([dataset_test_all, dataset], ignore_index=True)
        logger.info("test_data_set:{}",dataset_test_all)
        logger.info("initializing graph executor.")

        if is_modelops:
            graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                            "ComputationGraph", "modelops", [graph_define, True])
        else:
            # 训练执行
            graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                            "ComputationGraph", "modelops", [graph_define, True])
        logger.info("start of executing graph. ")
        graph_executor.call(dataset_train_proceed_dir,dataset_train_dir,dataset_test_all, train_set_define)
        logger.info("end of executing graph.")

    except Exception as e:
        logger.exception(e)
        exit(1)

    exit(0)
