"""
计算图
根据配置对输入的数据调用特征工程算子和算法算子
分训练和推理两个方法
训练时输入数据为dataframe，多条记录
推理时输入数据为list, 只包含单条记录
"""
import copy
import importlib
import json
import math
import os
import re
import shutil
import sys
import time
import traceback

import joblib
import pandas
import pandas as pd
from loguru import logger
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn import metrics

LABEL_CATEGORY = "label.category"


def make_model_file_dir(operator_type, operator_id):
    model_dir = os.path.join("./model", operator_type, str(operator_id))
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    return model_dir


def make_model_file_path(operator_type, operator_id, operator_symbol, model_index):
    model_dir = make_model_file_dir(operator_type, operator_id)
    return os.path.join(model_dir, operator_symbol + "_" + str(model_index))


def save_operator_model(operator_id, operator_symbol, operator_type, models, operator_id_count=None):
    if models is not None:
        if isinstance(models, tuple):
            for i, model in enumerate(models):
                model_path = make_model_file_path(operator_type, str(operator_id), operator_symbol,
                                                  left_pad(str(i + 1), '0', 4))
                try:
                    # 默认 tensorflow 和 pytorch 来自不同依赖库
                    import tensorflow as tf
                    tf_model_path = model_path + ".h5"
                    model.save(tf_model_path)
                    logger.info("tensorflow model saved: {}", model_path)
                except Exception:
                    try:
                        import torch
                        torch_model_path = model_path + ".pt"
                        torch.save(model, torch_model_path)
                        logger.info("pytorch model saved:{}", model_path)
                    except Exception:
                        joblib.dump(model, model_path)
                        logger.info("joblib model saved: {}", model_path)
        else:
            model_path = make_model_file_path(operator_type, str(operator_id), operator_symbol, operator_id_count)
            try:
                import tensorflow as tf
                model_path = model_path + ".h5"
                models.save(model_path)
                logger.info("tensorflow model saved: {}", model_path)
            except Exception:
                logger.warning("failed to save as tensorflow model")
                try:
                    import torch
                    model_path = model_path + ".pt"
                    torch.save(models, model_path)
                    logger.info("pytorch model saved:{}", model_path)
                except Exception:
                    logger.warning("failed to save as pytorch model")
                    joblib.dump(models, model_path)
                    joblib.load(model_path)
                    logger.info("joblib model saved: {}", model_path)


def load_operator_models(operator_type, model_file_dir, models):
    for root, dirs, files in os.walk(model_file_dir):
        files.sort()
        for name in files:
            model_path = os.path.join(root, name)
            logger.info("{} operator loading: {}", operator_type, name)
            model = None
            try:
                model = joblib.load(model_path)
                logger.info("loaded as joblib model: {}", model_path)
            except Exception:
                logger.warning("failed to load as joblib model: {}", model_path)
                try:
                    from tensorflow import keras
                    model = keras.models.load_model(model_path)
                    logger.info("loaded as tensorFlow model: {}", model_path)
                except Exception:
                    logger.warning("failed to load as tensorFlow model: {}", model_path)
                    try:
                        import torch
                        model = torch.load(model_path)
                        logger.info("loaded as pytorch model: {}", model_path)
                    except Exception:
                        logger.warning("failed to load as pytorch model: {}", model_path)
                        logger.error("failed to load the model!")
            models.append(model)
    return models


def get_prefix(dimension_type):
    prefix = ''
    millisecond = str(int(round(time.time() * 1000)))  # 毫秒级时间戳
    if dimension_type == 3:
        prefix = 'D_Up_' + millisecond
    if dimension_type == 2:
        prefix = 'D_Dw_' + millisecond
    return prefix


def _name_convert_to_camel(name: str) -> str:
    """下划线转驼峰(小驼峰)"""
    camel = re.sub(r'(_[a-z])', lambda x: x.group(1)[1].upper(), name)
    return camel[0].upper() + camel[1:]


def save_evaluation_output(modelops_result):
    evaluation_output = os.path.join("./", "modelops.json")
    with open(evaluation_output, "w") as f:
        json.dump(modelops_result, f)
    logger.info("modelops results:\n{}", modelops_result)


def left_pad(s: str, p: str, length: int):
    i = copy.deepcopy(s)
    while len(i) < length:
        i = p + i
    return i


def save_train_dataset(last_dataset_dir: str, dataset_train_dir: str, train_use_dataset: pd.DataFrame):
    logger.info("train_use_size:{}", len(train_use_dataset))
    # 加载数据集
    logger.info("last_train_dataset_dir:{}", last_dataset_dir)
    dataset_all = None
    for filename in os.listdir(last_dataset_dir):
        logger.info("filename:{}", filename)
        dataset_file = os.path.join(last_dataset_dir, filename)

        if os.path.isfile(dataset_file):
            logger.info("loading proceed dataset: {}", dataset_file)
            dataset = pandas.read_csv(dataset_file)
            if dataset_all is None:
                dataset_all = dataset
            else:
                dataset_all = pandas.concat([dataset_all, dataset], ignore_index=True)
    dataset_all = pandas.concat([dataset_all, train_use_dataset], ignore_index=True)  # 拼接此次的训练数据集

    # Delete files in last_dataset_dir
    for filename in os.listdir(last_dataset_dir):
        os.remove(os.path.join(last_dataset_dir, filename))
    dataset_all.to_csv(os.path.join(last_dataset_dir, "combined_train_dataset.csv"), index=False)

    # Delete files in dataset_train_dir
    for filename in os.listdir(dataset_train_dir):
        os.remove(os.path.join(dataset_train_dir, filename))

    dataset_all.to_csv(os.path.join(dataset_train_dir, "combined_train_dataset.csv"), index=False)

    return dataset_all


class ComputationGraph:

    def __init__(self, graph_define: dict, predict: bool) -> None:

        self.graph_define = graph_define
        self.graph_id = graph_define["id"]
        self.is_predict = predict

        # 特征工程算子
        self.feature_operators = {}
        feature_operators_counter = 0
        for operator_define in graph_define["featureOperators"]:
            feature_operator = self.create_operator_instance(self.is_predict, "feature",
                                                             operator_define, feature_operators_counter)
            self.feature_operators[
                str(feature_operator["id"]) + '_' + str(feature_operators_counter)] = feature_operator
            feature_operators_counter += 1

        # 对计算图进行编译，确定特征工程算子执行顺序（暂时使用list强制串行执行，后期改造为可并行执行）
        self.feature_operator_route = []
        for node in graph_define["featureNodes"]:
            for operator_id in node:
                self.feature_operator_route.append(operator_id)

        # 算法算子
        self.algorithm_operator = self.create_operator_instance(self.is_predict, "algorithm",
                                                                graph_define["algorithmOperator"])

    def modelops(self, last_train_dataset_dir: str, dataset_train_dir: str, test_dataset: pandas.DataFrame,
                 train_set_define: dict) -> None:

        """
        按计算图中指定的顺序依次调用算子的train方法，将返回的model进行保存，模型训练使用训练集，评价用测试集
        """
        # if not self.is_dataset_sufficient(test_dataset):
        #     raise ValueError("训练集的数据量过少，无法训练模型。请更换数据集后重试。")
        train_not_use_set, train_use_set = self.dataset_split(train_set_define, test_dataset)
        train_all_set = save_train_dataset(last_train_dataset_dir, dataset_train_dir, train_use_set)  # 更新训练集
        logger.info('dataset split with {}', train_set_define)
        logger.info('train_not_use_set size: {}, train_use_set size: {}', len(train_not_use_set), len(train_use_set),
                    len(train_all_set))

        # 特征算子
        feature_operators_counter = 0
        for operator_id in self.feature_operator_route:
            operator = self.feature_operators[str(operator_id) + '_' + str(feature_operators_counter)]
            instance = operator["instance"]
            symbol = operator["symbol"]
            target_symbol = operator["targetSymbol"]
            dimension_type = operator['dimensionType']
            logger.info("executing operator: {} for {}", symbol, target_symbol)

            # 首先判断是否存在逗号, 若存在则表明是对多列数据的操作(主成分分析/决策树/随机森林降维)
            if '*' == target_symbol:
                old_values = train_all_set
            elif ',' in target_symbol:
                target_symbol = target_symbol.replace(' ', '').split(',')
                old_values = train_all_set[target_symbol]
            else:
                # 获取dataset中指定列的值
                old_values = train_all_set[target_symbol].tolist()

            # 调用算子的训练方法进行训练
            models = instance.train(old_values)

            # 保存训练结果模型
            save_operator_model(operator_id, symbol, "feature", models, feature_operators_counter)

            # 调用推理方法获取新值（下一步的算子有可能需要使用）
            if isinstance(models, tuple):
                new_values = instance.predict(old_values, *models)
            else:
                new_values = instance.predict(old_values, models)

            # 把计算过后的值放回
            if dimension_type == 1:  # 普通变换
                if type(new_values) == list:
                    train_all_set[target_symbol] = None if new_values is None else new_values[0]  # list第0个数据
                elif type(new_values) == pandas.DataFrame:
                    columns = new_values.columns.tolist()
                    for c in columns:
                        train_all_set[c] = new_values[c]
            else:  # dimensionType == 3 升维变换, 例如OneHot; dimensionType == 2 降维变换, 例如PCA
                prefix = get_prefix(dimension_type)
                assert type(new_values) is pandas.DataFrame
                columns = new_values.columns
                if type(target_symbol) is list:
                    for symbol in target_symbol:
                        train_all_set = train_all_set.drop(symbol, axis='columns')
                else:
                    train_all_set = train_all_set.drop(target_symbol, axis='columns')
                for col in columns:
                    train_all_set.insert(loc=len(train_all_set.columns),
                                         column=prefix + col,
                                         value=new_values[col])
            feature_operators_counter += 1

        # 算法算子
        instance = self.algorithm_operator["instance"]
        logger.info("training model: {}", self.algorithm_operator["symbol"])

        # 算法算子训练
        dataset_copy = copy.deepcopy(train_all_set)
        models = instance.train(dataset_copy)
        if models is not None:
            # 保存训练结果模型
            save_operator_model(self.algorithm_operator["id"],
                                self.algorithm_operator["symbol"], "algorithm", models, "0001")

            # 算法算子模型评价
            self.evaluate_algorithm_model(instance, models, train_all_set, test_dataset)

    # 先在整个数据集上训练生成模型 ，后在整个数据集上推理得到标签

    def predict(self, data_record: dict) -> int:
        """
        data_record: 数据记录
        """

        # 特征算子
        feature_operators_counter = 0
        for operator_id in self.feature_operator_route:
            operator = self.feature_operators[str(operator_id) + '_' + str(feature_operators_counter)]
            instance = operator["instance"]
            target_symbol = operator["targetSymbol"]
            dimension_type = operator['dimensionType']
            models = None if "models" not in operator else operator["models"]
            feature_operators_counter += 1

            # 首先判断是否存在逗号, 若存在则表明是对多列数据的操作(主成分分析/随机森林降维)
            if '*' == target_symbol:
                old_values = pandas.DataFrame([data_record])
            elif ',' in target_symbol:
                target_symbol = target_symbol.replace(' ', '').split(',')
                pd_data_record = pandas.DataFrame([data_record])
                old_values = pd_data_record[target_symbol]
            else:
                old_values = list((None if target_symbol not in data_record else data_record[target_symbol],))

            # 调用算子的推理方法计算
            new_values = instance.predict(old_values, *models)

            # 把计算过后的值放回
            if dimension_type == 1:  # 普通变换
                if type(new_values) == list:
                    data_record[target_symbol] = None if new_values is None else new_values[0]  # list第0个数据
                elif type(new_values) == pandas.DataFrame:
                    columns = new_values.columns.tolist()
                    for c in columns:
                        data_record[c] = new_values[c]
            else:  # 升维或者降维变换
                prefix = get_prefix(dimension_type)
                assert type(new_values) is pandas.DataFrame
                if type(target_symbol) is list:
                    for symbol in target_symbol:
                        data_record.pop(symbol)
                else:
                    data_record.pop(target_symbol)
                columns = new_values.columns
                for i in range(len(columns)):
                    data_record[prefix + str(columns[i])] = new_values.values[0][i]

        # 算法算子
        instance = self.algorithm_operator["instance"]
        models = self.algorithm_operator["models"]
        return instance.predict(list((data_record,)), *models)

    def evaluate_algorithm_model(self, operator, models, train_all_set, dataset):
        """
        基于测试集进行模型评价
        """

        # 当算子自有evaluate函数时，调用其方法
        # if hasattr(operator, 'evaluate'):
        #     self_evaluation_output = operator.evaluate(dataset, *models)

        if models is None or not self.useTestData:
            return

        # 算法评估: 分类模型, 交叉验证
        if LABEL_CATEGORY not in dataset:
            logger.warning("The dataset does not contain '{}', the model can not be evaluated.", LABEL_CATEGORY)
            return

        evaluate_model = None
        if isinstance(models, tuple):
            for i, model in enumerate(models):
                if hasattr(model, 'fit'):
                    evaluate_model = model
                    break
        else:
            if hasattr(models, 'fit'):
                evaluate_model = models

        if evaluate_model is None:
            logger.warning("The model({}) can not be evaluated.", models)
            return

        x = dataset.drop(LABEL_CATEGORY, axis=1)
        y = dataset[LABEL_CATEGORY]
        evaluation_result = self.evaluation_compute(evaluate_model, x, y, train_all_set)
        save_evaluation_output(evaluation_result)

    def create_operator_instance(self, is_predict: bool, operator_type: str, operator_define: dict,
                                 feature_operators_counter: int = None) -> {}:
        operator_id = operator_define["id"]
        symbol = operator_define["symbol"]
        class_name = operator_define["className"]
        params = operator_define["params"]
        operator = {
            "id": operator_id,
            "symbol": symbol,
            "class_name": class_name,
            "params": params,
            "type": operator_type
        }

        # 根据算子的不同类型设置不同的属性
        if operator_type == "feature":
            operator["dimensionType"] = operator_define["dimensionType"]
            operator["targetSymbol"] = operator_define["targetSymbol"]
        elif operator_type == "algorithm":
            pass
        else:
            raise Exception("not supported operators type: {}".format(operator_type))

        # 根据算子类型和算子名(symbol)创建算子实例
        operator["instance"] = self.create_class_instance(operator_type, operator_id, symbol, class_name, params)

        model_file_dir = make_model_file_dir(operator_type, operator_id)
        if os.path.exists(model_file_dir):
            if is_predict:
                # 推理模式下，加载保存在本地的model
                models = []
                if operator_type == "feature":
                    models = load_operator_models(operator_type, model_file_dir, models)
                    operator["models"] = tuple(models)
                elif operator_type == "algorithm":
                    models = load_operator_models(operator_type, model_file_dir, models)
                    operator["models"] = tuple(models)
                else:
                    raise Exception("not supported operators type: {}".format(operator_type))
            else:
                # 训练模式下，清除旧模型
                shutil.rmtree(model_file_dir)
        return operator

    def create_class_instance(self, operator_type, operator_id, operator_symbol, operator_class, operator_params):
        operator_home = os.path.join(os.path.dirname(__file__), "operators", operator_type, str(operator_id))
        # 把算子所在目录加入sys.path
        sys.path.insert(0, operator_home)
        logger.info("sys.path={}", sys.path)
        # 创建算子实例
        instance = self.create_class_instance_core(operator_symbol, operator_class, operator_params)

        # 恢复sys.path（目的是避免算子之间互相影响)
        sys.path.pop(0)
        return instance

    @staticmethod
    def create_class_instance_core(operator_symbol, operator_class, operator_params):
        """创建算子实例"""
        module = importlib.import_module(operator_symbol)
        clazz = getattr(module, operator_class)
        return clazz(**operator_params)

    def convert_labels(self, y=None):
        y_copy = y.copy()
        for i in range(len(y)):
            if y.iloc[i] == 'benign':
                y_copy.iloc[i] = 0
            if y.iloc[i] == 'malware':
                y_copy.iloc[i] = 1
            else:
                y_copy.iloc[i] = 0
        return y_copy

    # 计算模型评价指标
    def evaluation_compute(self, model, x, y, train_all_set):
        logger.info('model evaluating...')
        y_true = self.convert_labels(y).values.tolist()
        # 转换数据结构
        y_pred = []
        predict_computation_graph = ComputationGraph(self.graph_define, True)
        for i in range(len(x)):
            x_i = dict(zip(x.columns.tolist(), x.iloc[i].tolist()))
            y_pred.append(predict_computation_graph.predict(x_i))  # 存储预测值
        y_pred = pandas.DataFrame(y_pred).values.tolist()
        # 计算指标
        # logger.info("y_pred:{}",y_pred)
        # logger.info("y_true:{}",y_true)
        try:
            tn, fp, fn, tp = metrics.confusion_matrix(y_true, y_pred).ravel()
        except Exception:
            tn, fp, fn, tp = -1, -1, -1, -1
        try:
            accuracy = '%0.6f' % (metrics.accuracy_score(y_true, y_pred))
        except Exception:
            accuracy = -1
        try:
            precision = '%0.6f' % (metrics.precision_score(y_true, y_pred, average='weighted'))
        except Exception:
            precision = -1
        try:
            recall = '%0.6f' % (metrics.recall_score(y_true, y_pred, average='weighted'))
        except Exception:
            recall = -1
        try:
            f1 = '%0.6f' % (metrics.f1_score(y_true, y_pred, average='weighted'))
        except Exception:
            f1 = -1
        try:
            roc_auc = '%0.6f' % (metrics.roc_auc_score(y_true, y_pred))
        except Exception:
            roc_auc = -1
        return {"tn": str(tn), "fp": str(fp), "fn": str(fn), "tp": str(tp),
                "accuracyScore": accuracy,
                "precisionScore": precision,
                "recallScore": recall,
                "f1Score": f1,
                "rocAucScore": roc_auc,
                "trainSetNumber": len(train_all_set),
                "testSetNumber": len(x)}

    @staticmethod
    def evaluate_core(model, x, y, cv, scoring):
        try:
            return cross_val_score(model, x, y, cv=cv, scoring=scoring).mean()
        except Exception as e:
            logger.warning("evaluate error when {} : {}\n{}", scoring, str(e), traceback.format_exc())
            return -1

    def dataset_split(self, test_set_define, dataset):
        self.useTestData = test_set_define['useTrainData']  # 测试集定义
        if not self.useTestData:
            return dataset, []
        data = dataset.copy()
        rows = len(data)
        test_rate = test_set_define['trainDataRatio']  # 百分数的整数部分
        test_distribution = test_set_define['trainDataDistribution']
        split_num = math.floor(rows * test_rate * 0.01)  # 数据个数向下取整

        if test_distribution == 'front':  # 最前
            train_set = data[split_num:]
            test_set = data.head(split_num)
        elif test_distribution == 'last':  # 最后
            train_set = data.head(rows - split_num)
            test_set = data.tail(split_num)
        elif test_distribution == 'uniform':  # 均匀分布
            if split_num == 1:
                raise ValueError('由于数据量不足,无法划分测试集。请更换数据集或调整测试集设置后重试。')
            step = int(rows / (split_num - 1))
            test_set = data[::step]
            train_set = data.loc[data.index.difference(test_set.index)].copy()
            if len(test_set) != split_num:  # 如不均匀则随机抽取
                train_set, test_set = train_test_split(data, test_size=split_num, random_state=8)
        elif test_distribution == 'random':  # 随机
            train_set, test_set = train_test_split(data, test_size=split_num, random_state=8)
        else:
            train_set, test_set = None, None
        return train_set, test_set

    @staticmethod
    def is_dataset_sufficient(dataset):
        if dataset.shape[0] < 24:
            return False
        return True


if __name__ == '__main__':
    pandas.set_option('display.max_columns', None)
    pandas.set_option('display.width', 4196)
    graph_define = {
        "algorithmOperator": {
            "className": "MalwareClassificationCnn",
            "id": 19,
            "params": {},
            "symbol": "malware_classification_cnn"},
        "featureNodes": [[]],
        "featureOperators": [],
        "id": 100281
    }
    # graph_define = {
    #     "algorithmOperator": {
    #         "className": "Webshell",
    #         "id": 1,
    #         "params": {"n_estimators": 110, "min_samples_leaf": 4,
    #                    "max_depth": 14, "n_jobs": 1, "random_state": 250}, "symbol": "webshell"},
    #     "featureNodes": [[11, 11]],
    #     "featureOperators": [
    #         {"className": "Standardization",
    #          "dimensionType": 1,
    #          "id": 11,
    #          "params": {"with_mean": True, "with_std": True},
    #          "symbol": "standardization",
    #          "targetSymbol": "sTtl"},
    #         {"className": "Standardization",
    #          "dimensionType": 1,
    #          "id": 11,
    #          "params": {"with_mean": True, "with_std": True},
    #          "symbol": "standardization",
    #          "targetSymbol": "dTtl"}],
    #     "id": 100281}

    # 训练
    graph_train = ComputationGraph(graph_define, False)
    # dataset_train = pandas.read_csv("./test/iris_numerical.csv")
    # dataset_train = pandas.read_csv("operators/algorithm/18/data/proceed.csv")
    dataset_train = pandas.read_csv("operators/algorithm/19/data/train_dataset_simple.csv")
    # dataset_train = pandas.read_csv("./operators/algorithm/3/data/raw/dga_simple_test.csv")
    # dataset_train[LABEL_CATEGORY] = \
    #     [np.random.choice([0, 1], p=[0.64, 0.36]) for id in dataset_train.id]

    # from numpy import random
    # f = lambda s: random.randint(2) if s is None else random.randint(2)
    # dataset_train["features.label"] = dataset_train.apply(f, axis=1)

    test_define = {'useTestData': False, 'testDataRatio': 10, 'testDataDistribution': 'uniform'}
    graph_train.train(dataset_train, test_define)
    print(dataset_train)

    # 推理
    # TODO 推理时，对于单条记录，直接构建dataframe快还是构建dict快？
    # dataset_predict = pandas.read_csv("./test/iris.csv")
    # dataset_predict = pandas.read_csv("./operators/algorithm/1/data/raw/dataset_1_download.csv")
    # # dataset_predict = pandas.read_csv("./test/aiflow_features.csv")
    # data_record_predict = dict(zip(
    #     dataset_train.columns.tolist(),
    #     dataset_train.loc[0].tolist()
    # ))
    # data_record_predict = {"index": 1, "time": 1636470000000, "value": 15000}
    # print(data_record_predict)
    # data_record_predict = {
    #         "srcip": "**************",
    #         "dstip": "**************",
    #         "timestamp": 1675267200000,
    #         "count": 200,
    #     }
    #  TODO 测试用例暂存：以下是dga_request_model.py的测试用例。
    # data_record_predict = {
    #     "TID": "0xAAA8",
    #     "ST": "2021-09-03 15:33:07 283",
    #     "SX": 73.0,
    #     "DN": "ccgslb.com.cn",
    #     "TYP": "A",
    #     "DU": 0.0,
    #     "RID": 0.0,
    #     "RX": 89.0,
    #     "ANUM": 1.0,
    #     "ADDI": "",
    # }
    # dataset_test = pandas.read_csv("operators/algorithm/18/data/CQ_black_process.csv")
    data_record_predict = dict(zip(
        dataset_train.columns.tolist(),
        dataset_train.loc[0].tolist()
    ))
    graph_predict = ComputationGraph(graph_define, True)
    cls = graph_predict.predict(data_record_predict)
    print(cls)

    # for i in range(dataset_test.shape[0]):
    #     print(i)
    #     data_record_predict = dict(zip(
    #         dataset_test.columns.tolist(),
    #         dataset_test.loc[i].tolist()
    #     ))
    #     graph_predict = ComputationGraph(graph_define, True)
    #     cls = graph_predict.predict(data_record_predict)
    #     print(cls)

    # graph_predict.evaluate(dataset_train, test_define)
