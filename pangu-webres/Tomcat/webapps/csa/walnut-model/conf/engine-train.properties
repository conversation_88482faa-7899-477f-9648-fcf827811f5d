engine.train.bin.dir=${APP_HOME:c:/aiss}/train
engine.train.workspace.dir=${APP_HOME:c:/aiss}/train/workspace
engine.train.graph.executor=${APP_HOME:c:/aiss}/train/computation_graph.py

engine.train.almond.home=${ALMOND_HOME:c:/aiss/almond}
engine.train.almond.url=http://localhost:18730/api
engine.train.almond.workspace=${ALMOND_HOME:c:/aiss/almond}/workspace
engine.train.almond.input.endpoint=tcp://127.0.0.1:18830
engine.train.almond.output.endpoint=tcp://127.0.0.1:18830
engine.train.almond.endpoint.type=omq

engine.train.job.pool=com.venustech.taihe.pangu.thbrain.lib.core.schedule.JobThreadPool
