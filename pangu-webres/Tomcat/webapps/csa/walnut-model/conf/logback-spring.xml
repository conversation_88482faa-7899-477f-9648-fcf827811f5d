<?xml version="1.0" encoding="UTF-8"?>
<!--
    Copyright 2010-2011 The myBatis Team
    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at
        http://www.apache.org/licenses/LICENSE-2.0
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<configuration debug="false">
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <property name="LOG_HOME" value="${APP_LOG}"/>
    <!--    <property name="logstash.host" value="cockpit-logstash"/>-->
    <!--    <property name="logstash.port" value="19800"/>-->

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    <!--    &lt;!&ndash; 按照每天生成日志文件 &ndash;&gt;-->
    <!--    <appender name="FILE" class="ch.qos.logback.classic.sift.SiftingAppender">-->
    <!--        <discriminator>-->
    <!--            <key>logName</key>-->
    <!--            <defaultValue>${APP_NAME}</defaultValue>-->
    <!--        </discriminator>-->
    <!--        <sift>-->
    <!--            <appender name="FILE-${logName}" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--                <file>${LOG_HOME}/${logName}.log</file>-->
    <!--                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">-->
    <!--                    <fileNamePattern>${LOG_HOME}/${logName}.%i.zip</fileNamePattern>-->
    <!--                    <minIndex>1</minIndex>-->
    <!--                    <maxIndex>20</maxIndex>-->
    <!--                </rollingPolicy>-->
    <!--                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
    <!--                    <maxFileSize>100MB</maxFileSize>-->
    <!--                </triggeringPolicy>-->
    <!--                <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">-->
    <!--                    <Pattern>-->
    <!--                        <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
    <!--                    </Pattern>-->
    <!--                </encoder>-->
    <!--            </appender>-->
    <!--        </sift>-->
    <!--    </appender>-->

    <!--    <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">-->
    <!--        <destination>${logstash.host}:${logstash.port}</destination>-->
    <!--        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">-->
    <!--            <providers>-->
    <!--                <pattern>-->
    <!--                    <pattern>-->
    <!--                        {-->
    <!--                        &lt;!&ndash;服务名 &ndash;&gt;-->
    <!--                        "service": "${APP_NAME}",-->
    <!--                        &lt;!&ndash;打印时间 &ndash;&gt;-->
    <!--                        "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",-->
    <!--                        &lt;!&ndash;线程名称 &ndash;&gt;-->
    <!--                        "thread": "%thread",-->
    <!--                        &lt;!&ndash;日志级别 &ndash;&gt;-->
    <!--                        "level": "%level",-->
    <!--                        &lt;!&ndash;日志名称 &ndash;&gt;-->
    <!--                        "logger_name": "%logger{50}",-->
    <!--                        &lt;!&ndash;日志信息 &ndash;&gt;-->
    <!--                        "message": "%msg",-->
    <!--                        &lt;!&ndash;日志堆栈 &ndash;&gt;-->
    <!--                        "stack_trace": "%exception"-->
    <!--                        }-->
    <!--                    </pattern>-->
    <!--                </pattern>-->
    <!--            </providers>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <appender name="FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <!--discriminator鉴别器，设置运行时动态属性,siftingAppender根据这个属性来输出日志到不同文件 -->
        <discriminator>
            <key>JobId</key>
            <defaultValue>main</defaultValue>
        </discriminator>
        <sift>
            <!--具体的写日志appender，每一个JobId创建一个文件-->
            <appender name="FILE-${JobId}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <append>true</append>
                <encoder charset="UTF-8">
                    <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] %-5level - %msg%n</pattern>
                </encoder>

                <file>${LOG_HOME}/${JobId}/${JobId}.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${LOG_HOME}/${JobId}.%i.zip</fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>20</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>

                <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                    <level>DEBUG</level>
                </filter>
            </appender>
        </sift>
    </appender>

    <!-- 本系统输出 -->
    <logger name="com.venustech" level="INFO"/>

    <!-- SQL文输出 (要关闭时需设为INFO以上级别-->
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.elasticsearch.client.RestClient" level="ERROR"/>
    <logger name="jdbc" level="ERROR"/>
    <logger name="jdbc.sqltiming" level="INFO"/>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <!--<appender-ref ref="STDOUT" />-->
        <appender-ref ref="FILE"/>
        <!--        <appender-ref ref="LOGSTASH" />-->
    </root>
</configuration>
