#!/bin/bash

CURRENT=$(cd `dirname $0`/; pwd)
PARAM_FILE=$1
#PYENV=$2

# python运行环境
export PYTHON_EXE=$PYTHON
echo "python=$PYTHON"

if [ $2 == "sk-learn" ]; then
    export PYTHONPATH=$THBRAIN_AI_ALGO_DEPS:$THBRAIN_AI_SVC_DEPS
    RUBBISH=$THBRAIN_AI_ALGO_DEPS/dataclasses.py
elif [ $2 == "pytorch" ]; then
    export PYTHONPATH=$THBRAIN_AI_ALGO_PYTORCH_DEPS:$THBRAIN_AI_SVC_DEPS
    RUBBISH=$THBRAIN_AI_ALGO_PYTORCH_DEPS/dataclasses.py
elif [ $2 == "Tensorflow" ]; then
    export PYTHONPATH=$THBRAIN_AI_ALGO_TENSORFLOW_DEPS:$THBRAIN_AI_SVC_DEPS
    RUBBISH=$THBRAIN_AI_ALGO_TENSORFLOW_DEPS/dataclasses.py
else
    export PYTHONPATH=$THBRAIN_AI_ALGO_DEPS:$THBRAIN_AI_SVC_DEPS
    RUBBISH=$THBRAIN_AI_ALGO_DEPS/dataclasses.py
fi

if [ -f $RUBBISH ];then
  rm -rf $RUBBISH
fi

$PYTHON_EXE $CURRENT/experiment-executor.py "$PARAM_FILE"
exit $?

v3
#ai = $THBRAIN_AI_ALGO_DEPS
#ai-pytorch = $THBRAIN_AI_ALGO_PYTORCH_DEPS
#ai-tensorflow = $THBRAIN_AI_ALGO_TENSORFLOW_DEPS