# coding=utf-8
import importlib
import importlib.util
import json
import os
import sys

import pandas
from loguru import logger


class PythonExecutor(object):

    def __init__(self, name, home, path, clazz, method, args):
        sys.path.insert(0, home)

        self.clazz = clazz
        self.path_full = home + os.sep + path

        os.chdir(home)
        if self.clazz is None:
            self.module = self.import_module_from_path(name, self.path_full)
            self.method = "self.module." + method
        else:
            self.module = __import__(path[0:-3] if path.endswith(".py") else path)
            self.instance = getattr(self.module, clazz)(*args)
            self.method = getattr(self.instance, method)

    def call(self, *args):
        if self.clazz is None:
            return eval(self.method)(*args)
        else:
            return self.method(*args)

    @staticmethod
    def import_module_from_path(name, path):
        module_spec = importlib.util.spec_from_file_location(name, path)
        module = importlib.util.module_from_spec(module_spec)
        module_spec.loader.exec_module(module)
        return module


if __name__ == "__main__":

    # 参数解析
    task_param_file = sys.argv[1]
    print(sys.argv)

    # 使用json解析参数
    with open(task_param_file, encoding="utf-8") as f:
        task_param = json.load(f, encoding="utf-8")

    # 日志
    log_file = task_param["logPath"]
    logger.remove()
    logger.add(log_file, format="{time} {level} {message}", backtrace=True, diagnose=True,
               level="DEBUG", rotation="100 MB", encoding="utf-8")

    # 原始数据集
    origin_dataset_dir = task_param["datasetDir"]
    logger.info("origin_dataset dir is: {}", origin_dataset_dir)

    # 预处理数据集
    dataset_dir = task_param["proceedDir"]
    logger.info("dataset dir is: {}", dataset_dir)

    # 人工标签文件
    manual_dir = task_param["manualWorkspace"] + os.sep + "manualLabel"
    logger.info("manual dir is: {}", manual_dir)

    # workspace
    workspace = task_param['executeDir']
    logger.info("workspace is: {}", workspace)

    run_Type = task_param['runType']
    logger.info("run_Type is: {}", run_Type)

    # 测试集定义
    test_set_define = {'useTestData': task_param['useTestData'],
                       'testDataRatio': task_param['testDataRatio'],
                       'testDataDistribution': task_param['testDataDistribution']}
    logger.info("test_set_define is: {}", test_set_define)

    # 智能标注标记
    # is_label = task_param.get("isLabel") if task_param.get("isLabel") is not None else False
    # logger.info("is_label is: {}", is_label)

    # 迭代次数
    if task_param.get("iterationNumber") is not None:
        iter_num = task_param['iterationNumber']
    else:
        iter_num = 0
    # 计算图定义
    with open(workspace + os.sep + "computation_graph.json", encoding="utf-8") as f:
        graph_define = json.load(f, encoding="utf-8")
    logger.info("graph define is: {}", json.dumps(graph_define))

    try:
        # 加载原始数据集
        origin_dataset = None
        for filename in os.listdir(origin_dataset_dir):
            origin_dataset_file = os.path.join(origin_dataset_dir, filename)
            if os.path.isfile(origin_dataset_file):
                logger.info("loading origin dataset: {}", origin_dataset_file)
                ori_dataset = pandas.read_csv(origin_dataset_file)
                if origin_dataset is None:
                    origin_dataset = ori_dataset
                else:
                    origin_dataset = pandas.concat([origin_dataset, ori_dataset], ignore_index=True)

        # 加载数据集
        dataset_all = None
        for filename in os.listdir(dataset_dir):
            dataset_file = os.path.join(dataset_dir, filename)

            if os.path.isfile(dataset_file):
                logger.info("loading dataset: {}", dataset_file)
                dataset = pandas.read_csv(dataset_file, low_memory=False)
                if dataset_all is None:
                    dataset_all = dataset
                else:
                    dataset_all = pandas.concat([dataset_all, dataset], ignore_index=True)

        if task_param['runType'] == 2 or task_param['runType'] == 4: #非首次运行 拿人工标记的训练集
            # 加载人工标签文件
            manual_all = None
            for filename in os.listdir(manual_dir):
                manual_file = os.path.join(manual_dir, filename)

                if os.path.isfile(manual_file):
                    logger.info("loading manual dataset: {}", manual_file)
                    manual_dataset = pandas.read_csv(manual_file, low_memory=False)
                    if manual_all is None:
                        manual_all = manual_dataset
                    else:
                        manual_all = pandas.concat([manual_all, manual_dataset], ignore_index=True)

        logger.info("initializing graph executor.")

        if task_param['runType'] == 1: # 新建模型首次运行-->100%训练集训练
            graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                            "ComputationGraph", "label_train", [graph_define, True])
            logger.info("start of executing graph. ")
            graph_executor.call(dataset_all, test_set_define, origin_dataset)
            logger.info("end of executing graph.")
        elif task_param['runType'] == 3: #导入模型首次运行
            graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                            "ComputationGraph", "label_evaluate", [graph_define, True])
            logger.info("start of executing graph. ")
            graph_executor.call(dataset_all, test_set_define, origin_dataset)
            logger.info("end of executing graph.")
        else: #新建、导入非首次运行
            graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                            "ComputationGraph", "iter_train", [graph_define, True])
            logger.info("start of executing graph. ")
            graph_executor.call(dataset_all, origin_dataset, manual_all)  # 预处理后的数据集、原始数据集、人工标签数据
            logger.info("end of executing graph.")
        '''
        if iter_num == 0:  # 首次运行
            if task_param['useTestData'] == False:  # 新建模型-->100%训练集训练 原始数据集测试
                graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                                "ComputationGraph", "label_train", [graph_define, True])
                logger.info("start of executing graph. ")
                graph_executor.call(dataset_all, test_set_define, origin_dataset)
                logger.info("end of executing graph.")
            else:  # 选择已有模型 100%测试集训练
                graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                                "ComputationGraph", "label_evaluate", [graph_define, True])
                logger.info("start of executing graph. ")
                graph_executor.call(dataset_all, test_set_define, origin_dataset)
                logger.info("end of executing graph.")
        else:  # 再次运行

            graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
                                            "ComputationGraph", "iter_train", [graph_define, True])
            logger.info("start of executing graph. ")
            graph_executor.call(dataset_all, origin_dataset, manual_all)  # 预处理后的数据集、原始数据集、人工标签数据
            logger.info("end of executing graph.")
        # else:
        #     # 训练执行
        #     graph_executor = PythonExecutor("graph-executor", workspace, "computation_graph.py",
        #                                     "ComputationGraph", "train", [graph_define, False])
        #     logger.info("start of executing graph. ")
        #     graph_executor.call(dataset_all, test_set_define)
        #     logger.info("end of executing graph.")
    '''
    except Exception as e:
        logger.exception(e)
        exit(1)

    exit(0)
